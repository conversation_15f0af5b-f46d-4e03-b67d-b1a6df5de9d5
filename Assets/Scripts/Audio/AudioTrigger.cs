using UnityEngine;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Audio
{
    /// <summary>
    /// 音频触发器组件
    /// 用于在特定事件发生时触发音频播放
    /// </summary>
    public class AudioTrigger : MonoBehaviour
    {
        [System.Serializable]
        public class AudioTriggerEvent
        {
            [Header("触发设置")]
            public TriggerType triggerType = TriggerType.OnStart;
            public string audioName;
            
            [Header("播放设置")]
            [Range(0f, 1f)] public float volume = 1f;
            [Range(0.1f, 3f)] public float pitch = 1f;
            [Range(0f, 5f)] public float delay = 0f;
            
            [Header("随机化")]
            public bool randomizeVolume = false;
            [Range(0f, 0.5f)] public float volumeVariation = 0.1f;
            
            public bool randomizePitch = false;
            [Range(0f, 0.5f)] public float pitchVariation = 0.1f;
            
            [Header("条件设置")]
            public bool enableCooldown = false;
            [Range(0f, 10f)] public float cooldownTime = 1f;
            
            [Header("3D音频设置")]
            public bool enable3D = false;
            [Range(0f, 100f)] public float maxDistance = 50f;
            [Range(0f, 1f)] public float spatialBlend = 1f;
            
            // 运行时数据
            [System.NonSerialized] private float lastPlayTime;
            
            /// <summary>
            /// 检查是否可以播放
            /// </summary>
            public bool CanPlay()
            {
                if (enableCooldown && Time.time - lastPlayTime < cooldownTime)
                {
                    return false;
                }
                return true;
            }
            
            /// <summary>
            /// 记录播放时间
            /// </summary>
            public void RecordPlay()
            {
                lastPlayTime = Time.time;
            }
            
            /// <summary>
            /// 获取随机化后的音量
            /// </summary>
            public float GetRandomizedVolume()
            {
                if (randomizeVolume)
                {
                    return volume + Random.Range(-volumeVariation, volumeVariation);
                }
                return volume;
            }
            
            /// <summary>
            /// 获取随机化后的音调
            /// </summary>
            public float GetRandomizedPitch()
            {
                if (randomizePitch)
                {
                    return pitch + Random.Range(-pitchVariation, pitchVariation);
                }
                return pitch;
            }
        }
        
        public enum TriggerType
        {
            OnStart,
            OnEnable,
            OnDisable,
            OnDestroy,
            OnTriggerEnter,
            OnTriggerExit,
            OnCollisionEnter,
            OnCollisionExit,
            OnMouseDown,
            OnMouseUp,
            Manual
        }
        
        [Header("音频触发事件")]
        [SerializeField] private AudioTriggerEvent[] audioEvents;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private AudioSource audioSource;
        private IAudioManager audioManager;
        
        private void Awake()
        {
            InitializeAudioTrigger();
        }
        
        private void Start()
        {
            TriggerAudioEvents(TriggerType.OnStart);
        }
        
        private void OnEnable()
        {
            TriggerAudioEvents(TriggerType.OnEnable);
        }
        
        private void OnDisable()
        {
            TriggerAudioEvents(TriggerType.OnDisable);
        }
        
        private void OnDestroy()
        {
            TriggerAudioEvents(TriggerType.OnDestroy);
        }
        
        private void OnTriggerEnter(Collider other)
        {
            TriggerAudioEvents(TriggerType.OnTriggerEnter);
        }
        
        private void OnTriggerEnter2D(Collider2D other)
        {
            TriggerAudioEvents(TriggerType.OnTriggerEnter);
        }
        
        private void OnTriggerExit(Collider other)
        {
            TriggerAudioEvents(TriggerType.OnTriggerExit);
        }
        
        private void OnTriggerExit2D(Collider2D other)
        {
            TriggerAudioEvents(TriggerType.OnTriggerExit);
        }
        
        private void OnCollisionEnter(Collision collision)
        {
            TriggerAudioEvents(TriggerType.OnCollisionEnter);
        }
        
        private void OnCollisionEnter2D(Collision2D collision)
        {
            TriggerAudioEvents(TriggerType.OnCollisionEnter);
        }
        
        private void OnCollisionExit(Collision collision)
        {
            TriggerAudioEvents(TriggerType.OnCollisionExit);
        }
        
        private void OnCollisionExit2D(Collision2D collision)
        {
            TriggerAudioEvents(TriggerType.OnCollisionExit);
        }
        
        private void OnMouseDown()
        {
            TriggerAudioEvents(TriggerType.OnMouseDown);
        }
        
        private void OnMouseUp()
        {
            TriggerAudioEvents(TriggerType.OnMouseUp);
        }
        
        /// <summary>
        /// 初始化音频触发器
        /// </summary>
        private void InitializeAudioTrigger()
        {
            // 获取或创建AudioSource组件
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }
            
            // 获取音频管理器
            if (GameManager.Instance != null)
            {
                audioManager = GameManager.Instance.GetAudioManager();
            }
            
            if (audioManager == null)
            {
                audioManager = AudioManager.Instance;
            }
        }
        
        /// <summary>
        /// 触发指定类型的音频事件
        /// </summary>
        private void TriggerAudioEvents(TriggerType triggerType)
        {
            if (audioEvents == null) return;
            
            foreach (var audioEvent in audioEvents)
            {
                if (audioEvent.triggerType == triggerType && audioEvent.CanPlay())
                {
                    if (audioEvent.delay > 0)
                    {
                        StartCoroutine(PlayDelayedAudio(audioEvent));
                    }
                    else
                    {
                        PlayAudioEvent(audioEvent);
                    }
                }
            }
        }
        
        /// <summary>
        /// 播放音频事件
        /// </summary>
        private void PlayAudioEvent(AudioTriggerEvent audioEvent)
        {
            if (string.IsNullOrEmpty(audioEvent.audioName)) return;
            
            if (audioEvent.enable3D)
            {
                Play3DAudio(audioEvent);
            }
            else
            {
                Play2DAudio(audioEvent);
            }
            
            audioEvent.RecordPlay();
            
            if (showDebugInfo)
            {
                Debug.Log($"AudioTrigger: 播放音频 {audioEvent.audioName} (触发类型: {audioEvent.triggerType})");
            }
        }
        
        /// <summary>
        /// 播放2D音频
        /// </summary>
        private void Play2DAudio(AudioTriggerEvent audioEvent)
        {
            if (audioManager != null)
            {
                audioManager.PlaySoundEffect(audioEvent.audioName);
            }
        }
        
        /// <summary>
        /// 播放3D音频
        /// </summary>
        private void Play3DAudio(AudioTriggerEvent audioEvent)
        {
            if (audioSource != null)
            {
                // 配置3D音频设置
                audioSource.spatialBlend = audioEvent.spatialBlend;
                audioSource.maxDistance = audioEvent.maxDistance;
                audioSource.volume = audioEvent.GetRandomizedVolume();
                audioSource.pitch = audioEvent.GetRandomizedPitch();
                
                // 从音频库获取AudioClip
                AudioClip clip = GetAudioClip(audioEvent.audioName);
                if (clip != null)
                {
                    audioSource.clip = clip;
                    audioSource.Play();
                }
            }
        }
        
        /// <summary>
        /// 从音频库获取AudioClip
        /// </summary>
        private AudioClip GetAudioClip(string audioName)
        {
            // 这里应该从AudioClipLibrary获取AudioClip
            // 由于AudioManager可能没有直接暴露AudioClipLibrary，
            // 我们可以通过其他方式获取，或者扩展AudioManager接口
            
            // 临时实现：返回null，实际项目中需要实现
            return null;
        }
        
        /// <summary>
        /// 延迟播放音频
        /// </summary>
        private System.Collections.IEnumerator PlayDelayedAudio(AudioTriggerEvent audioEvent)
        {
            yield return new WaitForSeconds(audioEvent.delay);
            PlayAudioEvent(audioEvent);
        }
        
        /// <summary>
        /// 手动触发音频事件
        /// </summary>
        public void TriggerManualAudio()
        {
            TriggerAudioEvents(TriggerType.Manual);
        }
        
        /// <summary>
        /// 手动播放指定音频
        /// </summary>
        public void PlayAudio(string audioName)
        {
            if (audioManager != null)
            {
                audioManager.PlaySoundEffect(audioName);
            }
        }
        
        /// <summary>
        /// 停止所有音频
        /// </summary>
        public void StopAllAudio()
        {
            if (audioSource != null && audioSource.isPlaying)
            {
                audioSource.Stop();
            }
        }
        
        /// <summary>
        /// 添加音频事件
        /// </summary>
        public void AddAudioEvent(AudioTriggerEvent audioEvent)
        {
            if (audioEvents == null)
            {
                audioEvents = new AudioTriggerEvent[1];
                audioEvents[0] = audioEvent;
            }
            else
            {
                AudioTriggerEvent[] newArray = new AudioTriggerEvent[audioEvents.Length + 1];
                System.Array.Copy(audioEvents, newArray, audioEvents.Length);
                newArray[audioEvents.Length] = audioEvent;
                audioEvents = newArray;
            }
        }
        
        /// <summary>
        /// 移除音频事件
        /// </summary>
        public bool RemoveAudioEvent(string audioName)
        {
            if (audioEvents == null) return false;
            
            for (int i = 0; i < audioEvents.Length; i++)
            {
                if (audioEvents[i].audioName == audioName)
                {
                    AudioTriggerEvent[] newArray = new AudioTriggerEvent[audioEvents.Length - 1];
                    System.Array.Copy(audioEvents, 0, newArray, 0, i);
                    System.Array.Copy(audioEvents, i + 1, newArray, i, audioEvents.Length - i - 1);
                    audioEvents = newArray;
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取音频事件数量
        /// </summary>
        public int GetAudioEventCount()
        {
            return audioEvents?.Length ?? 0;
        }
        
        /// <summary>
        /// 启用/禁用调试信息
        /// </summary>
        public void SetDebugMode(bool enabled)
        {
            showDebugInfo = enabled;
        }
    }
}
