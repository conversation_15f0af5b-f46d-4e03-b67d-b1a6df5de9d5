using UnityEngine;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Audio
{
    /// <summary>
    /// 游戏音频配置
    /// 定义游戏中所有音频事件的映射
    /// </summary>
    [CreateAssetMenu(fileName = "GameAudioConfig", menuName = "Mobile Scrolling Game/Audio/Game Audio Config")]
    public class GameAudioConfig : AudioEventConfig
    {
        [Header("游戏状态音效")]
        [SerializeField] private string gameStartSound = "GameStart";
        [SerializeField] private string gamePauseSound = "GamePause";
        [SerializeField] private string gameResumeSound = "GameResume";
        [SerializeField] private string gameOverSound = "GameOver";
        
        [Header("UI音效")]
        [SerializeField] private string buttonClickSound = "ButtonClick";
        [SerializeField] private string buttonHoverSound = "ButtonHover";
        [SerializeField] private string menuOpenSound = "MenuOpen";
        [SerializeField] private string menuCloseSound = "MenuClose";
        
        [Header("玩家音效")]
        [SerializeField] private string playerJumpSound = "PlayerJump";
        [SerializeField] private string playerLandSound = "PlayerLand";
        [SerializeField] private string playerHurtSound = "PlayerHurt";
        [SerializeField] private string playerDeathSound = "PlayerDeath";
        
        [Header("收集品音效")]
        [SerializeField] private string coinCollectSound = "CoinCollect";
        [SerializeField] private string gemCollectSound = "GemCollect";
        [SerializeField] private string powerUpCollectSound = "PowerUpCollect";
        [SerializeField] private string bonusCollectSound = "BonusCollect";
        
        [Header("分数系统音效")]
        [SerializeField] private string scoreIncrementSound = "ScoreIncrement";
        [SerializeField] private string comboSound = "Combo";
        [SerializeField] private string milestoneSound = "Milestone";
        [SerializeField] private string highScoreSound = "HighScore";
        
        [Header("成就系统音效")]
        [SerializeField] private string achievementUnlockedSound = "AchievementUnlocked";
        [SerializeField] private string levelCompleteSound = "LevelComplete";
        [SerializeField] private string perfectScoreSound = "PerfectScore";
        
        [Header("敌人音效")]
        [SerializeField] private string enemyHitSound = "EnemyHit";
        [SerializeField] private string enemyDeathSound = "EnemyDeath";
        [SerializeField] private string enemyAttackSound = "EnemyAttack";
        
        [Header("环境音效")]
        [SerializeField] private string windSound = "Wind";
        [SerializeField] private string waterSound = "Water";
        [SerializeField] private string footstepSound = "Footstep";
        
        [Header("背景音乐")]
        [SerializeField] private string mainMenuMusic = "MainMenuMusic";
        [SerializeField] private string gameplayMusic = "GameplayMusic";
        [SerializeField] private string pauseMenuMusic = "PauseMenuMusic";
        [SerializeField] private string gameOverMusic = "GameOverMusic";
        
        /// <summary>
        /// 初始化默认音频事件
        /// </summary>
        [ContextMenu("初始化默认音频事件")]
        public void InitializeDefaultAudioEvents()
        {
            // 清空现有事件
            ClearCache();
            
            // 游戏状态事件
            AddDefaultAudioEvent("GameStart", gameStartSound, "游戏开始音效");
            AddDefaultAudioEvent("GamePause", gamePauseSound, "游戏暂停音效");
            AddDefaultAudioEvent("GameResume", gameResumeSound, "游戏恢复音效");
            AddDefaultAudioEvent("GameOver", gameOverSound, "游戏结束音效");
            
            // UI事件
            AddDefaultAudioEvent("ButtonClick", buttonClickSound, "按钮点击音效");
            AddDefaultAudioEvent("ButtonHover", buttonHoverSound, "按钮悬停音效");
            AddDefaultAudioEvent("MenuOpen", menuOpenSound, "菜单打开音效");
            AddDefaultAudioEvent("MenuClose", menuCloseSound, "菜单关闭音效");
            
            // 玩家事件
            AddDefaultAudioEvent("PlayerJump", playerJumpSound, "玩家跳跃音效");
            AddDefaultAudioEvent("PlayerLand", playerLandSound, "玩家着陆音效");
            AddDefaultAudioEvent("PlayerHurt", playerHurtSound, "玩家受伤音效");
            AddDefaultAudioEvent("PlayerDeath", playerDeathSound, "玩家死亡音效");
            
            // 收集品事件
            AddDefaultAudioEvent("CoinCollect", coinCollectSound, "金币收集音效");
            AddDefaultAudioEvent("GemCollect", gemCollectSound, "宝石收集音效");
            AddDefaultAudioEvent("PowerUpCollect", powerUpCollectSound, "道具收集音效");
            AddDefaultAudioEvent("BonusCollect", bonusCollectSound, "奖励收集音效");
            
            // 分数系统事件
            AddDefaultAudioEvent("ScoreChanged", scoreIncrementSound, "分数变化音效");
            AddDefaultAudioEvent("Combo1", comboSound, "连击音效");
            AddDefaultAudioEvent("Combo2", comboSound, "连击音效");
            AddDefaultAudioEvent("Combo3", comboSound, "连击音效");
            AddDefaultAudioEvent("Combo4", comboSound, "连击音效");
            AddDefaultAudioEvent("Combo5", comboSound, "连击音效");
            AddDefaultAudioEvent("MilestoneReached", milestoneSound, "里程碑达成音效");
            AddDefaultAudioEvent("HighScore", highScoreSound, "新纪录音效");
            
            // 成就系统事件
            AddDefaultAudioEvent("AchievementUnlocked", achievementUnlockedSound, "成就解锁音效");
            AddDefaultAudioEvent("LevelComplete", levelCompleteSound, "关卡完成音效");
            AddDefaultAudioEvent("PerfectScore", perfectScoreSound, "完美分数音效");
            
            // 敌人事件
            AddDefaultAudioEvent("EnemyHit", enemyHitSound, "敌人受击音效");
            AddDefaultAudioEvent("EnemyDeath", enemyDeathSound, "敌人死亡音效");
            AddDefaultAudioEvent("EnemyAttack", enemyAttackSound, "敌人攻击音效");
            
            // 环境事件
            AddDefaultAudioEvent("Wind", windSound, "风声音效");
            AddDefaultAudioEvent("Water", waterSound, "水声音效");
            AddDefaultAudioEvent("Footstep", footstepSound, "脚步声音效");
            
            Debug.Log("默认音频事件初始化完成！");
        }
        
        /// <summary>
        /// 添加默认音频事件
        /// </summary>
        private void AddDefaultAudioEvent(string eventName, string audioName, string description)
        {
            AudioEvent audioEvent = new AudioEvent
            {
                eventName = eventName,
                audioName = audioName,
                description = description,
                delay = 0f,
                enableConditions = false,
                cooldownTime = 0f,
                maxPlaysPerSecond = 5
            };
            
            AddAudioEvent(audioEvent);
        }
        
        /// <summary>
        /// 获取游戏状态音效名称
        /// </summary>
        public string GetGameStateSound(string stateName)
        {
            switch (stateName.ToLower())
            {
                case "start": return gameStartSound;
                case "pause": return gamePauseSound;
                case "resume": return gameResumeSound;
                case "over": return gameOverSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取UI音效名称
        /// </summary>
        public string GetUISound(string uiAction)
        {
            switch (uiAction.ToLower())
            {
                case "click": return buttonClickSound;
                case "hover": return buttonHoverSound;
                case "open": return menuOpenSound;
                case "close": return menuCloseSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取玩家音效名称
        /// </summary>
        public string GetPlayerSound(string action)
        {
            switch (action.ToLower())
            {
                case "jump": return playerJumpSound;
                case "land": return playerLandSound;
                case "hurt": return playerHurtSound;
                case "death": return playerDeathSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取收集品音效名称
        /// </summary>
        public string GetCollectibleSound(string itemType)
        {
            switch (itemType.ToLower())
            {
                case "coin": return coinCollectSound;
                case "gem": return gemCollectSound;
                case "powerup": return powerUpCollectSound;
                case "bonus": return bonusCollectSound;
                default: return coinCollectSound; // 默认使用金币音效
            }
        }
        
        /// <summary>
        /// 获取分数系统音效名称
        /// </summary>
        public string GetScoreSound(string scoreEvent)
        {
            switch (scoreEvent.ToLower())
            {
                case "increment": return scoreIncrementSound;
                case "combo": return comboSound;
                case "milestone": return milestoneSound;
                case "highscore": return highScoreSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取成就系统音效名称
        /// </summary>
        public string GetAchievementSound(string achievementEvent)
        {
            switch (achievementEvent.ToLower())
            {
                case "unlocked": return achievementUnlockedSound;
                case "levelcomplete": return levelCompleteSound;
                case "perfectscore": return perfectScoreSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取敌人音效名称
        /// </summary>
        public string GetEnemySound(string action)
        {
            switch (action.ToLower())
            {
                case "hit": return enemyHitSound;
                case "death": return enemyDeathSound;
                case "attack": return enemyAttackSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取环境音效名称
        /// </summary>
        public string GetEnvironmentSound(string environmentType)
        {
            switch (environmentType.ToLower())
            {
                case "wind": return windSound;
                case "water": return waterSound;
                case "footstep": return footstepSound;
                default: return "";
            }
        }
        
        /// <summary>
        /// 获取背景音乐名称
        /// </summary>
        public string GetBackgroundMusic(string musicType)
        {
            switch (musicType.ToLower())
            {
                case "mainmenu": return mainMenuMusic;
                case "gameplay": return gameplayMusic;
                case "pausemenu": return pauseMenuMusic;
                case "gameover": return gameOverMusic;
                default: return "";
            }
        }
        
        /// <summary>
        /// 验证音频配置
        /// </summary>
        [ContextMenu("验证音频配置")]
        public void ValidateAudioConfig()
        {
            bool isValid = true;
            
            // 检查必要的音效是否配置
            string[] requiredSounds = {
                gameStartSound, gamePauseSound, gameResumeSound, gameOverSound,
                buttonClickSound, playerJumpSound, coinCollectSound,
                achievementUnlockedSound, milestoneSound
            };
            
            foreach (string soundName in requiredSounds)
            {
                if (string.IsNullOrEmpty(soundName))
                {
                    Debug.LogWarning($"必要音效未配置: {soundName}");
                    isValid = false;
                }
            }
            
            if (isValid)
            {
                Debug.Log("音频配置验证通过！");
            }
            else
            {
                Debug.LogError("音频配置验证失败，请检查缺失的音效配置。");
            }
        }
    }
}
