using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

namespace MobileScrollingGame.Build
{
    /// <summary>
    /// 构建优化器
    /// 处理游戏资源优化、构建设置配置和平台特定优化
    /// </summary>
    public class BuildOptimizer
    {
        /// <summary>
        /// 构建配置
        /// </summary>
        [System.Serializable]
        public class BuildConfig
        {
            [Header("基本设置")]
            public string productName = "Mobile Scrolling Game";
            public string companyName = "Game Studio";
            public string version = "1.0.0";
            public int bundleVersionCode = 1;
            
            [Header("平台设置")]
            public BuildTarget buildTarget = BuildTarget.Android;
            public bool developmentBuild = false;
            public bool allowDebugging = false;
            public bool autoconnectProfiler = false;
            
            [Header("优化设置")]
            public bool enableResourceOptimization = true;
            public bool enableCodeStripping = true;
            public bool enableTextureCompression = true;
            public bool enableAudioCompression = true;
            
            [Header("Android设置")]
            public AndroidSdkVersions minSdkVersion = AndroidSdkVersions.AndroidApiLevel23;
            public AndroidSdkVersions targetSdkVersion = AndroidSdkVersions.AndroidApiLevel30;
            public AndroidArchitecture targetArchitectures = AndroidArchitecture.ARM64;
            public bool useAPKExpansionFiles = false;
            
            [Header("iOS设置")]
            public iOSTargetDevice targetDevice = iOSTargetDevice.iPhoneAndiPad;
            public string targetOSVersionString = "11.0";
            public bool requiresFullScreen = true;
            public iOSStatusBarStyle statusBarStyle = iOSStatusBarStyle.Default;
        }
        
        /// <summary>
        /// 资源优化配置
        /// </summary>
        [System.Serializable]
        public class ResourceOptimizationConfig
        {
            [Header("纹理优化")]
            public int maxTextureSize = 2048;
            public TextureImporterCompression textureCompression = TextureImporterCompression.Compressed;
            public bool generateMipmaps = true;
            public FilterMode filterMode = FilterMode.Bilinear;
            
            [Header("音频优化")]
            public AudioCompressionFormat audioFormat = AudioCompressionFormat.Vorbis;
            public float audioQuality = 0.7f;
            public bool force3D = false;
            public AudioClipLoadType loadType = AudioClipLoadType.CompressedInMemory;
            
            [Header("模型优化")]
            public bool optimizeMesh = true;
            public bool generateColliders = false;
            public bool importBlendShapes = false;
            public ModelImporterNormals importNormals = ModelImporterNormals.Import;
            
            [Header("动画优化")]
            public bool optimizeGameObjects = true;
            public bool importAnimation = true;
            public ModelImporterAnimationCompression animationCompression = ModelImporterAnimationCompression.Optimal;
        }
        
        private static BuildConfig currentBuildConfig;
        private static ResourceOptimizationConfig resourceConfig;
        
        #region 构建配置
        
        /// <summary>
        /// 设置构建配置
        /// </summary>
        public static void SetupBuildConfiguration(BuildConfig config)
        {
            currentBuildConfig = config;
            
            // 应用基本设置
            PlayerSettings.productName = config.productName;
            PlayerSettings.companyName = config.companyName;
            PlayerSettings.bundleVersion = config.version;
            
            // 应用平台特定设置
            ApplyPlatformSpecificSettings(config);
            
            // 应用优化设置
            if (config.enableResourceOptimization)
            {
                ApplyResourceOptimizations();
            }
            
            if (config.enableCodeStripping)
            {
                ApplyCodeStrippingSettings();
            }
            
            Debug.Log($"构建配置已设置: {config.productName} v{config.version}");
        }
        
        /// <summary>
        /// 应用平台特定设置
        /// </summary>
        private static void ApplyPlatformSpecificSettings(BuildConfig config)
        {
            switch (config.buildTarget)
            {
                case BuildTarget.Android:
                    ApplyAndroidSettings(config);
                    break;
                case BuildTarget.iOS:
                    ApplyiOSSettings(config);
                    break;
            }
        }
        
        /// <summary>
        /// 应用Android设置
        /// </summary>
        private static void ApplyAndroidSettings(BuildConfig config)
        {
            PlayerSettings.Android.minSdkVersion = config.minSdkVersion;
            PlayerSettings.Android.targetSdkVersion = config.targetSdkVersion;
            PlayerSettings.Android.targetArchitectures = config.targetArchitectures;
            PlayerSettings.Android.splitApplicationBinary = config.useAPKExpansionFiles;
            PlayerSettings.Android.bundleVersionCode = config.bundleVersionCode;
            
            // 设置包名
            string packageName = $"com.{config.companyName.ToLower().Replace(" ", "")}.{config.productName.ToLower().Replace(" ", "")}";
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, packageName);
            
            // 优化设置
            PlayerSettings.Android.blitType = AndroidBlitType.Auto;
            PlayerSettings.Android.startInFullscreen = true;
            PlayerSettings.Android.splashScreenScale = AndroidSplashScreenScale.ScaleToFill;
            
            Debug.Log($"Android设置已应用: {packageName}");
        }
        
        /// <summary>
        /// 应用iOS设置
        /// </summary>
        private static void ApplyiOSSettings(BuildConfig config)
        {
            PlayerSettings.iOS.targetDevice = config.targetDevice;
            PlayerSettings.iOS.targetOSVersionString = config.targetOSVersionString;
            PlayerSettings.iOS.requiresFullScreen = config.requiresFullScreen;
            PlayerSettings.iOS.statusBarStyle = config.statusBarStyle;
            
            // 设置Bundle ID
            string bundleId = $"com.{config.companyName.ToLower().Replace(" ", "")}.{config.productName.ToLower().Replace(" ", "")}";
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, bundleId);
            
            // 优化设置
            PlayerSettings.iOS.scriptCallOptimization = ScriptCallOptimizationLevel.SlowAndSafe;
            PlayerSettings.iOS.sdkVersion = iOSSdkVersion.DeviceSDK;
            
            Debug.Log($"iOS设置已应用: {bundleId}");
        }
        
        #endregion
        
        #region 资源优化
        
        /// <summary>
        /// 应用资源优化
        /// </summary>
        public static void ApplyResourceOptimizations()
        {
            if (resourceConfig == null)
            {
                resourceConfig = CreateDefaultResourceConfig();
            }
            
            OptimizeTextures();
            OptimizeAudioClips();
            OptimizeModels();
            OptimizeAnimations();
            
            Debug.Log("资源优化已完成");
        }
        
        /// <summary>
        /// 创建默认资源配置
        /// </summary>
        private static ResourceOptimizationConfig CreateDefaultResourceConfig()
        {
            return new ResourceOptimizationConfig
            {
                maxTextureSize = 2048,
                textureCompression = TextureImporterCompression.Compressed,
                generateMipmaps = true,
                filterMode = FilterMode.Bilinear,
                audioFormat = AudioCompressionFormat.Vorbis,
                audioQuality = 0.7f,
                loadType = AudioClipLoadType.CompressedInMemory,
                optimizeMesh = true,
                animationCompression = ModelImporterAnimationCompression.Optimal
            };
        }
        
        /// <summary>
        /// 优化纹理
        /// </summary>
        private static void OptimizeTextures()
        {
            string[] textureGuids = AssetDatabase.FindAssets("t:Texture2D");
            
            foreach (string guid in textureGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
                
                if (importer != null)
                {
                    bool changed = false;
                    
                    // 设置最大纹理尺寸
                    if (importer.maxTextureSize > resourceConfig.maxTextureSize)
                    {
                        importer.maxTextureSize = resourceConfig.maxTextureSize;
                        changed = true;
                    }
                    
                    // 设置压缩格式
                    if (importer.textureCompression != resourceConfig.textureCompression)
                    {
                        importer.textureCompression = resourceConfig.textureCompression;
                        changed = true;
                    }
                    
                    // 设置Mipmap生成
                    if (importer.mipmapEnabled != resourceConfig.generateMipmaps)
                    {
                        importer.mipmapEnabled = resourceConfig.generateMipmaps;
                        changed = true;
                    }
                    
                    // 设置过滤模式
                    if (importer.filterMode != resourceConfig.filterMode)
                    {
                        importer.filterMode = resourceConfig.filterMode;
                        changed = true;
                    }
                    
                    if (changed)
                    {
                        importer.SaveAndReimport();
                    }
                }
            }
            
            Debug.Log($"已优化 {textureGuids.Length} 个纹理文件");
        }
        
        /// <summary>
        /// 优化音频剪辑
        /// </summary>
        private static void OptimizeAudioClips()
        {
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            
            foreach (string guid in audioGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AudioImporter importer = AssetImporter.GetAtPath(path) as AudioImporter;
                
                if (importer != null)
                {
                    bool changed = false;
                    
                    // 获取默认设置
                    AudioImporterSampleSettings settings = importer.defaultSampleSettings;
                    
                    // 设置压缩格式
                    if (settings.compressionFormat != resourceConfig.audioFormat)
                    {
                        settings.compressionFormat = resourceConfig.audioFormat;
                        changed = true;
                    }
                    
                    // 设置质量
                    if (settings.quality != resourceConfig.audioQuality)
                    {
                        settings.quality = resourceConfig.audioQuality;
                        changed = true;
                    }
                    
                    // 设置加载类型
                    if (settings.loadType != resourceConfig.loadType)
                    {
                        settings.loadType = resourceConfig.loadType;
                        changed = true;
                    }
                    
                    if (changed)
                    {
                        importer.defaultSampleSettings = settings;
                        importer.SaveAndReimport();
                    }
                }
            }
            
            Debug.Log($"已优化 {audioGuids.Length} 个音频文件");
        }
        
        /// <summary>
        /// 优化模型
        /// </summary>
        private static void OptimizeModels()
        {
            string[] modelGuids = AssetDatabase.FindAssets("t:Model");
            
            foreach (string guid in modelGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;
                
                if (importer != null)
                {
                    bool changed = false;
                    
                    // 设置网格优化
                    if (importer.optimizeMeshVertices != resourceConfig.optimizeMesh)
                    {
                        importer.optimizeMeshVertices = resourceConfig.optimizeMesh;
                        importer.optimizeMeshPolygons = resourceConfig.optimizeMesh;
                        changed = true;
                    }
                    
                    // 设置碰撞器生成
                    if (importer.generateSecondaryUV != resourceConfig.generateColliders)
                    {
                        importer.generateSecondaryUV = resourceConfig.generateColliders;
                        changed = true;
                    }
                    
                    // 设置法线导入
                    if (importer.importNormals != resourceConfig.importNormals)
                    {
                        importer.importNormals = resourceConfig.importNormals;
                        changed = true;
                    }
                    
                    if (changed)
                    {
                        importer.SaveAndReimport();
                    }
                }
            }
            
            Debug.Log($"已优化 {modelGuids.Length} 个模型文件");
        }
        
        /// <summary>
        /// 优化动画
        /// </summary>
        private static void OptimizeAnimations()
        {
            string[] animationGuids = AssetDatabase.FindAssets("t:AnimationClip");
            
            foreach (string guid in animationGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;
                
                if (importer != null && importer.importAnimation)
                {
                    bool changed = false;
                    
                    // 设置动画压缩
                    if (importer.animationCompression != resourceConfig.animationCompression)
                    {
                        importer.animationCompression = resourceConfig.animationCompression;
                        changed = true;
                    }
                    
                    // 设置游戏对象优化
                    if (importer.optimizeGameObjects != resourceConfig.optimizeGameObjects)
                    {
                        importer.optimizeGameObjects = resourceConfig.optimizeGameObjects;
                        changed = true;
                    }
                    
                    if (changed)
                    {
                        importer.SaveAndReimport();
                    }
                }
            }
            
            Debug.Log($"已优化动画文件");
        }
        
        #endregion
        
        #region 代码剥离
        
        /// <summary>
        /// 应用代码剥离设置
        /// </summary>
        private static void ApplyCodeStrippingSettings()
        {
            // 设置代码剥离等级
            PlayerSettings.stripEngineCode = true;
            PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.Android, ManagedStrippingLevel.Medium);
            PlayerSettings.SetManagedStrippingLevel(BuildTargetGroup.iOS, ManagedStrippingLevel.Medium);
            
            // 设置脚本后端
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.iOS, ScriptingImplementation.IL2CPP);
            
            // 设置API兼容性等级
            PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Android, ApiCompatibilityLevel.NET_Standard_2_0);
            PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.iOS, ApiCompatibilityLevel.NET_Standard_2_0);
            
            Debug.Log("代码剥离设置已应用");
        }
        
        #endregion
        
        #region 构建验证
        
        /// <summary>
        /// 验证构建设置
        /// </summary>
        public static bool ValidateBuildSettings()
        {
            List<string> issues = new List<string>();
            
            // 验证基本设置
            if (string.IsNullOrEmpty(PlayerSettings.productName))
            {
                issues.Add("产品名称不能为空");
            }
            
            if (string.IsNullOrEmpty(PlayerSettings.companyName))
            {
                issues.Add("公司名称不能为空");
            }
            
            if (string.IsNullOrEmpty(PlayerSettings.bundleVersion))
            {
                issues.Add("版本号不能为空");
            }
            
            // 验证平台特定设置
            ValidatePlatformSettings(issues);
            
            // 验证资源
            ValidateResources(issues);
            
            if (issues.Count > 0)
            {
                Debug.LogError("构建验证失败:");
                foreach (string issue in issues)
                {
                    Debug.LogError($"- {issue}");
                }
                return false;
            }
            
            Debug.Log("构建验证通过");
            return true;
        }
        
        /// <summary>
        /// 验证平台设置
        /// </summary>
        private static void ValidatePlatformSettings(List<string> issues)
        {
            BuildTarget currentTarget = EditorUserBuildSettings.activeBuildTarget;
            
            switch (currentTarget)
            {
                case BuildTarget.Android:
                    ValidateAndroidSettings(issues);
                    break;
                case BuildTarget.iOS:
                    ValidateiOSSettings(issues);
                    break;
            }
        }
        
        /// <summary>
        /// 验证Android设置
        /// </summary>
        private static void ValidateAndroidSettings(List<string> issues)
        {
            if (PlayerSettings.Android.minSdkVersion < AndroidSdkVersions.AndroidApiLevel23)
            {
                issues.Add("Android最小SDK版本应至少为API 23");
            }
            
            if (string.IsNullOrEmpty(PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.Android)))
            {
                issues.Add("Android包名不能为空");
            }
            
            if (PlayerSettings.Android.bundleVersionCode <= 0)
            {
                issues.Add("Android版本代码必须大于0");
            }
        }
        
        /// <summary>
        /// 验证iOS设置
        /// </summary>
        private static void ValidateiOSSettings(List<string> issues)
        {
            if (string.IsNullOrEmpty(PlayerSettings.iOS.targetOSVersionString))
            {
                issues.Add("iOS目标版本不能为空");
            }
            
            if (string.IsNullOrEmpty(PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.iOS)))
            {
                issues.Add("iOS Bundle ID不能为空");
            }
        }
        
        /// <summary>
        /// 验证资源
        /// </summary>
        private static void ValidateResources(List<string> issues)
        {
            // 检查必要的场景
            if (EditorBuildSettings.scenes.Length == 0)
            {
                issues.Add("构建设置中没有场景");
            }
            
            // 检查启动场景
            bool hasMainScene = false;
            foreach (var scene in EditorBuildSettings.scenes)
            {
                if (scene.enabled && scene.path.Contains("Main"))
                {
                    hasMainScene = true;
                    break;
                }
            }
            
            if (!hasMainScene)
            {
                issues.Add("没有找到主场景");
            }
            
            // 检查图标
            Texture2D[] icons = PlayerSettings.GetIconsForTargetGroup(BuildTargetGroup.Unknown);
            if (icons == null || icons.Length == 0 || icons[0] == null)
            {
                issues.Add("没有设置应用图标");
            }
        }
        
        #endregion
        
        #region 构建报告
        
        /// <summary>
        /// 生成构建报告
        /// </summary>
        public static void GenerateBuildReport()
        {
            string reportPath = Path.Combine(Application.dataPath, "../BuildReports");
            if (!Directory.Exists(reportPath))
            {
                Directory.CreateDirectory(reportPath);
            }
            
            string reportFile = Path.Combine(reportPath, $"BuildReport_{System.DateTime.Now:yyyyMMdd_HHmmss}.txt");
            
            using (StreamWriter writer = new StreamWriter(reportFile))
            {
                writer.WriteLine("=== 构建报告 ===");
                writer.WriteLine($"生成时间: {System.DateTime.Now}");
                writer.WriteLine($"Unity版本: {Application.unityVersion}");
                writer.WriteLine();
                
                writer.WriteLine("=== 项目信息 ===");
                writer.WriteLine($"产品名称: {PlayerSettings.productName}");
                writer.WriteLine($"公司名称: {PlayerSettings.companyName}");
                writer.WriteLine($"版本: {PlayerSettings.bundleVersion}");
                writer.WriteLine($"目标平台: {EditorUserBuildSettings.activeBuildTarget}");
                writer.WriteLine();
                
                writer.WriteLine("=== 构建设置 ===");
                writer.WriteLine($"开发构建: {EditorUserBuildSettings.development}");
                writer.WriteLine($"脚本调试: {EditorUserBuildSettings.allowDebugging}");
                writer.WriteLine($"自动连接分析器: {EditorUserBuildSettings.connectProfiler}");
                writer.WriteLine();
                
                writer.WriteLine("=== 优化设置 ===");
                writer.WriteLine($"代码剥离: {PlayerSettings.stripEngineCode}");
                writer.WriteLine($"脚本后端: {PlayerSettings.GetScriptingBackend(BuildTargetGroup.Android)}");
                writer.WriteLine($"API兼容性: {PlayerSettings.GetApiCompatibilityLevel(BuildTargetGroup.Android)}");
                writer.WriteLine();
                
                writer.WriteLine("=== 场景列表 ===");
                foreach (var scene in EditorBuildSettings.scenes)
                {
                    writer.WriteLine($"- {scene.path} ({(scene.enabled ? "启用" : "禁用")})");
                }
            }
            
            Debug.Log($"构建报告已生成: {reportFile}");
        }
        
        #endregion
        
        #region 公共API
        
        /// <summary>
        /// 执行完整的构建优化流程
        /// </summary>
        public static bool PerformFullOptimization(BuildConfig config)
        {
            Debug.Log("开始执行完整构建优化...");
            
            try
            {
                // 设置构建配置
                SetupBuildConfiguration(config);
                
                // 应用资源优化
                ApplyResourceOptimizations();
                
                // 验证构建设置
                if (!ValidateBuildSettings())
                {
                    return false;
                }
                
                // 生成构建报告
                GenerateBuildReport();
                
                Debug.Log("构建优化完成");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"构建优化失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 创建默认构建配置
        /// </summary>
        public static BuildConfig CreateDefaultBuildConfig()
        {
            return new BuildConfig
            {
                productName = "Mobile Scrolling Game",
                companyName = "Game Studio",
                version = "1.0.0",
                bundleVersionCode = 1,
                buildTarget = BuildTarget.Android,
                developmentBuild = false,
                enableResourceOptimization = true,
                enableCodeStripping = true,
                enableTextureCompression = true,
                enableAudioCompression = true,
                minSdkVersion = AndroidSdkVersions.AndroidApiLevel23,
                targetSdkVersion = AndroidSdkVersions.AndroidApiLevel30,
                targetArchitectures = AndroidArchitecture.ARM64
            };
        }
        
        #endregion
    }
}
