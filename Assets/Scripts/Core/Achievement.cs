using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 成就类型枚举
    /// </summary>
    public enum AchievementType
    {
        Score,          // 分数相关成就
        Collectible,    // 收集品相关成就
        Combo,          // 连击相关成就
        Level,          // 关卡相关成就
        Survival,       // 生存相关成就
        Special         // 特殊成就
    }
    
    /// <summary>
    /// 成就数据结构
    /// 定义单个成就的属性和状态
    /// </summary>
    [System.Serializable]
    public class Achievement
    {
        [Header("基本信息")]
        public string id;
        public string name;
        public string description;
        public AchievementType type;
        
        [Header("解锁条件")]
        public int targetValue;
        public int currentValue;
        public bool isUnlocked;
        
        [Header("奖励")]
        public int scoreReward;
        public string rewardDescription;
        
        [Header("显示设置")]
        public Sprite icon;
        public bool isHidden; // 隐藏成就，直到解锁
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public Achievement(string id, string name, string description, AchievementType type, int targetValue)
        {
            this.id = id;
            this.name = name;
            this.description = description;
            this.type = type;
            this.targetValue = targetValue;
            this.currentValue = 0;
            this.isUnlocked = false;
            this.scoreReward = 0;
            this.isHidden = false;
        }
        
        /// <summary>
        /// 更新进度
        /// </summary>
        public bool UpdateProgress(int value)
        {
            if (isUnlocked) return false;
            
            currentValue = Mathf.Max(currentValue, value);
            
            if (currentValue >= targetValue && !isUnlocked)
            {
                isUnlocked = true;
                return true; // 返回true表示刚刚解锁
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取进度百分比
        /// </summary>
        public float GetProgressPercentage()
        {
            if (targetValue <= 0) return 0f;
            return Mathf.Clamp01((float)currentValue / targetValue);
        }
        
        /// <summary>
        /// 检查是否可以显示
        /// </summary>
        public bool CanDisplay()
        {
            return !isHidden || isUnlocked;
        }
        
        /// <summary>
        /// 重置成就进度
        /// </summary>
        public void Reset()
        {
            currentValue = 0;
            isUnlocked = false;
        }
        
        /// <summary>
        /// 获取成就状态描述
        /// </summary>
        public string GetStatusDescription()
        {
            if (isUnlocked)
            {
                return "已解锁";
            }
            else if (isHidden)
            {
                return "???";
            }
            else
            {
                return $"{currentValue}/{targetValue}";
            }
        }
    }
}