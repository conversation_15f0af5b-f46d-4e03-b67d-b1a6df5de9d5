using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 成就管理器
    /// 处理成就的创建、跟踪、解锁和保存
    /// </summary>
    public class AchievementManager : MonoBehaviour
    {
        [Header("成就设置")]
        [SerializeField] private List<Achievement> achievements = new List<Achievement>();
        [SerializeField] private string saveKey = "Achievements";
        
        [Header("反馈设置")]
        [SerializeField] private bool showUnlockNotifications = true;
        [SerializeField] private float notificationDuration = 3f;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 事件
        public System.Action<Achievement> OnAchievementUnlocked;
        public System.Action<Achievement> OnAchievementProgress;
        
        // 属性
        public List<Achievement> Achievements => new List<Achievement>(achievements);
        public int UnlockedCount => achievements.Count(a => a.isUnlocked);
        public int TotalCount => achievements.Count;
        
        private void Awake()
        {
            InitializeAchievements();
            LoadAchievements();
        }
        
        private void Start()
        {
            RegisterGameEvents();
        }
        
        /// <summary>
        /// 初始化成就列表
        /// </summary>
        private void InitializeAchievements()
        {
            if (achievements.Count == 0)
            {
                CreateDefaultAchievements();
            }
        }
        
        /// <summary>
        /// 创建默认成就
        /// </summary>
        private void CreateDefaultAchievements()
        {
            achievements.Clear();
            
            // 分数相关成就
            achievements.Add(new Achievement("score_100", "初学者", "获得100分", AchievementType.Score, 100) { scoreReward = 50 });
            achievements.Add(new Achievement("score_500", "进步者", "获得500分", AchievementType.Score, 500) { scoreReward = 100 });
            achievements.Add(new Achievement("score_1000", "高手", "获得1000分", AchievementType.Score, 1000) { scoreReward = 200 });
            achievements.Add(new Achievement("score_5000", "专家", "获得5000分", AchievementType.Score, 5000) { scoreReward = 500 });
            achievements.Add(new Achievement("score_10000", "大师", "获得10000分", AchievementType.Score, 10000) { scoreReward = 1000 });
            
            // 收集品相关成就
            achievements.Add(new Achievement("collect_10", "收集家", "收集10个物品", AchievementType.Collectible, 10) { scoreReward = 100 });
            achievements.Add(new Achievement("collect_50", "寻宝者", "收集50个物品", AchievementType.Collectible, 50) { scoreReward = 250 });
            achievements.Add(new Achievement("collect_100", "收藏大师", "收集100个物品", AchievementType.Collectible, 100) { scoreReward = 500 });
            
            // 连击相关成就
            achievements.Add(new Achievement("combo_5", "连击新手", "达成5连击", AchievementType.Combo, 5) { scoreReward = 50 });
            achievements.Add(new Achievement("combo_10", "连击高手", "达成10连击", AchievementType.Combo, 10) { scoreReward = 150 });
            achievements.Add(new Achievement("combo_20", "连击大师", "达成20连击", AchievementType.Combo, 20) { scoreReward = 300 });
            
            // 特殊成就
            achievements.Add(new Achievement("first_death", "初次失败", "第一次死亡", AchievementType.Special, 1) { scoreReward = 25, isHidden = true });
            achievements.Add(new Achievement("perfect_level", "完美通关", "不受伤完成一个关卡", AchievementType.Special, 1) { scoreReward = 500, isHidden = true });
        }
        
        /// <summary>
        /// 注册游戏事件
        /// </summary>
        private void RegisterGameEvents()
        {
            // 注册分数管理器事件
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.OnScoreChanged += OnScoreChanged;
                ScoreManager.Instance.OnComboChanged += OnComboChanged;
                ScoreManager.Instance.OnItemCollected += OnItemCollected;
            }
            
            // 注册健康系统事件（用于死亡成就）
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                Health playerHealth = player.GetComponent<Health>();
                if (playerHealth != null)
                {
                    playerHealth.OnDeath += OnPlayerDeath;
                }
            }
        }
        
        /// <summary>
        /// 处理分数变化
        /// </summary>
        private void OnScoreChanged(int newScore)
        {
            CheckScoreAchievements(newScore);
        }
        
        /// <summary>
        /// 处理连击变化
        /// </summary>
        private void OnComboChanged(int comboCount)
        {
            CheckComboAchievements(comboCount);
        }
        
        /// <summary>
        /// 处理物品收集
        /// </summary>
        private void OnItemCollected(CollectibleType type, int quantity)
        {
            if (ScoreManager.Instance != null)
            {
                int totalCollected = ScoreManager.Instance.GetTotalCollectedItems();
                CheckCollectibleAchievements(totalCollected);
            }
        }
        
        /// <summary>
        /// 处理玩家死亡
        /// </summary>
        private void OnPlayerDeath()
        {
            CheckSpecialAchievement("first_death", 1);
        }
        
        /// <summary>
        /// 检查分数成就
        /// </summary>
        private void CheckScoreAchievements(int score)
        {
            var scoreAchievements = achievements.Where(a => a.type == AchievementType.Score);
            foreach (var achievement in scoreAchievements)
            {
                if (achievement.UpdateProgress(score))
                {
                    UnlockAchievement(achievement);
                }
            }
        }
        
        /// <summary>
        /// 检查连击成就
        /// </summary>
        private void CheckComboAchievements(int comboCount)
        {
            var comboAchievements = achievements.Where(a => a.type == AchievementType.Combo);
            foreach (var achievement in comboAchievements)
            {
                if (achievement.UpdateProgress(comboCount))
                {
                    UnlockAchievement(achievement);
                }
            }
        }
        
        /// <summary>
        /// 检查收集品成就
        /// </summary>
        private void CheckCollectibleAchievements(int totalCollected)
        {
            var collectibleAchievements = achievements.Where(a => a.type == AchievementType.Collectible);
            foreach (var achievement in collectibleAchievements)
            {
                if (achievement.UpdateProgress(totalCollected))
                {
                    UnlockAchievement(achievement);
                }
            }
        }
        
        /// <summary>
        /// 检查特殊成就
        /// </summary>
        private void CheckSpecialAchievement(string achievementId, int value)
        {
            var achievement = achievements.FirstOrDefault(a => a.id == achievementId);
            if (achievement != null && achievement.UpdateProgress(value))
            {
                UnlockAchievement(achievement);
            }
        }
        
        /// <summary>
        /// 解锁成就
        /// </summary>
        private void UnlockAchievement(Achievement achievement)
        {
            if (showDebugInfo)
            {
                Debug.Log($"成就解锁: {achievement.name} - {achievement.description}");
            }
            
            // 给予分数奖励
            if (achievement.scoreReward > 0 && ScoreManager.Instance != null)
            {
                ScoreManager.Instance.AddScore(achievement.scoreReward);
            }
            
            // 触发事件
            OnAchievementUnlocked?.Invoke(achievement);
            
            // 保存成就数据
            SaveAchievements();
        }
        
        /// <summary>
        /// 手动解锁成就（用于测试）
        /// </summary>
        public void UnlockAchievementById(string achievementId)
        {
            var achievement = achievements.FirstOrDefault(a => a.id == achievementId);
            if (achievement != null && !achievement.isUnlocked)
            {
                achievement.isUnlocked = true;
                achievement.currentValue = achievement.targetValue;
                UnlockAchievement(achievement);
            }
        }
        
        /// <summary>
        /// 获取成就进度
        /// </summary>
        public Achievement GetAchievement(string achievementId)
        {
            return achievements.FirstOrDefault(a => a.id == achievementId);
        }
        
        /// <summary>
        /// 获取指定类型的成就
        /// </summary>
        public List<Achievement> GetAchievementsByType(AchievementType type)
        {
            return achievements.Where(a => a.type == type).ToList();
        }
        
        /// <summary>
        /// 获取已解锁的成就
        /// </summary>
        public List<Achievement> GetUnlockedAchievements()
        {
            return achievements.Where(a => a.isUnlocked).ToList();
        }
        
        /// <summary>
        /// 获取可显示的成就
        /// </summary>
        public List<Achievement> GetDisplayableAchievements()
        {
            return achievements.Where(a => a.CanDisplay()).ToList();
        }
        
        /// <summary>
        /// 保存成就数据
        /// </summary>
        private void SaveAchievements()
        {
            try
            {
                var saveData = new AchievementSaveData();
                saveData.achievements = achievements;
                
                string json = JsonUtility.ToJson(saveData, true);
                PlayerPrefs.SetString(saveKey, json);
                PlayerPrefs.Save();
                
                if (showDebugInfo)
                {
                    Debug.Log("成就数据已保存");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"保存成就数据失败: {e.Message}");
            }
        }
        
        /// <summary>
        /// 加载成就数据
        /// </summary>
        private void LoadAchievements()
        {
            try
            {
                if (PlayerPrefs.HasKey(saveKey))
                {
                    string json = PlayerPrefs.GetString(saveKey);
                    var saveData = JsonUtility.FromJson<AchievementSaveData>(json);
                    
                    if (saveData != null && saveData.achievements != null)
                    {
                        // 合并保存的数据和默认成就
                        MergeAchievementData(saveData.achievements);
                        
                        if (showDebugInfo)
                        {
                            Debug.Log($"成就数据已加载，共 {achievements.Count} 个成就");
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加载成就数据失败: {e.Message}");
            }
        }
        
        /// <summary>
        /// 合并成就数据
        /// </summary>
        private void MergeAchievementData(List<Achievement> savedAchievements)
        {
            foreach (var savedAchievement in savedAchievements)
            {
                var existingAchievement = achievements.FirstOrDefault(a => a.id == savedAchievement.id);
                if (existingAchievement != null)
                {
                    existingAchievement.currentValue = savedAchievement.currentValue;
                    existingAchievement.isUnlocked = savedAchievement.isUnlocked;
                }
            }
        }
        
        /// <summary>
        /// 重置所有成就
        /// </summary>
        public void ResetAllAchievements()
        {
            foreach (var achievement in achievements)
            {
                achievement.Reset();
            }
            
            SaveAchievements();
            
            if (showDebugInfo)
            {
                Debug.Log("所有成就已重置");
            }
        }
        
        /// <summary>
        /// 获取成就统计信息
        /// </summary>
        public string GetAchievementStats()
        {
            int unlockedCount = UnlockedCount;
            int totalCount = TotalCount;
            float completionPercentage = totalCount > 0 ? (float)unlockedCount / totalCount * 100f : 0f;
            
            return $"成就进度: {unlockedCount}/{totalCount} ({completionPercentage:F1}%)";
        }
        
        #region 静态实例管理
        
        private static AchievementManager instance;
        public static AchievementManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<AchievementManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("AchievementManager");
                        instance = go.AddComponent<AchievementManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
            
            // 清理事件订阅
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.OnScoreChanged -= OnScoreChanged;
                ScoreManager.Instance.OnComboChanged -= OnComboChanged;
                ScoreManager.Instance.OnItemCollected -= OnItemCollected;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 成就保存数据结构
    /// </summary>
    [System.Serializable]
    public class AchievementSaveData
    {
        public List<Achievement> achievements = new List<Achievement>();
    }
}