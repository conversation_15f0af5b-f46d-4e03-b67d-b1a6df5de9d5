using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 音频剪辑库
    /// 管理游戏中所有的音频资源
    /// </summary>
    [CreateAssetMenu(fileName = "AudioClipLibrary", menuName = "Mobile Scrolling Game/Audio/Audio Clip Library")]
    public class AudioClipLibrary : ScriptableObject
    {
        [System.Serializable]
        public class AudioClipEntry
        {
            [Header("基本信息")]
            public string name;
            public AudioClip clip;
            
            [Header("播放设置")]
            [Range(0f, 1f)] public float volume = 1f;
            [Range(0.1f, 3f)] public float pitch = 1f;
            
            [Header("随机化设置")]
            public bool randomizeVolume = false;
            [Range(0f, 0.5f)] public float volumeVariation = 0.1f;
            
            public bool randomizePitch = false;
            [Range(0f, 0.5f)] public float pitchVariation = 0.1f;
            
            [Header("其他设置")]
            public bool loop = false;
            public int priority = 128;
            
            [Header("分类标签")]
            public AudioCategory category = AudioCategory.SoundEffect;
            public string[] tags;
            
            /// <summary>
            /// 获取随机化后的音量
            /// </summary>
            public float GetRandomizedVolume()
            {
                if (randomizeVolume)
                {
                    return volume + Random.Range(-volumeVariation, volumeVariation);
                }
                return volume;
            }
            
            /// <summary>
            /// 获取随机化后的音调
            /// </summary>
            public float GetRandomizedPitch()
            {
                if (randomizePitch)
                {
                    return pitch + Random.Range(-pitchVariation, pitchVariation);
                }
                return pitch;
            }
        }
        
        [System.Serializable]
        public enum AudioCategory
        {
            SoundEffect,
            BackgroundMusic,
            UI,
            Character,
            Environment,
            Enemy,
            Collectible,
            System
        }
        
        [Header("音效库")]
        [SerializeField] private List<AudioClipEntry> soundEffects = new List<AudioClipEntry>();
        
        [Header("背景音乐库")]
        [SerializeField] private List<AudioClipEntry> backgroundMusic = new List<AudioClipEntry>();
        
        [Header("UI音效库")]
        [SerializeField] private List<AudioClipEntry> uiSounds = new List<AudioClipEntry>();
        
        [Header("角色音效库")]
        [SerializeField] private List<AudioClipEntry> characterSounds = new List<AudioClipEntry>();
        
        [Header("环境音效库")]
        [SerializeField] private List<AudioClipEntry> environmentSounds = new List<AudioClipEntry>();
        
        [Header("敌人音效库")]
        [SerializeField] private List<AudioClipEntry> enemySounds = new List<AudioClipEntry>();
        
        [Header("收集品音效库")]
        [SerializeField] private List<AudioClipEntry> collectibleSounds = new List<AudioClipEntry>();
        
        [Header("系统音效库")]
        [SerializeField] private List<AudioClipEntry> systemSounds = new List<AudioClipEntry>();
        
        // 缓存字典，提高查找性能
        private Dictionary<string, AudioClipEntry> audioClipCache;
        private bool isCacheInitialized = false;
        
        /// <summary>
        /// 初始化缓存
        /// </summary>
        private void InitializeCache()
        {
            if (isCacheInitialized) return;
            
            audioClipCache = new Dictionary<string, AudioClipEntry>();
            
            // 添加所有音频条目到缓存
            AddToCache(soundEffects);
            AddToCache(backgroundMusic);
            AddToCache(uiSounds);
            AddToCache(characterSounds);
            AddToCache(environmentSounds);
            AddToCache(enemySounds);
            AddToCache(collectibleSounds);
            AddToCache(systemSounds);
            
            isCacheInitialized = true;
        }
        
        /// <summary>
        /// 添加音频条目到缓存
        /// </summary>
        private void AddToCache(List<AudioClipEntry> entries)
        {
            foreach (var entry in entries)
            {
                if (entry != null && !string.IsNullOrEmpty(entry.name))
                {
                    if (!audioClipCache.ContainsKey(entry.name))
                    {
                        audioClipCache[entry.name] = entry;
                    }
                    else
                    {
                        Debug.LogWarning($"音频库中存在重复的音频名称: {entry.name}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 获取音频条目
        /// </summary>
        public AudioClipEntry GetAudioEntry(string audioName)
        {
            InitializeCache();
            
            if (audioClipCache.TryGetValue(audioName, out AudioClipEntry entry))
            {
                return entry;
            }
            
            return null;
        }
        
        /// <summary>
        /// 获取音效
        /// </summary>
        public AudioClip GetSoundEffect(string soundName)
        {
            AudioClipEntry entry = GetAudioEntry(soundName);
            return entry?.clip;
        }
        
        /// <summary>
        /// 获取背景音乐
        /// </summary>
        public AudioClip GetBackgroundMusic(string musicName)
        {
            AudioClipEntry entry = GetAudioEntry(musicName);
            return entry?.clip;
        }
        
        /// <summary>
        /// 获取UI音效
        /// </summary>
        public AudioClip GetUISound(string soundName)
        {
            AudioClipEntry entry = GetAudioEntry(soundName);
            return entry?.clip;
        }
        
        /// <summary>
        /// 根据分类获取音频列表
        /// </summary>
        public List<AudioClipEntry> GetAudioByCategory(AudioCategory category)
        {
            InitializeCache();
            
            return audioClipCache.Values.Where(entry => entry.category == category).ToList();
        }
        
        /// <summary>
        /// 根据标签获取音频列表
        /// </summary>
        public List<AudioClipEntry> GetAudioByTag(string tag)
        {
            InitializeCache();
            
            return audioClipCache.Values.Where(entry => 
                entry.tags != null && entry.tags.Contains(tag)).ToList();
        }
        
        /// <summary>
        /// 获取随机音效
        /// </summary>
        public AudioClipEntry GetRandomSoundEffect()
        {
            if (soundEffects.Count == 0) return null;
            
            int randomIndex = Random.Range(0, soundEffects.Count);
            return soundEffects[randomIndex];
        }
        
        /// <summary>
        /// 获取随机背景音乐
        /// </summary>
        public AudioClipEntry GetRandomBackgroundMusic()
        {
            if (backgroundMusic.Count == 0) return null;
            
            int randomIndex = Random.Range(0, backgroundMusic.Count);
            return backgroundMusic[randomIndex];
        }
        
        /// <summary>
        /// 检查音频是否存在
        /// </summary>
        public bool HasAudio(string audioName)
        {
            InitializeCache();
            return audioClipCache.ContainsKey(audioName);
        }
        
        /// <summary>
        /// 获取所有音频名称
        /// </summary>
        public string[] GetAllAudioNames()
        {
            InitializeCache();
            return audioClipCache.Keys.ToArray();
        }
        
        /// <summary>
        /// 获取音频总数
        /// </summary>
        public int GetTotalAudioCount()
        {
            InitializeCache();
            return audioClipCache.Count;
        }
        
        /// <summary>
        /// 验证音频库完整性
        /// </summary>
        public bool ValidateLibrary()
        {
            bool isValid = true;
            
            // 检查重复名称
            HashSet<string> nameSet = new HashSet<string>();
            List<AudioClipEntry> allEntries = new List<AudioClipEntry>();
            
            allEntries.AddRange(soundEffects);
            allEntries.AddRange(backgroundMusic);
            allEntries.AddRange(uiSounds);
            allEntries.AddRange(characterSounds);
            allEntries.AddRange(environmentSounds);
            allEntries.AddRange(enemySounds);
            allEntries.AddRange(collectibleSounds);
            allEntries.AddRange(systemSounds);
            
            foreach (var entry in allEntries)
            {
                if (entry == null) continue;
                
                // 检查名称是否为空
                if (string.IsNullOrEmpty(entry.name))
                {
                    Debug.LogError("音频库中存在空名称的条目");
                    isValid = false;
                    continue;
                }
                
                // 检查重复名称
                if (nameSet.Contains(entry.name))
                {
                    Debug.LogError($"音频库中存在重复名称: {entry.name}");
                    isValid = false;
                }
                else
                {
                    nameSet.Add(entry.name);
                }
                
                // 检查AudioClip是否为空
                if (entry.clip == null)
                {
                    Debug.LogError($"音频条目 '{entry.name}' 的AudioClip为空");
                    isValid = false;
                }
            }
            
            return isValid;
        }
        
        /// <summary>
        /// 清理缓存
        /// </summary>
        public void ClearCache()
        {
            audioClipCache?.Clear();
            isCacheInitialized = false;
        }
        
        #region Unity Editor Methods
        
#if UNITY_EDITOR
        /// <summary>
        /// 在Inspector中验证音频库
        /// </summary>
        [ContextMenu("验证音频库")]
        private void ValidateLibraryInEditor()
        {
            if (ValidateLibrary())
            {
                Debug.Log("音频库验证通过！");
            }
            else
            {
                Debug.LogError("音频库验证失败，请检查错误信息。");
            }
        }
        
        /// <summary>
        /// 自动分类音频
        /// </summary>
        [ContextMenu("自动分类音频")]
        private void AutoCategorizeAudio()
        {
            List<AudioClipEntry> allEntries = new List<AudioClipEntry>();
            allEntries.AddRange(soundEffects);
            allEntries.AddRange(backgroundMusic);
            allEntries.AddRange(uiSounds);
            allEntries.AddRange(characterSounds);
            allEntries.AddRange(environmentSounds);
            allEntries.AddRange(enemySounds);
            allEntries.AddRange(collectibleSounds);
            allEntries.AddRange(systemSounds);
            
            foreach (var entry in allEntries)
            {
                if (entry == null || entry.clip == null) continue;
                
                string clipName = entry.clip.name.ToLower();
                
                // 根据名称自动分类
                if (clipName.Contains("music") || clipName.Contains("bgm") || clipName.Contains("theme"))
                {
                    entry.category = AudioCategory.BackgroundMusic;
                }
                else if (clipName.Contains("ui") || clipName.Contains("button") || clipName.Contains("menu"))
                {
                    entry.category = AudioCategory.UI;
                }
                else if (clipName.Contains("player") || clipName.Contains("character") || clipName.Contains("jump") || clipName.Contains("walk"))
                {
                    entry.category = AudioCategory.Character;
                }
                else if (clipName.Contains("enemy") || clipName.Contains("monster"))
                {
                    entry.category = AudioCategory.Enemy;
                }
                else if (clipName.Contains("collect") || clipName.Contains("pickup") || clipName.Contains("coin"))
                {
                    entry.category = AudioCategory.Collectible;
                }
                else if (clipName.Contains("ambient") || clipName.Contains("wind") || clipName.Contains("water"))
                {
                    entry.category = AudioCategory.Environment;
                }
                else
                {
                    entry.category = AudioCategory.SoundEffect;
                }
            }
            
            Debug.Log("音频自动分类完成！");
        }
#endif
        
        #endregion
    }
}
