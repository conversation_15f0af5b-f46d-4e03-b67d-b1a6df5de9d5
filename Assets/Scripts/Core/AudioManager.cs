using UnityEngine;
using UnityEngine.Audio;
using System.Collections.Generic;
using System.Collections;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 音频管理器
    /// 处理音效和背景音乐的播放、音量控制和音频池管理
    /// </summary>
    public class AudioManager : MonoBehaviour, IAudioManager
    {
        [Header("音频混合器")]
        [SerializeField] private AudioMixerGroup masterMixerGroup;
        [SerializeField] private AudioMixerGroup musicMixerGroup;
        [SerializeField] private AudioMixerGroup sfxMixerGroup;
        
        [Header("音频源")]
        [SerializeField] private AudioSource musicAudioSource;
        [SerializeField] private AudioSource[] sfxAudioSources;
        [SerializeField] private int maxSFXSources = 10;
        
        [Header("音频资源")]
        [SerializeField] private AudioClipLibrary audioLibrary;
        
        [Header("音量设置")]
        [SerializeField] private float masterVolume = 1f;
        [SerializeField] private float musicVolume = 1f;
        [SerializeField] private float sfxVolume = 1f;
        
        [Header("淡入淡出设置")]
        [SerializeField] private float musicFadeDuration = 1f;
        [SerializeField] private AnimationCurve fadeInCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private AnimationCurve fadeOutCurve = AnimationCurve.EaseInOut(0, 1, 1, 0);
        
        [Header("音频池设置")]
        [SerializeField] private bool enableAudioPooling = true;
        [SerializeField] private int poolSize = 20;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 音频池
        private Queue<AudioSource> audioSourcePool;
        private List<AudioSource> activeAudioSources;
        
        // 当前播放状态
        private string currentMusicTrack = "";
        private Coroutine musicFadeCoroutine;
        
        // 音量控制键名
        private const string MASTER_VOLUME_KEY = "MasterVolume";
        private const string MUSIC_VOLUME_KEY = "MusicVolume";
        private const string SFX_VOLUME_KEY = "SFXVolume";
        
        // 事件
        public System.Action<string> OnMusicStarted;
        public System.Action<string> OnMusicStopped;
        public System.Action<string> OnSoundEffectPlayed;
        
        // 属性
        public float MasterVolume => masterVolume;
        public float MusicVolume => musicVolume;
        public float SFXVolume => sfxVolume;
        public string CurrentMusicTrack => currentMusicTrack;
        public bool IsMusicPlaying => musicAudioSource != null && musicAudioSource.isPlaying;
        
        private void Awake()
        {
            InitializeAudioSystem();
            LoadVolumeSettings();
        }
        
        private void Start()
        {
            RegisterWithGameManager();
        }
        
        /// <summary>
        /// 初始化音频系统
        /// </summary>
        private void InitializeAudioSystem()
        {
            // 创建音乐音频源
            if (musicAudioSource == null)
            {
                GameObject musicObject = new GameObject("MusicAudioSource");
                musicObject.transform.SetParent(transform);
                musicAudioSource = musicObject.AddComponent<AudioSource>();
                musicAudioSource.loop = true;
                musicAudioSource.playOnAwake = false;
                musicAudioSource.outputAudioMixerGroup = musicMixerGroup;
            }
            
            // 初始化音效音频源数组
            if (sfxAudioSources == null || sfxAudioSources.Length == 0)
            {
                sfxAudioSources = new AudioSource[maxSFXSources];
                for (int i = 0; i < maxSFXSources; i++)
                {
                    GameObject sfxObject = new GameObject($"SFXAudioSource_{i}");
                    sfxObject.transform.SetParent(transform);
                    sfxAudioSources[i] = sfxObject.AddComponent<AudioSource>();
                    sfxAudioSources[i].playOnAwake = false;
                    sfxAudioSources[i].outputAudioMixerGroup = sfxMixerGroup;
                }
            }
            
            // 初始化音频池
            if (enableAudioPooling)
            {
                InitializeAudioPool();
            }
            
            // 创建默认音频库
            if (audioLibrary == null)
            {
                audioLibrary = ScriptableObject.CreateInstance<AudioClipLibrary>();
            }
        }
        
        /// <summary>
        /// 初始化音频池
        /// </summary>
        private void InitializeAudioPool()
        {
            audioSourcePool = new Queue<AudioSource>();
            activeAudioSources = new List<AudioSource>();
            
            for (int i = 0; i < poolSize; i++)
            {
                GameObject poolObject = new GameObject($"PooledAudioSource_{i}");
                poolObject.transform.SetParent(transform);
                AudioSource audioSource = poolObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.outputAudioMixerGroup = sfxMixerGroup;
                audioSource.gameObject.SetActive(false);
                
                audioSourcePool.Enqueue(audioSource);
            }
        }
        
        /// <summary>
        /// 注册到游戏管理器
        /// </summary>
        private void RegisterWithGameManager()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.RegisterAudioManager(this);
            }
        }
        
        /// <summary>
        /// 加载音量设置
        /// </summary>
        private void LoadVolumeSettings()
        {
            masterVolume = PlayerPrefs.GetFloat(MASTER_VOLUME_KEY, 1f);
            musicVolume = PlayerPrefs.GetFloat(MUSIC_VOLUME_KEY, 1f);
            sfxVolume = PlayerPrefs.GetFloat(SFX_VOLUME_KEY, 1f);
            
            ApplyVolumeSettings();
        }
        
        /// <summary>
        /// 应用音量设置
        /// </summary>
        private void ApplyVolumeSettings()
        {
            if (masterMixerGroup != null && masterMixerGroup.audioMixer != null)
            {
                masterMixerGroup.audioMixer.SetFloat("MasterVolume", Mathf.Log10(masterVolume) * 20);
            }

            if (musicMixerGroup != null && musicMixerGroup.audioMixer != null)
            {
                musicMixerGroup.audioMixer.SetFloat("MusicVolume", Mathf.Log10(musicVolume) * 20);
            }

            if (sfxMixerGroup != null && sfxMixerGroup.audioMixer != null)
            {
                sfxMixerGroup.audioMixer.SetFloat("SFXVolume", Mathf.Log10(sfxVolume) * 20);
            }

            // 如果没有混合器，直接设置AudioSource音量
            if (masterMixerGroup == null)
            {
                if (musicAudioSource != null)
                    musicAudioSource.volume = masterVolume * musicVolume;

                // 确保音频源数组已初始化（防止在测试环境中出现空引用）
                if (sfxAudioSources != null)
                {
                    foreach (var sfxSource in sfxAudioSources)
                    {
                        if (sfxSource != null)
                            sfxSource.volume = masterVolume * sfxVolume;
                    }
                }
            }
        }
        
        #region IAudioManager Implementation
        
        public void PlaySoundEffect(string soundName)
        {
            // 确保音频库已初始化（防止在测试环境中Awake未被调用）
            if (audioLibrary == null)
            {
                InitializeAudioSystem();
            }

            // 双重检查，确保初始化成功
            if (audioLibrary == null)
            {
                if (showDebugInfo)
                {
                    Debug.LogError("AudioManager: 音频库初始化失败");
                }
                return;
            }

            AudioClip clip = audioLibrary.GetSoundEffect(soundName);
            if (clip != null)
            {
                PlaySoundEffect(clip);
            }
            else if (showDebugInfo)
            {
                Debug.LogWarning($"音效未找到: {soundName}");
            }
        }
        
        public void PlayBackgroundMusic(string musicName)
        {
            // 确保音频库已初始化（防止在测试环境中Awake未被调用）
            if (audioLibrary == null)
            {
                InitializeAudioSystem();
            }

            // 双重检查，确保初始化成功
            if (audioLibrary == null)
            {
                if (showDebugInfo)
                {
                    Debug.LogError("AudioManager: 音频库初始化失败");
                }
                return;
            }

            AudioClip clip = audioLibrary.GetBackgroundMusic(musicName);
            if (clip != null)
            {
                PlayBackgroundMusic(clip);
            }
            else if (showDebugInfo)
            {
                Debug.LogWarning($"背景音乐未找到: {musicName}");
            }
        }
        
        public void StopBackgroundMusic()
        {
            if (musicFadeCoroutine != null)
            {
                StopCoroutine(musicFadeCoroutine);
            }
            
            musicFadeCoroutine = StartCoroutine(FadeOutMusic());
        }
        
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            PlayerPrefs.SetFloat(MASTER_VOLUME_KEY, masterVolume);

            // 确保音频系统已初始化（防止在测试环境中出现空引用）
            if (sfxAudioSources == null || musicAudioSource == null)
            {
                InitializeAudioSystem();
            }

            ApplyVolumeSettings();
        }
        
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            PlayerPrefs.SetFloat(SFX_VOLUME_KEY, sfxVolume);

            // 确保音频系统已初始化（防止在测试环境中出现空引用）
            if (sfxAudioSources == null || musicAudioSource == null)
            {
                InitializeAudioSystem();
            }

            ApplyVolumeSettings();
        }

        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            PlayerPrefs.SetFloat(MUSIC_VOLUME_KEY, musicVolume);

            // 确保音频系统已初始化（防止在测试环境中出现空引用）
            if (sfxAudioSources == null || musicAudioSource == null)
            {
                InitializeAudioSystem();
            }

            ApplyVolumeSettings();
        }
        
        #endregion
        
        /// <summary>
        /// 播放音效（使用AudioClip）
        /// </summary>
        public void PlaySoundEffect(AudioClip clip, float volume = 1f, float pitch = 1f)
        {
            if (clip == null) return;
            
            AudioSource audioSource = GetAvailableAudioSource();
            if (audioSource != null)
            {
                audioSource.clip = clip;
                audioSource.volume = volume * sfxVolume * masterVolume;
                audioSource.pitch = pitch;
                audioSource.Play();
                
                OnSoundEffectPlayed?.Invoke(clip.name);
                
                if (enableAudioPooling)
                {
                    StartCoroutine(ReturnToPoolWhenFinished(audioSource, clip.length / pitch));
                }
                
                if (showDebugInfo)
                {
                    Debug.Log($"播放音效: {clip.name}");
                }
            }
        }
        
        /// <summary>
        /// 播放背景音乐（使用AudioClip）
        /// </summary>
        public void PlayBackgroundMusic(AudioClip clip, bool loop = true, bool fadeIn = true)
        {
            if (clip == null) return;

            // 确保音频系统已初始化（防止在测试环境中Awake未被调用）
            if (musicAudioSource == null)
            {
                InitializeAudioSystem();
            }

            if (musicAudioSource == null) return;
            
            // 如果正在播放相同的音乐，不重复播放
            if (currentMusicTrack == clip.name && musicAudioSource.isPlaying)
            {
                return;
            }
            
            // 停止当前音乐淡出协程
            if (musicFadeCoroutine != null)
            {
                StopCoroutine(musicFadeCoroutine);
            }
            
            currentMusicTrack = clip.name;
            musicAudioSource.clip = clip;
            musicAudioSource.loop = loop;
            
            if (fadeIn)
            {
                musicFadeCoroutine = StartCoroutine(FadeInMusic());
            }
            else
            {
                musicAudioSource.volume = musicVolume * masterVolume;
                musicAudioSource.Play();
            }
            
            OnMusicStarted?.Invoke(clip.name);
            
            if (showDebugInfo)
            {
                Debug.Log($"播放背景音乐: {clip.name}");
            }
        }
        
        /// <summary>
        /// 获取可用的音频源
        /// </summary>
        private AudioSource GetAvailableAudioSource()
        {
            // 确保音频系统已初始化（防止在测试环境中Awake未被调用）
            if ((enableAudioPooling && audioSourcePool == null) ||
                (!enableAudioPooling && sfxAudioSources == null))
            {
                InitializeAudioSystem();
            }

            if (enableAudioPooling)
            {
                return GetPooledAudioSource();
            }
            else
            {
                return GetStaticAudioSource();
            }
        }
        
        /// <summary>
        /// 从池中获取音频源
        /// </summary>
        private AudioSource GetPooledAudioSource()
        {
            if (audioSourcePool.Count > 0)
            {
                AudioSource audioSource = audioSourcePool.Dequeue();
                audioSource.gameObject.SetActive(true);
                activeAudioSources.Add(audioSource);
                return audioSource;
            }
            
            // 如果池为空，尝试回收已完成的音频源
            for (int i = activeAudioSources.Count - 1; i >= 0; i--)
            {
                if (!activeAudioSources[i].isPlaying)
                {
                    AudioSource audioSource = activeAudioSources[i];
                    activeAudioSources.RemoveAt(i);
                    return audioSource;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 获取静态音频源
        /// </summary>
        private AudioSource GetStaticAudioSource()
        {
            foreach (var audioSource in sfxAudioSources)
            {
                if (!audioSource.isPlaying)
                {
                    return audioSource;
                }
            }
            
            // 如果所有音频源都在使用，使用第一个
            return sfxAudioSources[0];
        }
        
        /// <summary>
        /// 音频播放完毕后返回池中
        /// </summary>
        private IEnumerator ReturnToPoolWhenFinished(AudioSource audioSource, float duration)
        {
            yield return new WaitForSeconds(duration);
            
            if (activeAudioSources.Contains(audioSource))
            {
                activeAudioSources.Remove(audioSource);
                audioSource.gameObject.SetActive(false);
                audioSourcePool.Enqueue(audioSource);
            }
        }
        
        /// <summary>
        /// 音乐淡入
        /// </summary>
        private IEnumerator FadeInMusic()
        {
            musicAudioSource.volume = 0f;
            musicAudioSource.Play();
            
            float elapsedTime = 0f;
            float targetVolume = musicVolume * masterVolume;
            
            while (elapsedTime < musicFadeDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / musicFadeDuration;
                float curveValue = fadeInCurve.Evaluate(progress);
                
                musicAudioSource.volume = targetVolume * curveValue;
                
                yield return null;
            }
            
            musicAudioSource.volume = targetVolume;
            musicFadeCoroutine = null;
        }
        
        /// <summary>
        /// 音乐淡出
        /// </summary>
        private IEnumerator FadeOutMusic()
        {
            float startVolume = musicAudioSource.volume;
            float elapsedTime = 0f;
            
            while (elapsedTime < musicFadeDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / musicFadeDuration;
                float curveValue = fadeOutCurve.Evaluate(progress);
                
                musicAudioSource.volume = startVolume * curveValue;
                
                yield return null;
            }
            
            musicAudioSource.volume = 0f;
            musicAudioSource.Stop();
            currentMusicTrack = "";
            
            OnMusicStopped?.Invoke(currentMusicTrack);
            musicFadeCoroutine = null;
        }
        
        #region 静态实例管理
        
        private static AudioManager instance;
        public static AudioManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<AudioManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("AudioManager");
                        instance = go.AddComponent<AudioManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
}
