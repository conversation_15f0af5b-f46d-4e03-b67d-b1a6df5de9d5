using UnityEngine;
using System.Collections;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 收集品控制器
    /// 处理收集品的行为、动画和交互
    /// </summary>
    [RequireComponent(typeof(Collider2D), typeof(SpriteRenderer))]
    public class Collectible : MonoBehaviour
    {
        [Header("收集品数据")]
        [SerializeField] private CollectibleData collectibleData;
        
        [Header("动画设置")]
        [SerializeField] private bool enableFloating = true;
        [SerializeField] private float floatAmplitude = 0.5f;
        [SerializeField] private float floatSpeed = 2f;
        [SerializeField] private bool enableRotation = true;
        [SerializeField] private float rotationSpeed = 90f;
        
        [Header("收集效果")]
        [SerializeField] private bool enableCollectAnimation = true;
        [SerializeField] private float collectAnimationDuration = 0.5f;
        [SerializeField] private AnimationCurve collectScaleCurve = AnimationCurve.EaseInOut(0, 1, 1, 0);
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private SpriteRenderer spriteRenderer;
        private Collider2D itemCollider;
        private AudioSource audioSource;
        
        // 状态
        private Vector3 initialPosition;
        private bool isCollected = false;
        private float animationTimer = 0f;
        
        // 事件
        public System.Action<CollectibleData> OnCollected;
        public System.Action<Collectible> OnCollectionComplete;
        
        // 属性
        public CollectibleData Data => collectibleData;
        public bool IsCollected => isCollected;
        
        private void Awake()
        {
            InitializeComponents();
            ValidateData();
        }
        
        private void Start()
        {
            InitializeVisuals();
            initialPosition = transform.position;
        }
        
        private void Update()
        {
            if (isCollected) return;
            
            UpdateAnimations();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
            itemCollider = GetComponent<Collider2D>();
            audioSource = GetComponent<AudioSource>();
            
            // 设置碰撞器为触发器
            if (itemCollider != null)
            {
                itemCollider.isTrigger = true;
            }
            
            // 配置音频源
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 0f; // 2D音效
        }
        
        /// <summary>
        /// 验证数据
        /// </summary>
        private void ValidateData()
        {
            if (collectibleData == null)
            {
                Debug.LogWarning($"收集品数据为空: {gameObject.name}");
                collectibleData = new CollectibleData();
            }
            
            if (!collectibleData.IsValid())
            {
                Debug.LogWarning($"收集品数据无效: {gameObject.name}");
            }
        }
        
        /// <summary>
        /// 初始化视觉效果
        /// </summary>
        private void InitializeVisuals()
        {
            if (spriteRenderer != null && collectibleData.icon != null)
            {
                spriteRenderer.sprite = collectibleData.icon;
            }
            
            // 设置发光效果
            if (collectibleData.glowColor != Color.clear)
            {
                spriteRenderer.color = collectibleData.glowColor;
            }
        }
        
        /// <summary>
        /// 更新动画
        /// </summary>
        private void UpdateAnimations()
        {
            animationTimer += Time.deltaTime * collectibleData.animationSpeed;
            
            // 浮动动画
            if (enableFloating)
            {
                Vector3 pos = initialPosition;
                pos.y += Mathf.Sin(animationTimer * floatSpeed) * floatAmplitude;
                transform.position = pos;
            }
            
            // 旋转动画
            if (enableRotation)
            {
                transform.Rotate(0, 0, rotationSpeed * Time.deltaTime);
            }
        }
        
        /// <summary>
        /// 收集物品
        /// </summary>
        /// <param name="collector">收集者对象</param>
        public void Collect(GameObject collector)
        {
            if (isCollected) return;

            // 标记为已收集
            isCollected = true;

            if (showDebugInfo)
            {
                Debug.Log($"收集物品: {collectibleData.itemName}, 价值: {collectibleData.GetTotalValue()}");
            }

            // 触发收集事件
            OnCollected?.Invoke(collectibleData);

            // 播放音效
            PlayCollectSound();

            // 播放收集动画
            if (enableCollectAnimation)
            {
                StartCoroutine(PlayCollectAnimation());
            }
            else
            {
                CompleteCollection();
            }
        }

        /// <summary>
        /// 播放收集音效
        /// </summary>
        private void PlayCollectSound()
        {
            // 优先使用音频事件系统
            if (AudioEventSystem.Instance != null)
            {
                AudioEventSystem.Instance.PlayCollectibleSound(collectibleData.itemName);
            }
            // 备用方案：直接播放音效
            else if (audioSource != null && collectibleData.collectSound != null)
            {
                audioSource.clip = collectibleData.collectSound;
                audioSource.volume = collectibleData.soundVolume;
                audioSource.Play();
            }
        }
        
        /// <summary>
        /// 播放收集动画
        /// </summary>
        private IEnumerator PlayCollectAnimation()
        {
            // 禁用碰撞器防止重复收集
            if (itemCollider != null)
            {
                itemCollider.enabled = false;
            }
            
            Vector3 originalScale = transform.localScale;
            Vector3 originalPosition = transform.position;
            
            float elapsedTime = 0f;
            
            while (elapsedTime < collectAnimationDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / collectAnimationDuration;
                
                // 缩放动画
                float scaleMultiplier = collectScaleCurve.Evaluate(progress);
                transform.localScale = originalScale * scaleMultiplier;
                
                // 上升动画
                Vector3 pos = originalPosition;
                pos.y += progress * 2f; // 向上移动
                transform.position = pos;
                
                // 透明度动画
                if (spriteRenderer != null)
                {
                    Color color = spriteRenderer.color;
                    color.a = 1f - progress;
                    spriteRenderer.color = color;
                }
                
                yield return null;
            }
            
            CompleteCollection();
        }
        
        /// <summary>
        /// 完成收集
        /// </summary>
        private void CompleteCollection()
        {
            OnCollectionComplete?.Invoke(this);
            
            // 销毁对象
            Destroy(gameObject);
        }
        
        /// <summary>
        /// 设置收集品数据
        /// </summary>
        public void SetData(CollectibleData data)
        {
            collectibleData = data;
            ValidateData();
            
            if (Application.isPlaying)
            {
                InitializeVisuals();
            }
        }
        
        #region Unity事件
        
        private void OnTriggerEnter2D(Collider2D other)
        {
            if (isCollected) return;
            
            // 检查是否是玩家
            if (other.CompareTag("Player"))
            {
                Collect(other.gameObject);
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!showDebugInfo) return;
            
            // 绘制收集范围
            Gizmos.color = Color.green;
            if (itemCollider != null && itemCollider is CircleCollider2D circleCollider)
            {
                Gizmos.DrawWireSphere(transform.position, circleCollider.radius);
            }
            else if (itemCollider != null && itemCollider is BoxCollider2D boxCollider)
            {
                Gizmos.DrawWireCube(transform.position, boxCollider.size);
            }
        }
        
        #endregion
    }
}