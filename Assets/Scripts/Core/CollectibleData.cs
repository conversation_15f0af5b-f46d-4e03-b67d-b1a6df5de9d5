using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 收集品类型枚举
    /// </summary>
    public enum CollectibleType
    {
        Coin,           // 金币
        Gem,            // 宝石
        PowerUp,        // 能力提升
        HealthPotion,   // 生命药水
        Key,            // 钥匙
        Artifact        // 神器
    }

    /// <summary>
    /// 收集品数据结构
    /// </summary>
    [System.Serializable]
    public class CollectibleData
    {
        [Header("基础信息")]
        public CollectibleType type = CollectibleType.Coin;
        public string itemName = "Coin";
        public string description = "A shiny coin";
        
        [Header("数值属性")]
        public int value = 10;              // 分数值
        public int quantity = 1;            // 数量
        public bool isConsumable = true;    // 是否消耗品
        
        [Header("视觉效果")]
        public Sprite icon;                 // 图标
        public Color glowColor = Color.yellow;  // 发光颜色
        public float animationSpeed = 1f;   // 动画速度
        
        [Header("音效")]
        public AudioClip collectSound;      // 收集音效
        public float soundVolume = 1f;      // 音量
        
        [Header("特殊效果")]
        public bool hasSpecialEffect = false;   // 是否有特殊效果
        public float effectDuration = 0f;       // 效果持续时间
        public int healthRestore = 0;           // 恢复生命值
        public float speedBoost = 0f;           // 速度提升
        
        /// <summary>
        /// 验证数据有效性
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(itemName) && 
                   value >= 0 && 
                   quantity > 0 && 
                   soundVolume >= 0f && soundVolume <= 1f;
        }
        
        /// <summary>
        /// 获取显示文本
        /// </summary>
        public string GetDisplayText()
        {
            if (quantity > 1)
                return $"{itemName} x{quantity}";
            return itemName;
        }
        
        /// <summary>
        /// 获取总价值
        /// </summary>
        public int GetTotalValue()
        {
            return value * quantity;
        }
    }
}