using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 收集品系统使用示例
    /// 展示如何在游戏中集成和使用收集品系统
    /// </summary>
    public class CollectibleSystemExample : MonoBehaviour
    {
        [Header("收集品预制体")]
        [SerializeField] private GameObject coinPrefab;
        [SerializeField] private GameObject gemPrefab;
        [SerializeField] private GameObject healthPotionPrefab;
        [SerializeField] private GameObject powerUpPrefab;
        
        [Header("生成设置")]
        [SerializeField] private int coinsToSpawn = 10;
        [SerializeField] private int gemsToSpawn = 5;
        [SerializeField] private int healthPotionsToSpawn = 3;
        [SerializeField] private int powerUpsToSpawn = 2;
        [SerializeField] private Vector2 spawnArea = new Vector2(20f, 10f);
        
        [Header("收集品数据模板")]
        [SerializeField] private CollectibleData coinData;
        [SerializeField] private CollectibleData gemData;
        [SerializeField] private CollectibleData healthPotionData;
        [SerializeField] private CollectibleData powerUpData;
        
        [Header("UI反馈")]
        [SerializeField] private UnityEngine.UI.Text scoreText;
        [SerializeField] private UnityEngine.UI.Text comboText;
        [SerializeField] private UnityEngine.UI.Text inventoryText;
        
        [Header("调试设置")]
        [SerializeField] private bool autoSpawnOnStart = true;
        [SerializeField] private bool showDebugInfo = true;
        
        // 管理器引用
        private ScoreManager scoreManager;
        private InventoryManager inventoryManager;
        
        // 生成的收集品列表
        private List<GameObject> spawnedCollectibles;
        
        private void Start()
        {
            InitializeSystem();
            
            if (autoSpawnOnStart)
            {
                SpawnAllCollectibles();
            }
        }
        
        private void Update()
        {
            UpdateUI();
        }
        
        /// <summary>
        /// 初始化系统
        /// </summary>
        private void InitializeSystem()
        {
            // 获取或创建管理器
            scoreManager = ScoreManager.Instance;
            inventoryManager = InventoryManager.Instance;
            
            // 初始化收集品列表
            spawnedCollectibles = new List<GameObject>();
            
            // 注册事件
            RegisterEvents();
            
            // 初始化收集品数据模板
            InitializeCollectibleData();
            
            if (showDebugInfo)
            {
                Debug.Log("收集品系统初始化完成");
            }
        }
        
        /// <summary>
        /// 注册事件
        /// </summary>
        private void RegisterEvents()
        {
            if (scoreManager != null)
            {
                scoreManager.OnScoreChanged += OnScoreChanged;
                scoreManager.OnComboChanged += OnComboChanged;
                scoreManager.OnHighScoreChanged += OnHighScoreChanged;
                scoreManager.OnItemCollected += OnItemCollected;
            }
            
            if (inventoryManager != null)
            {
                inventoryManager.OnItemAdded += OnItemAdded;
                inventoryManager.OnItemUsed += OnItemUsed;
                inventoryManager.OnInventoryFull += OnInventoryFull;
            }
        }
        
        /// <summary>
        /// 初始化收集品数据模板
        /// </summary>
        private void InitializeCollectibleData()
        {
            // 金币数据
            if (coinData == null)
            {
                coinData = new CollectibleData
                {
                    type = CollectibleType.Coin,
                    itemName = "金币",
                    description = "闪闪发光的金币",
                    value = 10,
                    quantity = 1,
                    isConsumable = false,
                    glowColor = Color.yellow,
                    animationSpeed = 1f,
                    soundVolume = 0.8f
                };
            }
            
            // 宝石数据
            if (gemData == null)
            {
                gemData = new CollectibleData
                {
                    type = CollectibleType.Gem,
                    itemName = "宝石",
                    description = "珍贵的宝石",
                    value = 50,
                    quantity = 1,
                    isConsumable = false,
                    glowColor = Color.blue,
                    animationSpeed = 1.5f,
                    soundVolume = 1f
                };
            }
            
            // 生命药水数据
            if (healthPotionData == null)
            {
                healthPotionData = new CollectibleData
                {
                    type = CollectibleType.HealthPotion,
                    itemName = "生命药水",
                    description = "恢复生命值的药水",
                    value = 0,
                    quantity = 1,
                    isConsumable = true,
                    hasSpecialEffect = true,
                    healthRestore = 25,
                    glowColor = Color.red,
                    animationSpeed = 0.8f,
                    soundVolume = 0.9f
                };
            }
            
            // 能力提升数据
            if (powerUpData == null)
            {
                powerUpData = new CollectibleData
                {
                    type = CollectibleType.PowerUp,
                    itemName = "速度提升",
                    description = "临时提升移动速度",
                    value = 20,
                    quantity = 1,
                    isConsumable = true,
                    hasSpecialEffect = true,
                    speedBoost = 2f,
                    effectDuration = 10f,
                    glowColor = Color.green,
                    animationSpeed = 2f,
                    soundVolume = 1f
                };
            }
        }
        
        /// <summary>
        /// 生成所有收集品
        /// </summary>
        [ContextMenu("生成所有收集品")]
        public void SpawnAllCollectibles()
        {
            ClearAllCollectibles();
            
            SpawnCollectibles(coinPrefab, coinData, coinsToSpawn);
            SpawnCollectibles(gemPrefab, gemData, gemsToSpawn);
            SpawnCollectibles(healthPotionPrefab, healthPotionData, healthPotionsToSpawn);
            SpawnCollectibles(powerUpPrefab, powerUpData, powerUpsToSpawn);
            
            if (showDebugInfo)
            {
                Debug.Log($"生成收集品完成，总数: {spawnedCollectibles.Count}");
            }
        }
        
        /// <summary>
        /// 生成指定类型的收集品
        /// </summary>
        private void SpawnCollectibles(GameObject prefab, CollectibleData data, int count)
        {
            if (prefab == null || data == null) return;
            
            for (int i = 0; i < count; i++)
            {
                Vector3 spawnPosition = GetRandomSpawnPosition();
                GameObject collectibleObj = Instantiate(prefab, spawnPosition, Quaternion.identity);
                
                // 设置收集品数据
                Collectible collectible = collectibleObj.GetComponent<Collectible>();
                if (collectible == null)
                {
                    collectible = collectibleObj.AddComponent<Collectible>();
                }
                
                collectible.SetData(data);
                
                // 注册收集完成事件
                collectible.OnCollectionComplete += OnCollectibleDestroyed;
                
                spawnedCollectibles.Add(collectibleObj);
            }
        }
        
        /// <summary>
        /// 获取随机生成位置
        /// </summary>
        private Vector3 GetRandomSpawnPosition()
        {
            float x = Random.Range(-spawnArea.x / 2f, spawnArea.x / 2f);
            float y = Random.Range(-spawnArea.y / 2f, spawnArea.y / 2f);
            return transform.position + new Vector3(x, y, 0);
        }
        
        /// <summary>
        /// 清除所有收集品
        /// </summary>
        [ContextMenu("清除所有收集品")]
        public void ClearAllCollectibles()
        {
            foreach (var collectible in spawnedCollectibles)
            {
                if (collectible != null)
                {
                    DestroyImmediate(collectible);
                }
            }
            
            spawnedCollectibles.Clear();
            
            if (showDebugInfo)
            {
                Debug.Log("已清除所有收集品");
            }
        }
        
        /// <summary>
        /// 更新UI显示
        /// </summary>
        private void UpdateUI()
        {
            if (scoreText != null && scoreManager != null)
            {
                scoreText.text = $"分数: {scoreManager.CurrentScore}";
            }
            
            if (comboText != null && scoreManager != null)
            {
                comboText.text = scoreManager.ComboCount > 0 ? $"连击: {scoreManager.ComboCount}" : "";
            }
            
            if (inventoryText != null && inventoryManager != null)
            {
                inventoryText.text = $"库存: {inventoryManager.CurrentSize}/{inventoryManager.MaxSize}";
            }
        }
        
        #region 事件处理
        
        private void OnScoreChanged(int newScore)
        {
            if (showDebugInfo)
            {
                Debug.Log($"分数更新: {newScore}");
            }
        }
        
        private void OnComboChanged(int newCombo)
        {
            if (showDebugInfo && newCombo > 1)
            {
                Debug.Log($"连击: {newCombo}");
            }
        }
        
        private void OnHighScoreChanged(int newHighScore)
        {
            if (showDebugInfo)
            {
                Debug.Log($"新纪录! {newHighScore}");
            }
        }
        
        private void OnItemCollected(CollectibleType type, int quantity)
        {
            if (showDebugInfo)
            {
                Debug.Log($"收集物品: {type} x{quantity}");
            }
        }
        
        private void OnItemAdded(InventoryItem item)
        {
            if (showDebugInfo)
            {
                Debug.Log($"物品添加到库存: {item.data.itemName} x{item.quantity}");
            }
        }
        
        private void OnItemUsed(InventoryItem item)
        {
            if (showDebugInfo)
            {
                Debug.Log($"使用物品: {item.data.itemName}");
            }
        }
        
        private void OnInventoryFull()
        {
            if (showDebugInfo)
            {
                Debug.LogWarning("库存已满!");
            }
        }
        
        private void OnCollectibleDestroyed(Collectible collectible)
        {
            if (spawnedCollectibles.Contains(collectible.gameObject))
            {
                spawnedCollectibles.Remove(collectible.gameObject);
            }
        }
        
        #endregion
        
        #region 调试方法
        
        [ContextMenu("重置分数")]
        public void ResetScore()
        {
            if (scoreManager != null)
            {
                scoreManager.ResetCurrentScore();
            }
        }
        
        [ContextMenu("清空库存")]
        public void ClearInventory()
        {
            if (inventoryManager != null)
            {
                inventoryManager.ClearInventory();
            }
        }
        
        [ContextMenu("显示统计信息")]
        public void ShowStats()
        {
            if (scoreManager != null)
            {
                Debug.Log("=== 分数统计 ===");
                Debug.Log(scoreManager.GetScoreStats());
            }
            
            if (inventoryManager != null)
            {
                Debug.Log("=== 库存统计 ===");
                Debug.Log(inventoryManager.GetInventorySummary());
            }
        }
        
        #endregion
        
        #region Unity事件
        
        private void OnDestroy()
        {
            // 取消注册事件
            if (scoreManager != null)
            {
                scoreManager.OnScoreChanged -= OnScoreChanged;
                scoreManager.OnComboChanged -= OnComboChanged;
                scoreManager.OnHighScoreChanged -= OnHighScoreChanged;
                scoreManager.OnItemCollected -= OnItemCollected;
            }
            
            if (inventoryManager != null)
            {
                inventoryManager.OnItemAdded -= OnItemAdded;
                inventoryManager.OnItemUsed -= OnItemUsed;
                inventoryManager.OnInventoryFull -= OnInventoryFull;
            }
        }
        
        private void OnDrawGizmos()
        {
            // 绘制生成区域
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireCube(transform.position, new Vector3(spawnArea.x, spawnArea.y, 0));
        }
        
        #endregion
    }
}