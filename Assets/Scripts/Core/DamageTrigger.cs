using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 伤害触发器
    /// 用于创建伤害区域，如陷阱、敌人攻击等
    /// </summary>
    [RequireComponent(typeof(Collider2D))]
    public class DamageTrigger : MonoBehaviour
    {
        [Header("伤害设置")]
        [SerializeField] private int damage = 10;
        [SerializeField] private DamageType damageType = DamageType.Physical;
        [SerializeField] private float damageInterval = 1f; // 伤害间隔（持续伤害）
        [SerializeField] private bool dealDamageOnce = true; // 是否只造成一次伤害
        
        [Header("击退设置")]
        [SerializeField] private bool applyKnockback = false;
        [SerializeField] private Vector2 knockbackForce = Vector2.zero;
        [SerializeField] private bool knockbackFromCenter = true; // 从中心向外击退
        [SerializeField] private float knockbackStrength = 5f;
        
        [Header("目标过滤")]
        [SerializeField] private LayerMask targetLayers = -1;
        [SerializeField] private string[] targetTags = { "Player", "Enemy" };
        [SerializeField] private bool damagePlayer = true;
        [SerializeField] private bool damageEnemies = false;
        
        [Header("生命周期")]
        [SerializeField] private bool destroyAfterDamage = false;
        [SerializeField] private float lifetime = 0f; // 0表示永久存在
        [SerializeField] private bool disableAfterDamage = false;
        
        [Header("视觉效果")]
        [SerializeField] private GameObject damageEffect;
        [SerializeField] private bool showDamageArea = true;
        [SerializeField] private Color damageAreaColor = Color.red;
        
        [Header("音效")]
        [SerializeField] private AudioClip damageSound;
        [SerializeField] private float soundVolume = 1f;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private Collider2D damageTrigger;
        private AudioSource audioSource;
        
        // 状态变量
        private Dictionary<GameObject, float> lastDamageTime;
        private bool isActive = true;
        
        // 事件
        public System.Action<GameObject, DamageInfo> OnDamageDealt;
        public System.Action<GameObject> OnTargetEntered;
        public System.Action<GameObject> OnTargetExited;
        
        private void Awake()
        {
            InitializeComponents();
            InitializeData();
        }
        
        private void Start()
        {
            if (lifetime > 0)
            {
                Destroy(gameObject, lifetime);
            }
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            damageTrigger = GetComponent<Collider2D>();
            damageTrigger.isTrigger = true;
            
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null && damageSound != null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.spatialBlend = 0f; // 2D音效
            }
        }
        
        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            lastDamageTime = new Dictionary<GameObject, float>();
        }
        
        /// <summary>
        /// 检查目标是否有效
        /// </summary>
        private bool IsValidTarget(GameObject target)
        {
            if (!isActive) return false;
            
            // 检查层级
            if ((targetLayers.value & (1 << target.layer)) == 0) return false;
            
            // 检查标签
            bool hasValidTag = false;
            foreach (string tag in targetTags)
            {
                if (target.CompareTag(tag))
                {
                    hasValidTag = true;
                    break;
                }
            }
            
            if (!hasValidTag) return false;
            
            // 检查特定目标类型
            if (target.CompareTag("Player") && !damagePlayer) return false;
            if (target.CompareTag("Enemy") && !damageEnemies) return false;
            
            // 检查是否有Health组件
            Health targetHealth = target.GetComponent<Health>();
            if (targetHealth == null) return false;
            
            // 检查目标是否存活
            if (!targetHealth.IsAlive) return false;
            
            return true;
        }
        
        /// <summary>
        /// 检查是否可以对目标造成伤害
        /// </summary>
        private bool CanDamageTarget(GameObject target)
        {
            if (dealDamageOnce)
            {
                return !lastDamageTime.ContainsKey(target);
            }
            else
            {
                if (!lastDamageTime.ContainsKey(target))
                {
                    return true;
                }
                
                return Time.time - lastDamageTime[target] >= damageInterval;
            }
        }
        
        /// <summary>
        /// 对目标造成伤害
        /// </summary>
        private void DealDamageToTarget(GameObject target)
        {
            Health targetHealth = target.GetComponent<Health>();
            if (targetHealth == null) return;
            
            // 计算击退力
            Vector2 finalKnockbackForce = knockbackForce;
            if (applyKnockback && knockbackFromCenter)
            {
                Vector2 direction = (target.transform.position - transform.position).normalized;
                finalKnockbackForce = direction * knockbackStrength;
            }
            
            // 创建伤害信息
            DamageInfo damageInfo = new DamageInfo(
                damage,
                damageType,
                finalKnockbackForce,
                0f,
                gameObject
            );
            
            // 造成伤害
            bool damageDealt = targetHealth.TakeDamage(damageInfo);
            
            if (damageDealt)
            {
                // 记录伤害时间
                lastDamageTime[target] = Time.time;
                
                // 触发事件
                OnDamageDealt?.Invoke(target, damageInfo);
                
                // 播放音效
                PlayDamageSound();
                
                // 生成视觉效果
                SpawnDamageEffect(target.transform.position);
                
                if (showDebugInfo)
                {
                    Debug.Log($"{gameObject.name} 对 {target.name} 造成 {damage} 点 {damageType} 伤害");
                }
                
                // 处理伤害后的逻辑
                HandlePostDamage();
            }
        }
        
        /// <summary>
        /// 播放伤害音效
        /// </summary>
        private void PlayDamageSound()
        {
            if (audioSource != null && damageSound != null)
            {
                audioSource.clip = damageSound;
                audioSource.volume = soundVolume;
                audioSource.Play();
            }
        }
        
        /// <summary>
        /// 生成伤害效果
        /// </summary>
        private void SpawnDamageEffect(Vector3 position)
        {
            if (damageEffect != null)
            {
                GameObject effect = Instantiate(damageEffect, position, Quaternion.identity);
                
                // 自动销毁效果（如果没有其他脚本管理的话）
                ParticleSystem particles = effect.GetComponent<ParticleSystem>();
                if (particles != null)
                {
                    Destroy(effect, particles.main.duration + particles.main.startLifetime.constantMax);
                }
                else
                {
                    Destroy(effect, 2f); // 默认2秒后销毁
                }
            }
        }
        
        /// <summary>
        /// 处理伤害后的逻辑
        /// </summary>
        private void HandlePostDamage()
        {
            if (destroyAfterDamage)
            {
                Destroy(gameObject);
            }
            else if (disableAfterDamage)
            {
                SetActive(false);
            }
        }
        
        /// <summary>
        /// 设置激活状态
        /// </summary>
        public void SetActive(bool active)
        {
            isActive = active;
            damageTrigger.enabled = active;
        }
        
        /// <summary>
        /// 设置伤害值
        /// </summary>
        public void SetDamage(int newDamage)
        {
            damage = Mathf.Max(0, newDamage);
        }
        
        /// <summary>
        /// 设置伤害类型
        /// </summary>
        public void SetDamageType(DamageType type)
        {
            damageType = type;
        }
        
        /// <summary>
        /// 重置伤害记录
        /// </summary>
        public void ResetDamageHistory()
        {
            lastDamageTime.Clear();
        }
        
        /// <summary>
        /// 立即对范围内所有目标造成伤害
        /// </summary>
        public void DealDamageToAllTargetsInRange()
        {
            List<Collider2D> colliders = new List<Collider2D>();
            ContactFilter2D contactFilter = new ContactFilter2D().NoFilter();
            Physics2D.OverlapCollider(damageTrigger, contactFilter, colliders);

            foreach (var collider in colliders)
            {
                if (IsValidTarget(collider.gameObject) && CanDamageTarget(collider.gameObject))
                {
                    DealDamageToTarget(collider.gameObject);
                }
            }
        }
        
        #region Unity事件
        
        private void OnTriggerEnter2D(Collider2D other)
        {
            if (!IsValidTarget(other.gameObject)) return;
            
            OnTargetEntered?.Invoke(other.gameObject);
            
            if (CanDamageTarget(other.gameObject))
            {
                DealDamageToTarget(other.gameObject);
            }
        }
        
        private void OnTriggerStay2D(Collider2D other)
        {
            if (!dealDamageOnce && IsValidTarget(other.gameObject) && CanDamageTarget(other.gameObject))
            {
                DealDamageToTarget(other.gameObject);
            }
        }
        
        private void OnTriggerExit2D(Collider2D other)
        {
            if (IsValidTarget(other.gameObject))
            {
                OnTargetExited?.Invoke(other.gameObject);
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!showDamageArea) return;
            
            Gizmos.color = damageAreaColor;
            
            Collider2D col = GetComponent<Collider2D>();
            if (col != null)
            {
                if (col is BoxCollider2D boxCol)
                {
                    Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, transform.lossyScale);
                    Gizmos.DrawWireCube(boxCol.offset, boxCol.size);
                }
                else if (col is CircleCollider2D circleCol)
                {
                    Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, transform.lossyScale);
                    Gizmos.DrawWireSphere(circleCol.offset, circleCol.radius);
                }
            }
            
            Gizmos.matrix = Matrix4x4.identity;
        }
        
        #endregion
    }
}