using UnityEngine;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using MobileScrollingGame.UI;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 错误处理和日志记录系统
    /// 统一处理游戏中的错误、异常和日志记录
    /// </summary>
    public class ErrorHandler : MonoBehaviour
    {
        [Header("日志设置")]
        [SerializeField] private bool enableLogging = true;
        [SerializeField] private bool enableFileLogging = true;
        [SerializeField] private bool enableConsoleLogging = true;
        [SerializeField] private LogLevel minimumLogLevel = LogLevel.Info;
        
        [Header("错误处理设置")]
        [SerializeField] private bool enableErrorRecovery = true;
        [SerializeField] private bool showErrorDialog = true;
        [SerializeField] private int maxErrorsPerSession = 50;
        [SerializeField] private float errorCooldownTime = 1f;
        
        [Header("文件设置")]
        [SerializeField] private string logFileName = "game_log.txt";
        [SerializeField] private int maxLogFileSize = 10 * 1024 * 1024; // 10MB
        [SerializeField] private int maxLogFiles = 5;
        
        [Header("调试设置")]
        [SerializeField] private bool enableStackTrace = true;
        [SerializeField] private bool enablePerformanceLogging = false;
        
        /// <summary>
        /// 日志等级
        /// </summary>
        public enum LogLevel
        {
            Debug = 0,
            Info = 1,
            Warning = 2,
            Error = 3,
            Fatal = 4
        }
        
        /// <summary>
        /// 错误类型
        /// </summary>
        public enum ErrorType
        {
            System,
            Gameplay,
            UI,
            Audio,
            Network,
            FileIO,
            Memory,
            Performance
        }
        
        /// <summary>
        /// 日志条目
        /// </summary>
        [Serializable]
        public class LogEntry
        {
            public DateTime timestamp;
            public LogLevel level;
            public ErrorType errorType;
            public string message;
            public string stackTrace;
            public string context;
            
            public LogEntry(LogLevel level, ErrorType errorType, string message, string stackTrace = "", string context = "")
            {
                this.timestamp = DateTime.Now;
                this.level = level;
                this.errorType = errorType;
                this.message = message;
                this.stackTrace = stackTrace;
                this.context = context;
            }
            
            public override string ToString()
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"[{timestamp:yyyy-MM-dd HH:mm:ss}] [{level}] [{errorType}] {message}");
                
                if (!string.IsNullOrEmpty(context))
                {
                    sb.AppendLine($"Context: {context}");
                }
                
                if (!string.IsNullOrEmpty(stackTrace))
                {
                    sb.AppendLine($"Stack Trace:\n{stackTrace}");
                }
                
                return sb.ToString();
            }
        }
        
        // 日志存储
        private List<LogEntry> logEntries = new List<LogEntry>();
        private Queue<LogEntry> recentErrors = new Queue<LogEntry>();
        
        // 错误统计
        private Dictionary<ErrorType, int> errorCounts = new Dictionary<ErrorType, int>();
        private float lastErrorTime = 0f;
        private int errorsThisSession = 0;
        
        // 文件写入
        private string logFilePath;
        private StreamWriter logFileWriter;
        
        // 事件
        public static event Action<LogEntry> OnLogEntry;
        public static event Action<LogEntry> OnError;
        public static event Action<LogEntry> OnFatalError;
        
        // 属性
        public List<LogEntry> LogEntries => new List<LogEntry>(logEntries);
        public Dictionary<ErrorType, int> ErrorCounts => new Dictionary<ErrorType, int>(errorCounts);
        public int ErrorsThisSession => errorsThisSession;
        
        private void Awake()
        {
            InitializeErrorHandler();
        }
        
        private void Start()
        {
            RegisterUnityLogHandler();
        }
        
        private void OnDestroy()
        {
            CleanupErrorHandler();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                FlushLogs();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                FlushLogs();
            }
        }
        
        /// <summary>
        /// 初始化错误处理器
        /// </summary>
        private void InitializeErrorHandler()
        {
            // 初始化错误计数
            foreach (ErrorType errorType in Enum.GetValues(typeof(ErrorType)))
            {
                errorCounts[errorType] = 0;
            }
            
            // 设置日志文件路径
            if (enableFileLogging)
            {
                SetupLogFile();
            }
            
            // 注册应用程序异常处理
            Application.logMessageReceived += HandleUnityLog;
            
            Log(LogLevel.Info, ErrorType.System, "错误处理系统已初始化");
        }
        
        /// <summary>
        /// 设置日志文件
        /// </summary>
        private void SetupLogFile()
        {
            try
            {
                string logDirectory = Path.Combine(Application.persistentDataPath, "Logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
                
                logFilePath = Path.Combine(logDirectory, logFileName);
                
                // 检查文件大小并轮转
                RotateLogFileIfNeeded();
                
                // 打开文件写入器
                logFileWriter = new StreamWriter(logFilePath, true, Encoding.UTF8);
                logFileWriter.AutoFlush = true;
                
                // 写入会话开始标记
                logFileWriter.WriteLine($"\n=== 游戏会话开始 {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
            }
            catch (Exception e)
            {
                Debug.LogError($"无法设置日志文件: {e.Message}");
                enableFileLogging = false;
            }
        }
        
        /// <summary>
        /// 轮转日志文件
        /// </summary>
        private void RotateLogFileIfNeeded()
        {
            if (!File.Exists(logFilePath)) return;
            
            FileInfo fileInfo = new FileInfo(logFilePath);
            if (fileInfo.Length > maxLogFileSize)
            {
                // 轮转现有日志文件
                for (int i = maxLogFiles - 1; i > 0; i--)
                {
                    string oldFile = logFilePath.Replace(".txt", $"_{i}.txt");
                    string newFile = logFilePath.Replace(".txt", $"_{i + 1}.txt");
                    
                    if (File.Exists(oldFile))
                    {
                        if (File.Exists(newFile))
                        {
                            File.Delete(newFile);
                        }
                        File.Move(oldFile, newFile);
                    }
                }
                
                // 移动当前文件
                string firstBackup = logFilePath.Replace(".txt", "_1.txt");
                if (File.Exists(firstBackup))
                {
                    File.Delete(firstBackup);
                }
                File.Move(logFilePath, firstBackup);
            }
        }
        
        /// <summary>
        /// 注册Unity日志处理器
        /// </summary>
        private void RegisterUnityLogHandler()
        {
            Application.logMessageReceived += HandleUnityLog;
        }
        
        /// <summary>
        /// 处理Unity日志
        /// </summary>
        private void HandleUnityLog(string logString, string stackTrace, LogType type)
        {
            LogLevel level = ConvertUnityLogType(type);
            ErrorType errorType = DetermineErrorType(logString);
            
            LogEntry entry = new LogEntry(level, errorType, logString, stackTrace);
            ProcessLogEntry(entry);
        }
        
        /// <summary>
        /// 转换Unity日志类型
        /// </summary>
        private LogLevel ConvertUnityLogType(LogType unityLogType)
        {
            switch (unityLogType)
            {
                case LogType.Log: return LogLevel.Info;
                case LogType.Warning: return LogLevel.Warning;
                case LogType.Error: return LogLevel.Error;
                case LogType.Exception: return LogLevel.Fatal;
                case LogType.Assert: return LogLevel.Error;
                default: return LogLevel.Info;
            }
        }
        
        /// <summary>
        /// 确定错误类型
        /// </summary>
        private ErrorType DetermineErrorType(string message)
        {
            string lowerMessage = message.ToLower();
            
            if (lowerMessage.Contains("audio") || lowerMessage.Contains("sound"))
                return ErrorType.Audio;
            if (lowerMessage.Contains("ui") || lowerMessage.Contains("canvas"))
                return ErrorType.UI;
            if (lowerMessage.Contains("network") || lowerMessage.Contains("connection"))
                return ErrorType.Network;
            if (lowerMessage.Contains("file") || lowerMessage.Contains("io"))
                return ErrorType.FileIO;
            if (lowerMessage.Contains("memory") || lowerMessage.Contains("gc"))
                return ErrorType.Memory;
            if (lowerMessage.Contains("performance") || lowerMessage.Contains("fps"))
                return ErrorType.Performance;
            if (lowerMessage.Contains("player") || lowerMessage.Contains("enemy") || lowerMessage.Contains("game"))
                return ErrorType.Gameplay;
            
            return ErrorType.System;
        }
        
        /// <summary>
        /// 处理日志条目
        /// </summary>
        private void ProcessLogEntry(LogEntry entry)
        {
            // 检查日志等级
            if (entry.level < minimumLogLevel) return;
            
            // 添加到日志列表
            logEntries.Add(entry);
            
            // 限制日志条目数量
            if (logEntries.Count > 1000)
            {
                logEntries.RemoveAt(0);
            }
            
            // 更新错误统计
            if (entry.level >= LogLevel.Error)
            {
                errorCounts[entry.errorType]++;
                errorsThisSession++;
                recentErrors.Enqueue(entry);
                
                // 限制最近错误队列大小
                if (recentErrors.Count > 10)
                {
                    recentErrors.Dequeue();
                }
                
                // 触发错误事件
                OnError?.Invoke(entry);
                
                if (entry.level == LogLevel.Fatal)
                {
                    OnFatalError?.Invoke(entry);
                    HandleFatalError(entry);
                }
            }
            
            // 写入文件
            if (enableFileLogging && logFileWriter != null)
            {
                try
                {
                    logFileWriter.WriteLine(entry.ToString());
                }
                catch (Exception e)
                {
                    Debug.LogError($"写入日志文件失败: {e.Message}");
                }
            }
            
            // 控制台输出
            if (enableConsoleLogging)
            {
                OutputToConsole(entry);
            }
            
            // 触发日志事件
            OnLogEntry?.Invoke(entry);
        }
        
        /// <summary>
        /// 输出到控制台
        /// </summary>
        private void OutputToConsole(LogEntry entry)
        {
            string message = $"[{entry.level}] [{entry.errorType}] {entry.message}";
            
            switch (entry.level)
            {
                case LogLevel.Debug:
                case LogLevel.Info:
                    Debug.Log(message);
                    break;
                case LogLevel.Warning:
                    Debug.LogWarning(message);
                    break;
                case LogLevel.Error:
                case LogLevel.Fatal:
                    Debug.LogError(message);
                    break;
            }
        }
        
        /// <summary>
        /// 处理致命错误
        /// </summary>
        private void HandleFatalError(LogEntry entry)
        {
            // 立即刷新日志
            FlushLogs();
            
            // 显示错误对话框（如果启用）
            if (showErrorDialog)
            {
                ShowErrorDialog(entry);
            }
            
            // 尝试错误恢复
            if (enableErrorRecovery)
            {
                AttemptErrorRecovery(entry);
            }
        }
        
        /// <summary>
        /// 显示错误对话框
        /// </summary>
        private void ShowErrorDialog(LogEntry entry)
        {
            // 在实际项目中，这里应该显示一个用户友好的错误对话框
            Debug.LogError($"致命错误: {entry.message}");
        }
        
        /// <summary>
        /// 尝试错误恢复
        /// </summary>
        private void AttemptErrorRecovery(LogEntry entry)
        {
            try
            {
                switch (entry.errorType)
                {
                    case ErrorType.Memory:
                        // 内存错误恢复
                        System.GC.Collect();
                        Resources.UnloadUnusedAssets();
                        break;
                        
                    case ErrorType.Audio:
                        // 音频错误恢复
                        if (AudioManager.Instance != null)
                        {
                            AudioManager.Instance.SetMasterVolume(0f);
                        }
                        break;
                        
                    case ErrorType.UI:
                        // UI错误恢复
                        if (UIManager.Instance != null)
                        {
                            UIManager.Instance.SetUIState(UIState.Game);
                        }
                        break;
                }
                
                Log(LogLevel.Info, ErrorType.System, $"尝试从{entry.errorType}错误中恢复");
            }
            catch (Exception e)
            {
                Log(LogLevel.Error, ErrorType.System, $"错误恢复失败: {e.Message}");
            }
        }
        
        /// <summary>
        /// 清理错误处理器
        /// </summary>
        private void CleanupErrorHandler()
        {
            FlushLogs();
            
            if (logFileWriter != null)
            {
                try
                {
                    logFileWriter.WriteLine($"=== 游戏会话结束 {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===\n");
                    logFileWriter.Close();
                    logFileWriter.Dispose();
                }
                catch (Exception e)
                {
                    Debug.LogError($"关闭日志文件失败: {e.Message}");
                }
            }
            
            Application.logMessageReceived -= HandleUnityLog;
        }
        
        /// <summary>
        /// 刷新日志
        /// </summary>
        public void FlushLogs()
        {
            if (logFileWriter != null)
            {
                try
                {
                    logFileWriter.Flush();
                }
                catch (Exception e)
                {
                    Debug.LogError($"刷新日志失败: {e.Message}");
                }
            }
        }
        
        #region 公共API
        
        /// <summary>
        /// 记录日志
        /// </summary>
        public static void Log(LogLevel level, ErrorType errorType, string message, string context = "")
        {
            if (Instance != null)
            {
                string stackTrace = Instance.enableStackTrace ? Environment.StackTrace : "";
                LogEntry entry = new LogEntry(level, errorType, message, stackTrace, context);
                Instance.ProcessLogEntry(entry);
            }
        }
        
        /// <summary>
        /// 记录调试信息
        /// </summary>
        public static void LogDebug(string message, string context = "")
        {
            Log(LogLevel.Debug, ErrorType.System, message, context);
        }
        
        /// <summary>
        /// 记录信息
        /// </summary>
        public static void LogInfo(string message, string context = "")
        {
            Log(LogLevel.Info, ErrorType.System, message, context);
        }
        
        /// <summary>
        /// 记录警告
        /// </summary>
        public static void LogWarning(string message, string context = "")
        {
            Log(LogLevel.Warning, ErrorType.System, message, context);
        }
        
        /// <summary>
        /// 记录错误
        /// </summary>
        public static void LogError(string message, string context = "")
        {
            Log(LogLevel.Error, ErrorType.System, message, context);
        }
        
        /// <summary>
        /// 记录致命错误
        /// </summary>
        public static void LogFatal(string message, string context = "")
        {
            Log(LogLevel.Fatal, ErrorType.System, message, context);
        }
        
        /// <summary>
        /// 获取错误统计
        /// </summary>
        public Dictionary<ErrorType, int> GetErrorStatistics()
        {
            return new Dictionary<ErrorType, int>(errorCounts);
        }
        
        /// <summary>
        /// 获取最近错误
        /// </summary>
        public LogEntry[] GetRecentErrors()
        {
            return recentErrors.ToArray();
        }
        
        /// <summary>
        /// 清除日志
        /// </summary>
        public void ClearLogs()
        {
            logEntries.Clear();
            recentErrors.Clear();
            
            foreach (ErrorType errorType in Enum.GetValues(typeof(ErrorType)))
            {
                errorCounts[errorType] = 0;
            }
            
            errorsThisSession = 0;
        }
        
        /// <summary>
        /// 导出日志
        /// </summary>
        public string ExportLogs()
        {
            StringBuilder sb = new StringBuilder();
            
            sb.AppendLine("=== 游戏日志导出 ===");
            sb.AppendLine($"导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"Unity版本: {Application.unityVersion}");
            sb.AppendLine($"平台: {Application.platform}");
            sb.AppendLine($"设备: {SystemInfo.deviceModel}");
            sb.AppendLine();
            
            sb.AppendLine("=== 错误统计 ===");
            foreach (var kvp in errorCounts)
            {
                sb.AppendLine($"{kvp.Key}: {kvp.Value}");
            }
            sb.AppendLine();
            
            sb.AppendLine("=== 日志条目 ===");
            foreach (var entry in logEntries)
            {
                sb.AppendLine(entry.ToString());
                sb.AppendLine();
            }
            
            return sb.ToString();
        }
        
        #endregion
        
        #region 静态实例管理
        
        private static ErrorHandler instance;
        public static ErrorHandler Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<ErrorHandler>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("ErrorHandler");
                        instance = go.AddComponent<ErrorHandler>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        #endregion
    }
}
