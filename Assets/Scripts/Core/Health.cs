using UnityEngine;
using System.Collections;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 伤害类型枚举
    /// </summary>
    public enum DamageType
    {
        Physical,       // 物理伤害
        Fire,          // 火焰伤害
        Ice,           // 冰霜伤害
        Poison,        // 毒素伤害
        Magic,         // 魔法伤害
        Fall           // 坠落伤害
    }

    /// <summary>
    /// 伤害信息结构
    /// </summary>
    [System.Serializable]
    public struct DamageInfo
    {
        public int amount;              // 伤害数值
        public DamageType type;         // 伤害类型
        public Vector2 knockbackForce;  // 击退力
        public float stunDuration;     // 眩晕时间
        public GameObject source;      // 伤害来源
        
        public DamageInfo(int damage, DamageType damageType = DamageType.Physical, 
                         Vector2 knockback = default, float stun = 0f, GameObject src = null)
        {
            amount = damage;
            type = damageType;
            knockbackForce = knockback;
            stunDuration = stun;
            source = src;
        }
    }

    /// <summary>
    /// 生命值组件
    /// 管理角色的生命值、伤害处理、无敌帧和死亡逻辑
    /// </summary>
    public class Health : MonoBehaviour
    {
        [Header("生命值设置")]
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private int currentHealth = 100;
        [SerializeField] private bool canRegenerate = false;
        [SerializeField] private float regenerationRate = 1f;
        [SerializeField] private float regenerationDelay = 5f;
        
        [Header("无敌帧设置")]
        [SerializeField] private float invincibilityDuration = 1f;
        [SerializeField] private bool enableInvincibilityFlash = true;
        [SerializeField] private float flashInterval = 0.1f;
        
        [Header("击退设置")]
        [SerializeField] private bool enableKnockback = true;
        [SerializeField] private float knockbackResistance = 1f;
        
        [Header("死亡设置")]
        [SerializeField] private bool destroyOnDeath = false;
        [SerializeField] private float deathDelay = 2f;
        [SerializeField] private bool canRespawn = true;
        [SerializeField] private Vector3 respawnPosition = Vector3.zero;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool showHealthBar = true;
        
        // 组件引用
        private SpriteRenderer spriteRenderer;
        private Rigidbody2D rb2d;
        private Collider2D entityCollider;
        
        // 状态变量
        private bool isInvincible = false;
        private bool isDead = false;
        private float lastDamageTime = 0f;
        private Coroutine invincibilityCoroutine;
        private Color originalColor;
        
        // 事件
        public System.Action<int, int> OnHealthChanged;
        public System.Action<DamageInfo> OnDamageTaken;
        public System.Action<int> OnHealthRestored;
        public System.Action OnDeath;
        public System.Action OnRespawn;
        public System.Action OnInvincibilityStart;
        public System.Action OnInvincibilityEnd;
        
        // 属性
        public int CurrentHealth => currentHealth;
        public int MaxHealth => maxHealth;
        public float HealthPercentage => maxHealth > 0 ? (float)currentHealth / maxHealth : 0f;
        public bool IsAlive => !isDead && currentHealth > 0;
        public bool IsInvincible => isInvincible;
        public bool IsDead => isDead;
        
        private void Awake()
        {
            InitializeComponents();
            InitializeHealth();
        }
        
        private void Start()
        {
            if (respawnPosition == Vector3.zero)
            {
                respawnPosition = transform.position;
            }
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
            rb2d = GetComponent<Rigidbody2D>();
            entityCollider = GetComponent<Collider2D>();
            
            if (spriteRenderer != null)
            {
                originalColor = spriteRenderer.color;
            }
        }
        
        /// <summary>
        /// 初始化生命值
        /// </summary>
        private void InitializeHealth()
        {
            currentHealth = maxHealth;
            isDead = false;
            isInvincible = false;
            
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        /// <summary>
        /// 受到伤害
        /// </summary>
        public bool TakeDamage(DamageInfo damageInfo)
        {
            if (isDead || isInvincible) return false;
            
            int actualDamage = damageInfo.amount;
            if (actualDamage <= 0) return false;
            
            currentHealth = Mathf.Max(0, currentHealth - actualDamage);
            lastDamageTime = Time.time;
            
            OnDamageTaken?.Invoke(damageInfo);
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            
            if (enableKnockback && damageInfo.knockbackForce != Vector2.zero)
            {
                ApplyKnockback(damageInfo.knockbackForce);
            }
            
            if (invincibilityDuration > 0)
            {
                StartInvincibility();
            }
            
            if (currentHealth <= 0)
            {
                Die();
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"{gameObject.name} 受到 {actualDamage} 点伤害，剩余生命值: {currentHealth}");
            }
            
            return true;
        }
        
        /// <summary>
        /// 受到伤害（简化版本）
        /// </summary>
        public bool TakeDamage(int damage, DamageType type = DamageType.Physical, GameObject source = null)
        {
            DamageInfo damageInfo = new DamageInfo(damage, type, Vector2.zero, 0f, source);
            return TakeDamage(damageInfo);
        }
        
        /// <summary>
        /// 应用击退效果
        /// </summary>
        private void ApplyKnockback(Vector2 knockbackForce)
        {
            if (rb2d == null) return;
            
            Vector2 adjustedForce = knockbackForce * (1f - knockbackResistance);
            rb2d.AddForce(adjustedForce, ForceMode2D.Impulse);
        }
        
        /// <summary>
        /// 恢复生命值
        /// </summary>
        public void RestoreHealth(int amount)
        {
            if (isDead || amount <= 0) return;
            
            int oldHealth = currentHealth;
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            
            if (currentHealth > oldHealth)
            {
                OnHealthRestored?.Invoke(amount);
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
                
                if (showDebugInfo)
                {
                    Debug.Log($"{gameObject.name} 恢复 {amount} 点生命值");
                }
            }
        }
        
        /// <summary>
        /// 开始无敌状态
        /// </summary>
        private void StartInvincibility()
        {
            if (invincibilityCoroutine != null)
            {
                StopCoroutine(invincibilityCoroutine);
            }
            
            invincibilityCoroutine = StartCoroutine(InvincibilityCoroutine());
        }
        
        /// <summary>
        /// 无敌状态协程
        /// </summary>
        private IEnumerator InvincibilityCoroutine()
        {
            isInvincible = true;
            OnInvincibilityStart?.Invoke();
            
            if (enableInvincibilityFlash && spriteRenderer != null)
            {
                StartCoroutine(FlashCoroutine());
            }
            
            yield return new WaitForSeconds(invincibilityDuration);
            
            isInvincible = false;
            OnInvincibilityEnd?.Invoke();
            
            if (spriteRenderer != null)
            {
                spriteRenderer.color = originalColor;
            }
            
            invincibilityCoroutine = null;
        }
        
        /// <summary>
        /// 闪烁效果协程
        /// </summary>
        private IEnumerator FlashCoroutine()
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < invincibilityDuration)
            {
                if (spriteRenderer != null)
                {
                    spriteRenderer.color = Color.red;
                }
                
                yield return new WaitForSeconds(flashInterval);
                
                if (spriteRenderer != null)
                {
                    spriteRenderer.color = originalColor;
                }
                
                yield return new WaitForSeconds(flashInterval);
                elapsedTime += flashInterval * 2f;
            }
        }
        
        /// <summary>
        /// 死亡处理
        /// </summary>
        private void Die()
        {
            if (isDead) return;
            
            isDead = true;
            OnDeath?.Invoke();
            
            if (entityCollider != null)
            {
                entityCollider.enabled = false;
            }
            
            if (rb2d != null)
            {
                rb2d.linearVelocity = Vector2.zero;
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"{gameObject.name} 死亡");
            }
            
            if (destroyOnDeath)
            {
                Destroy(gameObject, deathDelay);
            }
            else if (canRespawn)
            {
                StartCoroutine(RespawnCoroutine());
            }
        }
        
        /// <summary>
        /// 重生协程
        /// </summary>
        private IEnumerator RespawnCoroutine()
        {
            yield return new WaitForSeconds(deathDelay);
            Respawn();
        }
        
        /// <summary>
        /// 重生
        /// </summary>
        public void Respawn()
        {
            if (!canRespawn) return;
            
            isDead = false;
            isInvincible = false;
            currentHealth = maxHealth;
            
            if (entityCollider != null)
            {
                entityCollider.enabled = true;
            }
            
            transform.position = respawnPosition;
            
            if (spriteRenderer != null)
            {
                spriteRenderer.color = originalColor;
            }
            
            OnRespawn?.Invoke();
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            
            if (showDebugInfo)
            {
                Debug.Log($"{gameObject.name} 重生");
            }
        }
        
        /// <summary>
        /// 设置重生位置
        /// </summary>
        public void SetRespawnPosition(Vector3 position)
        {
            respawnPosition = position;
        }
        
        /// <summary>
        /// 强制死亡
        /// </summary>
        public void ForceDeath()
        {
            currentHealth = 0;
            Die();
        }
        
        private void OnDrawGizmos()
        {
            if (!showHealthBar || !Application.isPlaying) return;
            
            Vector3 barPosition = transform.position + Vector3.up * 2f;
            float barWidth = 2f;
            float barHeight = 0.2f;
            
            Gizmos.color = Color.red;
            Gizmos.DrawCube(barPosition, new Vector3(barWidth, barHeight, 0));
            
            Gizmos.color = Color.green;
            float healthWidth = barWidth * HealthPercentage;
            Vector3 healthPosition = barPosition - Vector3.right * (barWidth - healthWidth) * 0.5f;
            Gizmos.DrawCube(healthPosition, new Vector3(healthWidth, barHeight, 0));
        }
    }
}