using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 生命值系统使用示例
    /// 展示如何在游戏中集成和使用生命值系统
    /// </summary>
    public class HealthSystemExample : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private GameObject healthTestPrefab;
        [SerializeField] private GameObject damageTriggerPrefab;
        [SerializeField] private int testDamage = 25;
        [SerializeField] private int testHeal = 15;
        
        [Header("UI显示")]
        [SerializeField] private UnityEngine.UI.Slider healthBar;
        [SerializeField] private UnityEngine.UI.Text healthText;
        [SerializeField] private UnityEngine.UI.Text statusText;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = true;
        
        // 测试对象
        private GameObject testHealthObject;
        private Health testHealth;
        private GameObject testDamageTrigger;
        
        private void Start()
        {
            CreateTestObjects();
            SetupUI();
        }
        
        private void Update()
        {
            UpdateUI();
        }
        
        /// <summary>
        /// 创建测试对象
        /// </summary>
        [ContextMenu("创建测试对象")]
        public void CreateTestObjects()
        {
            // 清理现有对象
            CleanupTestObjects();
            
            // 创建生命值测试对象
            if (healthTestPrefab != null)
            {
                testHealthObject = Instantiate(healthTestPrefab, Vector3.zero, Quaternion.identity);
            }
            else
            {
                testHealthObject = CreateDefaultHealthObject();
            }
            
            testHealth = testHealthObject.GetComponent<Health>();
            if (testHealth == null)
            {
                testHealth = testHealthObject.AddComponent<Health>();
            }
            
            // 注册事件
            RegisterHealthEvents();
            
            // 创建伤害触发器
            if (damageTriggerPrefab != null)
            {
                testDamageTrigger = Instantiate(damageTriggerPrefab, Vector3.right * 3f, Quaternion.identity);
            }
            else
            {
                testDamageTrigger = CreateDefaultDamageTrigger();
            }
            
            if (showDebugInfo)
            {
                Debug.Log("测试对象创建完成");
            }
        }
        
        /// <summary>
        /// 创建默认生命值对象
        /// </summary>
        private GameObject CreateDefaultHealthObject()
        {
            GameObject obj = new GameObject("TestHealthObject");
            
            // 添加必要组件
            var spriteRenderer = obj.AddComponent<SpriteRenderer>();
            spriteRenderer.sprite = CreateDefaultSprite();
            spriteRenderer.color = Color.green;
            
            obj.AddComponent<Rigidbody2D>();
            
            var collider = obj.AddComponent<BoxCollider2D>();
            collider.size = Vector2.one;
            
            return obj;
        }
        
        /// <summary>
        /// 创建默认伤害触发器
        /// </summary>
        private GameObject CreateDefaultDamageTrigger()
        {
            GameObject obj = new GameObject("TestDamageTrigger");
            obj.transform.position = Vector3.right * 3f;
            
            // 添加视觉组件
            var spriteRenderer = obj.AddComponent<SpriteRenderer>();
            spriteRenderer.sprite = CreateDefaultSprite();
            spriteRenderer.color = Color.red;
            
            // 添加触发器
            var collider = obj.AddComponent<BoxCollider2D>();
            collider.size = Vector2.one * 1.5f;
            collider.isTrigger = true;
            
            var damageTrigger = obj.AddComponent<DamageTrigger>();
            
            // 设置伤害触发器参数
            var damageField = typeof(DamageTrigger).GetField("damage",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            damageField?.SetValue(damageTrigger, testDamage);
            
            var targetLayersField = typeof(DamageTrigger).GetField("targetLayers",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            targetLayersField?.SetValue(damageTrigger, LayerMask.GetMask("Default"));
            
            return obj;
        }
        
        /// <summary>
        /// 创建默认精灵
        /// </summary>
        private Sprite CreateDefaultSprite()
        {
            Texture2D texture = new Texture2D(32, 32);
            Color[] pixels = new Color[32 * 32];
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = Color.white;
            }
            texture.SetPixels(pixels);
            texture.Apply();
            
            return Sprite.Create(texture, new Rect(0, 0, 32, 32), Vector2.one * 0.5f);
        }
        
        /// <summary>
        /// 注册生命值事件
        /// </summary>
        private void RegisterHealthEvents()
        {
            if (testHealth == null) return;
            
            testHealth.OnHealthChanged += OnHealthChanged;
            testHealth.OnDamageTaken += OnDamageTaken;
            testHealth.OnHealthRestored += OnHealthRestored;
            testHealth.OnDeath += OnDeath;
            testHealth.OnRespawn += OnRespawn;
            testHealth.OnInvincibilityStart += OnInvincibilityStart;
            testHealth.OnInvincibilityEnd += OnInvincibilityEnd;
        }
        
        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            if (healthBar != null)
            {
                healthBar.minValue = 0;
                healthBar.maxValue = 100;
                healthBar.value = 100;
            }
        }
        
        /// <summary>
        /// 更新UI显示
        /// </summary>
        private void UpdateUI()
        {
            if (testHealth == null) return;
            
            if (healthBar != null)
            {
                healthBar.value = testHealth.CurrentHealth;
            }
            
            if (healthText != null)
            {
                healthText.text = $"生命值: {testHealth.CurrentHealth}/{testHealth.MaxHealth}";
            }
            
            if (statusText != null)
            {
                string status = "";
                if (testHealth.IsDead)
                    status = "死亡";
                else if (testHealth.IsInvincible)
                    status = "无敌";
                else
                    status = "正常";
                
                statusText.text = $"状态: {status}";
            }
        }
        
        /// <summary>
        /// 清理测试对象
        /// </summary>
        private void CleanupTestObjects()
        {
            if (testHealthObject != null)
            {
                if (testHealth != null)
                {
                    // 取消注册事件
                    testHealth.OnHealthChanged -= OnHealthChanged;
                    testHealth.OnDamageTaken -= OnDamageTaken;
                    testHealth.OnHealthRestored -= OnHealthRestored;
                    testHealth.OnDeath -= OnDeath;
                    testHealth.OnRespawn -= OnRespawn;
                    testHealth.OnInvincibilityStart -= OnInvincibilityStart;
                    testHealth.OnInvincibilityEnd -= OnInvincibilityEnd;
                }
                
                DestroyImmediate(testHealthObject);
                testHealthObject = null;
                testHealth = null;
            }
            
            if (testDamageTrigger != null)
            {
                DestroyImmediate(testDamageTrigger);
                testDamageTrigger = null;
            }
        }
        
        #region 测试方法
        
        [ContextMenu("造成伤害")]
        public void DealTestDamage()
        {
            if (testHealth != null)
            {
                testHealth.TakeDamage(testDamage);
            }
        }
        
        [ContextMenu("恢复生命值")]
        public void RestoreTestHealth()
        {
            if (testHealth != null)
            {
                testHealth.RestoreHealth(testHeal);
            }
        }
        
        [ContextMenu("造成致命伤害")]
        public void DealFatalDamage()
        {
            if (testHealth != null)
            {
                testHealth.TakeDamage(200);
            }
        }
        
        [ContextMenu("完全恢复")]
        public void FullHeal()
        {
            if (testHealth != null)
            {
                testHealth.RestoreHealth(testHealth.MaxHealth);
            }
        }
        
        [ContextMenu("强制死亡")]
        public void ForceDeath()
        {
            if (testHealth != null)
            {
                testHealth.ForceDeath();
            }
        }
        
        [ContextMenu("手动重生")]
        public void ManualRespawn()
        {
            if (testHealth != null)
            {
                testHealth.Respawn();
            }
        }
        
        [ContextMenu("测试击退伤害")]
        public void TestKnockbackDamage()
        {
            if (testHealth != null)
            {
                var damageInfo = new DamageInfo(
                    15,
                    DamageType.Physical,
                    Vector2.right * 10f,
                    0f,
                    gameObject
                );
                
                testHealth.TakeDamage(damageInfo);
            }
        }
        
        [ContextMenu("测试不同伤害类型")]
        public void TestDifferentDamageTypes()
        {
            if (testHealth == null) return;
            
            // 测试不同类型的伤害
            testHealth.TakeDamage(new DamageInfo(10, DamageType.Fire, Vector2.zero, 0f, gameObject));
            
            StartCoroutine(DelayedDamage());
        }
        
        private System.Collections.IEnumerator DelayedDamage()
        {
            yield return new UnityEngine.WaitForSeconds(1.5f);
            
            if (testHealth != null)
            {
                testHealth.TakeDamage(new DamageInfo(10, DamageType.Ice, Vector2.zero, 0f, gameObject));
            }
            
            yield return new UnityEngine.WaitForSeconds(1.5f);
            
            if (testHealth != null)
            {
                testHealth.TakeDamage(new DamageInfo(10, DamageType.Magic, Vector2.zero, 0f, gameObject));
            }
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnHealthChanged(int currentHealth, int maxHealth)
        {
            if (showDebugInfo)
            {
                Debug.Log($"生命值变化: {currentHealth}/{maxHealth} ({(float)currentHealth/maxHealth*100:F1}%)");
            }
        }
        
        private void OnDamageTaken(DamageInfo damageInfo)
        {
            if (showDebugInfo)
            {
                Debug.Log($"受到伤害: {damageInfo.amount} 点 {damageInfo.type} 伤害");
            }
        }
        
        private void OnHealthRestored(int amount)
        {
            if (showDebugInfo)
            {
                Debug.Log($"恢复生命值: {amount} 点");
            }
        }
        
        private void OnDeath()
        {
            if (showDebugInfo)
            {
                Debug.Log("角色死亡!");
            }
        }
        
        private void OnRespawn()
        {
            if (showDebugInfo)
            {
                Debug.Log("角色重生!");
            }
        }
        
        private void OnInvincibilityStart()
        {
            if (showDebugInfo)
            {
                Debug.Log("无敌状态开始");
            }
        }
        
        private void OnInvincibilityEnd()
        {
            if (showDebugInfo)
            {
                Debug.Log("无敌状态结束");
            }
        }
        
        #endregion
        
        #region Unity事件
        
        private void OnDestroy()
        {
            CleanupTestObjects();
        }
        
        private void OnDrawGizmos()
        {
            // 绘制测试区域
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(Vector3.zero, Vector3.one * 10f);
            
            // 绘制生命值对象位置
            if (testHealthObject != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(testHealthObject.transform.position, 0.5f);
            }
            
            // 绘制伤害触发器位置
            if (testDamageTrigger != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(testDamageTrigger.transform.position, Vector3.one * 1.5f);
            }
        }
        
        #endregion
    }
}