using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 库存物品数据
    /// </summary>
    [System.Serializable]
    public class InventoryItem
    {
        public CollectibleData data;
        public int quantity;
        public float acquiredTime;
        
        public InventoryItem(CollectibleData collectibleData, int qty)
        {
            data = collectibleData;
            quantity = qty;
            acquiredTime = Time.time;
        }
        
        public bool IsExpired(float currentTime, float maxAge)
        {
            return maxAge > 0 && (currentTime - acquiredTime) > maxAge;
        }
    }

    /// <summary>
    /// 库存管理器
    /// 处理物品存储、使用和管理
    /// </summary>
    public class InventoryManager : MonoBehaviour
    {
        [Header("库存设置")]
        [SerializeField] private int maxInventorySize = 50;
        [SerializeField] private bool allowDuplicates = true;
        [SerializeField] private float itemExpireTime = 0f; // 0表示不过期
        
        [Header("自动使用设置")]
        [SerializeField] private bool autoUseConsumables = true;
        [SerializeField] private bool autoUseHealthPotions = true;
        [SerializeField] private float healthThreshold = 0.5f; // 生命值低于50%时自动使用
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 库存数据
        private List<InventoryItem> inventory;
        private Dictionary<CollectibleType, int> itemCounts;
        
        // 事件
        public System.Action<InventoryItem> OnItemAdded;
        public System.Action<InventoryItem> OnItemRemoved;
        public System.Action<InventoryItem> OnItemUsed;
        public System.Action OnInventoryChanged;
        public System.Action OnInventoryFull;
        
        // 属性
        public int CurrentSize => inventory.Count;
        public int MaxSize => maxInventorySize;
        public bool IsFull => inventory.Count >= maxInventorySize;
        public List<InventoryItem> Items => new List<InventoryItem>(inventory);
        
        private void Awake()
        {
            InitializeInventory();
        }
        
        private void Start()
        {
            RegisterCollectibleEvents();
        }
        
        private void Update()
        {
            UpdateInventory();
        }
        
        /// <summary>
        /// 初始化库存
        /// </summary>
        private void InitializeInventory()
        {
            inventory = new List<InventoryItem>();
            itemCounts = new Dictionary<CollectibleType, int>();
            
            // 初始化所有物品类型的计数
            foreach (CollectibleType type in System.Enum.GetValues(typeof(CollectibleType)))
            {
                itemCounts[type] = 0;
            }
        }
        
        /// <summary>
        /// 注册收集品事件
        /// </summary>
        private void RegisterCollectibleEvents()
        {
            Collectible[] collectibles = FindObjectsOfType<Collectible>();
            foreach (var collectible in collectibles)
            {
                collectible.OnCollected += HandleItemCollected;
            }
        }
        
        /// <summary>
        /// 更新库存（处理过期物品等）
        /// </summary>
        private void UpdateInventory()
        {
            if (itemExpireTime > 0)
            {
                RemoveExpiredItems();
            }
            
            if (autoUseHealthPotions)
            {
                AutoUseHealthPotions();
            }
        }
        
        /// <summary>
        /// 处理物品收集
        /// </summary>
        private void HandleItemCollected(CollectibleData data)
        {
            if (data == null) return;
            
            // 如果是消耗品且设置了自动使用，直接使用
            if (data.isConsumable && autoUseConsumables && ShouldAutoUse(data))
            {
                UseItemImmediately(data);
                return;
            }
            
            // 否则添加到库存
            AddItem(data, data.quantity);
        }
        
        /// <summary>
        /// 添加物品到库存
        /// </summary>
        public bool AddItem(CollectibleData data, int quantity = 1)
        {
            if (data == null || quantity <= 0) return false;
            
            // 检查库存是否已满
            if (IsFull && !allowDuplicates)
            {
                OnInventoryFull?.Invoke();
                return false;
            }
            
            // 如果允许重复且已存在相同物品，增加数量
            if (allowDuplicates)
            {
                var existingItem = inventory.FirstOrDefault(item => 
                    item.data.type == data.type && item.data.itemName == data.itemName);
                
                if (existingItem != null)
                {
                    existingItem.quantity += quantity;
                    UpdateItemCount(data.type, quantity);
                    OnInventoryChanged?.Invoke();
                    
                    if (showDebugInfo)
                    {
                        Debug.Log($"增加物品数量: {data.itemName}, 新数量: {existingItem.quantity}");
                    }
                    
                    return true;
                }
            }
            
            // 检查是否有空间添加新物品
            if (inventory.Count >= maxInventorySize)
            {
                OnInventoryFull?.Invoke();
                return false;
            }
            
            // 添加新物品
            var newItem = new InventoryItem(data, quantity);
            inventory.Add(newItem);
            UpdateItemCount(data.type, quantity);
            
            OnItemAdded?.Invoke(newItem);
            OnInventoryChanged?.Invoke();
            
            if (showDebugInfo)
            {
                Debug.Log($"添加物品到库存: {data.itemName} x{quantity}");
            }
            
            return true;
        }
        
        /// <summary>
        /// 移除物品
        /// </summary>
        public bool RemoveItem(CollectibleType type, int quantity = 1)
        {
            var item = inventory.FirstOrDefault(i => i.data.type == type);
            if (item == null) return false;
            
            return RemoveItem(item, quantity);
        }
        
        /// <summary>
        /// 移除指定物品
        /// </summary>
        public bool RemoveItem(InventoryItem item, int quantity = 1)
        {
            if (item == null || !inventory.Contains(item)) return false;
            
            if (item.quantity <= quantity)
            {
                // 完全移除物品
                int removedQuantity = item.quantity;
                inventory.Remove(item);
                UpdateItemCount(item.data.type, -removedQuantity);
                
                OnItemRemoved?.Invoke(item);
                OnInventoryChanged?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log($"完全移除物品: {item.data.itemName}");
                }
            }
            else
            {
                // 减少数量
                item.quantity -= quantity;
                UpdateItemCount(item.data.type, -quantity);
                OnInventoryChanged?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log($"减少物品数量: {item.data.itemName}, 剩余: {item.quantity}");
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// 使用物品
        /// </summary>
        public bool UseItem(CollectibleType type, int quantity = 1)
        {
            var item = inventory.FirstOrDefault(i => i.data.type == type);
            if (item == null) return false;
            
            return UseItem(item, quantity);
        }
        
        /// <summary>
        /// 使用指定物品
        /// </summary>
        public bool UseItem(InventoryItem item, int quantity = 1)
        {
            if (item == null || !inventory.Contains(item)) return false;
            
            // 应用物品效果
            ApplyItemEffect(item.data, quantity);
            
            // 如果是消耗品，从库存中移除
            if (item.data.isConsumable)
            {
                RemoveItem(item, quantity);
            }
            
            OnItemUsed?.Invoke(item);
            
            if (showDebugInfo)
            {
                Debug.Log($"使用物品: {item.data.itemName} x{quantity}");
            }
            
            return true;
        }
        
        /// <summary>
        /// 立即使用物品（不添加到库存）
        /// </summary>
        private void UseItemImmediately(CollectibleData data)
        {
            ApplyItemEffect(data, data.quantity);
            
            var tempItem = new InventoryItem(data, data.quantity);
            OnItemUsed?.Invoke(tempItem);
            
            if (showDebugInfo)
            {
                Debug.Log($"立即使用物品: {data.itemName}");
            }
        }
        
        /// <summary>
        /// 应用物品效果
        /// </summary>
        private void ApplyItemEffect(CollectibleData data, int quantity)
        {
            if (!data.hasSpecialEffect) return;
            
            // 根据物品类型应用不同效果
            switch (data.type)
            {
                case CollectibleType.HealthPotion:
                    ApplyHealthRestore(data.healthRestore * quantity);
                    break;
                    
                case CollectibleType.PowerUp:
                    ApplySpeedBoost(data.speedBoost, data.effectDuration);
                    break;
                    
                // 可以添加更多物品效果
            }
        }
        
        /// <summary>
        /// 应用生命值恢复
        /// </summary>
        private void ApplyHealthRestore(int healthAmount)
        {
            // 查找玩家控制器并恢复生命值
            var playerController = FindObjectOfType<MonoBehaviour>() as ICharacterController;
            if (playerController != null)
            {
                // 这里需要实现生命值恢复逻辑
                // playerController.RestoreHealth(healthAmount);
                
                if (showDebugInfo)
                {
                    Debug.Log($"恢复生命值: {healthAmount}");
                }
            }
        }
        
        /// <summary>
        /// 应用速度提升
        /// </summary>
        private void ApplySpeedBoost(float speedBoost, float duration)
        {
            // 这里可以实现速度提升效果
            StartCoroutine(SpeedBoostCoroutine(speedBoost, duration));
        }
        
        /// <summary>
        /// 速度提升协程
        /// </summary>
        private System.Collections.IEnumerator SpeedBoostCoroutine(float speedBoost, float duration)
        {
            if (showDebugInfo)
            {
                Debug.Log($"速度提升开始: +{speedBoost} 持续 {duration}秒");
            }
            
            // 应用速度提升
            // 这里需要与角色控制器集成
            
            yield return new WaitForSeconds(duration);
            
            // 移除速度提升
            if (showDebugInfo)
            {
                Debug.Log("速度提升结束");
            }
        }
        
        /// <summary>
        /// 判断是否应该自动使用物品
        /// </summary>
        private bool ShouldAutoUse(CollectibleData data)
        {
            switch (data.type)
            {
                case CollectibleType.HealthPotion:
                    return ShouldAutoUseHealthPotion();
                    
                case CollectibleType.PowerUp:
                    return true; // 能力提升物品总是自动使用
                    
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 判断是否应该自动使用生命药水
        /// </summary>
        private bool ShouldAutoUseHealthPotion()
        {
            // 这里需要获取玩家当前生命值百分比
            // 暂时返回true作为示例
            return true;
        }
        
        /// <summary>
        /// 自动使用生命药水
        /// </summary>
        private void AutoUseHealthPotions()
        {
            if (!ShouldAutoUseHealthPotion()) return;
            
            var healthPotion = inventory.FirstOrDefault(item => 
                item.data.type == CollectibleType.HealthPotion);
            
            if (healthPotion != null)
            {
                UseItem(healthPotion, 1);
            }
        }
        
        /// <summary>
        /// 移除过期物品
        /// </summary>
        private void RemoveExpiredItems()
        {
            float currentTime = Time.time;
            var expiredItems = inventory.Where(item => 
                item.IsExpired(currentTime, itemExpireTime)).ToList();
            
            foreach (var item in expiredItems)
            {
                RemoveItem(item, item.quantity);
                
                if (showDebugInfo)
                {
                    Debug.Log($"移除过期物品: {item.data.itemName}");
                }
            }
        }
        
        /// <summary>
        /// 更新物品计数
        /// </summary>
        private void UpdateItemCount(CollectibleType type, int change)
        {
            if (itemCounts.ContainsKey(type))
            {
                itemCounts[type] = Mathf.Max(0, itemCounts[type] + change);
            }
        }
        
        /// <summary>
        /// 获取物品数量
        /// </summary>
        public int GetItemCount(CollectibleType type)
        {
            return itemCounts.ContainsKey(type) ? itemCounts[type] : 0;
        }
        
        /// <summary>
        /// 清空库存
        /// </summary>
        public void ClearInventory()
        {
            inventory.Clear();
            
            foreach (var key in new List<CollectibleType>(itemCounts.Keys))
            {
                itemCounts[key] = 0;
            }
            
            OnInventoryChanged?.Invoke();
            
            if (showDebugInfo)
            {
                Debug.Log("库存已清空");
            }
        }
        
        /// <summary>
        /// 获取库存摘要
        /// </summary>
        public string GetInventorySummary()
        {
            var summary = $"库存 ({CurrentSize}/{MaxSize}):\n";
            
            foreach (var item in inventory)
            {
                summary += $"- {item.data.itemName} x{item.quantity}\n";
            }
            
            return summary;
        }
        
        #region 静态实例管理
        
        private static InventoryManager instance;
        public static InventoryManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<InventoryManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("InventoryManager");
                        instance = go.AddComponent<InventoryManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
}