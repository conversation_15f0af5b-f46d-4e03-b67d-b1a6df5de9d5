using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 通用对象池系统
    /// 减少对象创建和销毁的开销，优化垃圾回收
    /// </summary>
    public class ObjectPool : MonoBehaviour
    {
        [System.Serializable]
        public class PoolConfig
        {
            [Header("池配置")]
            public string poolName;
            public GameObject prefab;
            public int initialSize = 10;
            public int maxSize = 100;
            public bool allowGrowth = true;
            public bool preInstantiate = true;
            
            [Header("自动回收设置")]
            public bool enableAutoReturn = false;
            public float autoReturnTime = 5f;
            
            [Header("父对象设置")]
            public bool useParentObject = true;
            public string parentObjectName = "";
        }
        
        [Header("对象池配置")]
        [SerializeField] private List<PoolConfig> poolConfigs = new List<PoolConfig>();
        
        [Header("全局设置")]
        [SerializeField] private bool enablePooling = true;
        [SerializeField] private bool enableStatistics = true;
        [SerializeField] private float cleanupInterval = 30f;
        [SerializeField] private int maxInactiveTime = 60;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool showPoolStatistics = false;
        
        // 对象池字典
        private Dictionary<string, Pool> pools = new Dictionary<string, Pool>();
        private Dictionary<GameObject, PooledObject> activeObjects = new Dictionary<GameObject, PooledObject>();
        
        // 统计信息
        private Dictionary<string, PoolStatistics> statistics = new Dictionary<string, PoolStatistics>();
        
        /// <summary>
        /// 单个对象池
        /// </summary>
        private class Pool
        {
            public PoolConfig config;
            public Queue<PooledObject> availableObjects = new Queue<PooledObject>();
            public List<PooledObject> allObjects = new List<PooledObject>();
            public Transform parentTransform;
            public int currentSize = 0;
            
            public Pool(PoolConfig config, Transform parent)
            {
                this.config = config;
                
                // 创建父对象
                if (config.useParentObject)
                {
                    string parentName = string.IsNullOrEmpty(config.parentObjectName) 
                        ? $"Pool_{config.poolName}" 
                        : config.parentObjectName;
                    
                    GameObject parentObject = new GameObject(parentName);
                    parentObject.transform.SetParent(parent);
                    parentTransform = parentObject.transform;
                }
                else
                {
                    parentTransform = parent;
                }
            }
        }
        
        /// <summary>
        /// 池化对象包装器
        /// </summary>
        private class PooledObject
        {
            public GameObject gameObject;
            public string poolName;
            public float lastActiveTime;
            public bool isActive;
            public Coroutine autoReturnCoroutine;
            
            public PooledObject(GameObject obj, string pool)
            {
                gameObject = obj;
                poolName = pool;
                lastActiveTime = Time.time;
                isActive = false;
            }
        }
        
        /// <summary>
        /// 池统计信息
        /// </summary>
        public class PoolStatistics
        {
            public int totalCreated;
            public int currentActive;
            public int currentInactive;
            public int totalRequests;
            public int totalReturns;
            public float averageActiveTime;
            
            public void Reset()
            {
                totalCreated = 0;
                currentActive = 0;
                currentInactive = 0;
                totalRequests = 0;
                totalReturns = 0;
                averageActiveTime = 0f;
            }
        }
        
        private void Awake()
        {
            InitializeObjectPool();
        }
        
        private void Start()
        {
            CreateInitialPools();
            
            if (cleanupInterval > 0)
            {
                InvokeRepeating(nameof(CleanupInactiveObjects), cleanupInterval, cleanupInterval);
            }
        }
        
        /// <summary>
        /// 初始化对象池系统
        /// </summary>
        private void InitializeObjectPool()
        {
            if (!enablePooling)
            {
                Debug.LogWarning("ObjectPool: 对象池系统已禁用");
                return;
            }
            
            pools.Clear();
            activeObjects.Clear();
            statistics.Clear();
        }
        
        /// <summary>
        /// 创建初始对象池
        /// </summary>
        private void CreateInitialPools()
        {
            foreach (var config in poolConfigs)
            {
                CreatePool(config);
            }
        }
        
        /// <summary>
        /// 创建对象池
        /// </summary>
        public void CreatePool(PoolConfig config)
        {
            if (pools.ContainsKey(config.poolName))
            {
                Debug.LogWarning($"ObjectPool: 池 '{config.poolName}' 已存在");
                return;
            }
            
            if (config.prefab == null)
            {
                Debug.LogError($"ObjectPool: 池 '{config.poolName}' 的预制体为空");
                return;
            }
            
            Pool pool = new Pool(config, transform);
            pools[config.poolName] = pool;
            
            // 初始化统计信息
            if (enableStatistics)
            {
                statistics[config.poolName] = new PoolStatistics();
            }
            
            // 预实例化对象
            if (config.preInstantiate)
            {
                for (int i = 0; i < config.initialSize; i++)
                {
                    CreatePooledObject(pool);
                }
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"ObjectPool: 创建池 '{config.poolName}', 初始大小: {config.initialSize}");
            }
        }
        
        /// <summary>
        /// 创建池化对象
        /// </summary>
        private PooledObject CreatePooledObject(Pool pool)
        {
            GameObject obj = Instantiate(pool.config.prefab, pool.parentTransform);
            obj.SetActive(false);
            
            PooledObject pooledObj = new PooledObject(obj, pool.config.poolName);
            pool.allObjects.Add(pooledObj);
            pool.availableObjects.Enqueue(pooledObj);
            pool.currentSize++;
            
            // 更新统计信息
            if (enableStatistics && statistics.ContainsKey(pool.config.poolName))
            {
                statistics[pool.config.poolName].totalCreated++;
                statistics[pool.config.poolName].currentInactive++;
            }
            
            return pooledObj;
        }
        
        /// <summary>
        /// 从池中获取对象
        /// </summary>
        public GameObject GetObject(string poolName)
        {
            if (!enablePooling)
            {
                // 如果池系统禁用，直接实例化
                var config = poolConfigs.Find(c => c.poolName == poolName);
                if (config != null && config.prefab != null)
                {
                    return Instantiate(config.prefab);
                }
                return null;
            }
            
            if (!pools.ContainsKey(poolName))
            {
                Debug.LogError($"ObjectPool: 池 '{poolName}' 不存在");
                return null;
            }
            
            Pool pool = pools[poolName];
            PooledObject pooledObj = null;
            
            // 尝试从可用对象中获取
            if (pool.availableObjects.Count > 0)
            {
                pooledObj = pool.availableObjects.Dequeue();
            }
            // 如果没有可用对象且允许增长
            else if (pool.config.allowGrowth && pool.currentSize < pool.config.maxSize)
            {
                pooledObj = CreatePooledObject(pool);
                pool.availableObjects.Dequeue(); // 移除刚加入的对象
            }
            
            if (pooledObj != null)
            {
                // 激活对象
                pooledObj.gameObject.SetActive(true);
                pooledObj.isActive = true;
                pooledObj.lastActiveTime = Time.time;
                
                // 添加到活动对象字典
                activeObjects[pooledObj.gameObject] = pooledObj;
                
                // 设置自动回收
                if (pool.config.enableAutoReturn)
                {
                    pooledObj.autoReturnCoroutine = StartCoroutine(AutoReturnObject(pooledObj, pool.config.autoReturnTime));
                }
                
                // 更新统计信息
                if (enableStatistics && statistics.ContainsKey(poolName))
                {
                    var stats = statistics[poolName];
                    stats.totalRequests++;
                    stats.currentActive++;
                    stats.currentInactive--;
                }
                
                if (showDebugInfo)
                {
                    Debug.Log($"ObjectPool: 从池 '{poolName}' 获取对象");
                }
                
                return pooledObj.gameObject;
            }
            
            Debug.LogWarning($"ObjectPool: 无法从池 '{poolName}' 获取对象");
            return null;
        }
        
        /// <summary>
        /// 将对象返回到池中
        /// </summary>
        public void ReturnObject(GameObject obj)
        {
            if (!enablePooling)
            {
                // 如果池系统禁用，直接销毁
                Destroy(obj);
                return;
            }
            
            if (!activeObjects.ContainsKey(obj))
            {
                Debug.LogWarning($"ObjectPool: 对象不属于任何池，直接销毁");
                Destroy(obj);
                return;
            }
            
            PooledObject pooledObj = activeObjects[obj];
            Pool pool = pools[pooledObj.poolName];
            
            // 停止自动回收协程
            if (pooledObj.autoReturnCoroutine != null)
            {
                StopCoroutine(pooledObj.autoReturnCoroutine);
                pooledObj.autoReturnCoroutine = null;
            }
            
            // 重置对象状态
            obj.SetActive(false);
            obj.transform.SetParent(pool.parentTransform);
            obj.transform.localPosition = Vector3.zero;
            obj.transform.localRotation = Quaternion.identity;
            obj.transform.localScale = Vector3.one;
            
            // 更新池化对象状态
            pooledObj.isActive = false;
            pooledObj.lastActiveTime = Time.time;
            
            // 移除活动对象记录
            activeObjects.Remove(obj);
            
            // 返回到可用队列
            pool.availableObjects.Enqueue(pooledObj);
            
            // 更新统计信息
            if (enableStatistics && statistics.ContainsKey(pooledObj.poolName))
            {
                var stats = statistics[pooledObj.poolName];
                stats.totalReturns++;
                stats.currentActive--;
                stats.currentInactive++;
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"ObjectPool: 对象返回到池 '{pooledObj.poolName}'");
            }
        }
        
        /// <summary>
        /// 自动回收对象
        /// </summary>
        private IEnumerator AutoReturnObject(PooledObject pooledObj, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (pooledObj.isActive && activeObjects.ContainsKey(pooledObj.gameObject))
            {
                ReturnObject(pooledObj.gameObject);
            }
        }
        
        /// <summary>
        /// 清理长时间未使用的对象
        /// </summary>
        private void CleanupInactiveObjects()
        {
            float currentTime = Time.time;
            
            foreach (var pool in pools.Values)
            {
                var objectsToRemove = new List<PooledObject>();
                
                foreach (var pooledObj in pool.allObjects)
                {
                    if (!pooledObj.isActive && 
                        currentTime - pooledObj.lastActiveTime > maxInactiveTime &&
                        pool.currentSize > pool.config.initialSize)
                    {
                        objectsToRemove.Add(pooledObj);
                    }
                }
                
                foreach (var objToRemove in objectsToRemove)
                {
                    // 从队列中移除（如果存在）
                    var tempQueue = new Queue<PooledObject>();
                    while (pool.availableObjects.Count > 0)
                    {
                        var obj = pool.availableObjects.Dequeue();
                        if (obj != objToRemove)
                        {
                            tempQueue.Enqueue(obj);
                        }
                    }
                    pool.availableObjects = tempQueue;
                    
                    // 从所有对象列表中移除
                    pool.allObjects.Remove(objToRemove);
                    pool.currentSize--;
                    
                    // 销毁游戏对象
                    if (objToRemove.gameObject != null)
                    {
                        Destroy(objToRemove.gameObject);
                    }
                    
                    // 更新统计信息
                    if (enableStatistics && statistics.ContainsKey(pool.config.poolName))
                    {
                        statistics[pool.config.poolName].currentInactive--;
                    }
                }
                
                if (showDebugInfo && objectsToRemove.Count > 0)
                {
                    Debug.Log($"ObjectPool: 清理池 '{pool.config.poolName}' 中 {objectsToRemove.Count} 个长时间未使用的对象");
                }
            }
        }
        
        /// <summary>
        /// 获取池统计信息
        /// </summary>
        public PoolStatistics GetPoolStatistics(string poolName)
        {
            if (enableStatistics && statistics.ContainsKey(poolName))
            {
                return statistics[poolName];
            }
            return null;
        }
        
        /// <summary>
        /// 获取所有池的统计信息
        /// </summary>
        public Dictionary<string, PoolStatistics> GetAllStatistics()
        {
            return new Dictionary<string, PoolStatistics>(statistics);
        }
        
        /// <summary>
        /// 清空指定池
        /// </summary>
        public void ClearPool(string poolName)
        {
            if (!pools.ContainsKey(poolName))
            {
                Debug.LogWarning($"ObjectPool: 池 '{poolName}' 不存在");
                return;
            }
            
            Pool pool = pools[poolName];
            
            // 销毁所有对象
            foreach (var pooledObj in pool.allObjects)
            {
                if (activeObjects.ContainsKey(pooledObj.gameObject))
                {
                    activeObjects.Remove(pooledObj.gameObject);
                }
                
                if (pooledObj.gameObject != null)
                {
                    Destroy(pooledObj.gameObject);
                }
            }
            
            // 清空池
            pool.allObjects.Clear();
            pool.availableObjects.Clear();
            pool.currentSize = 0;
            
            // 重置统计信息
            if (enableStatistics && statistics.ContainsKey(poolName))
            {
                statistics[poolName].Reset();
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"ObjectPool: 清空池 '{poolName}'");
            }
        }
        
        /// <summary>
        /// 清空所有池
        /// </summary>
        public void ClearAllPools()
        {
            foreach (var poolName in pools.Keys)
            {
                ClearPool(poolName);
            }
        }
        
        /// <summary>
        /// 预热池（预先创建对象）
        /// </summary>
        public void WarmupPool(string poolName, int count)
        {
            if (!pools.ContainsKey(poolName))
            {
                Debug.LogWarning($"ObjectPool: 池 '{poolName}' 不存在");
                return;
            }
            
            Pool pool = pools[poolName];
            
            for (int i = 0; i < count && pool.currentSize < pool.config.maxSize; i++)
            {
                CreatePooledObject(pool);
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"ObjectPool: 预热池 '{poolName}', 创建 {count} 个对象");
            }
        }
        
        #region 调试和统计
        
        private void OnGUI()
        {
            if (!showPoolStatistics || !enableStatistics) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("对象池统计信息", GUI.skin.box);
            
            foreach (var kvp in statistics)
            {
                string poolName = kvp.Key;
                PoolStatistics stats = kvp.Value;
                
                GUILayout.Label($"池: {poolName}");
                GUILayout.Label($"  总创建: {stats.totalCreated}");
                GUILayout.Label($"  当前活动: {stats.currentActive}");
                GUILayout.Label($"  当前非活动: {stats.currentInactive}");
                GUILayout.Label($"  总请求: {stats.totalRequests}");
                GUILayout.Label($"  总返回: {stats.totalReturns}");
                GUILayout.Space(5);
            }
            
            GUILayout.EndArea();
        }
        
        #endregion
        
        #region 静态实例管理
        
        private static ObjectPool instance;
        public static ObjectPool Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<ObjectPool>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("ObjectPool");
                        instance = go.AddComponent<ObjectPool>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
}
