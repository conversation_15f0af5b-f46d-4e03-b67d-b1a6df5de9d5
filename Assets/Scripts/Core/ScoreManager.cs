using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 分数管理器
    /// 处理分数计算、保存和显示
    /// </summary>
    public class ScoreManager : MonoBehaviour
    {
        [Header("分数设置")]
        [SerializeField] private int currentScore = 0;
        [SerializeField] private int highScore = 0;
        [SerializeField] private string highScoreKey = "HighScore";

        [Header("分数倍数")]
        [SerializeField] private float scoreMultiplier = 1f;
        [SerializeField] private float comboMultiplier = 1f;
        [SerializeField] private int comboCount = 0;
        [SerializeField] private float comboResetTime = 3f;

        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;

        // 状态
        private float lastScoreTime;
        private Dictionary<CollectibleType, int> collectedItems;

        // 事件
        public System.Action<int> OnScoreChanged;
        public System.Action<int> OnHighScoreChanged;
        public System.Action<int> OnComboChanged;
        public System.Action<CollectibleType, int> OnItemCollected;
        public System.Action<int> OnMilestoneReached; // 新增里程碑事件

        // 属性
        public int CurrentScore => currentScore;
        public int HighScore => highScore;
        public float ScoreMultiplier => scoreMultiplier;
        public int ComboCount => comboCount;
        public Dictionary<CollectibleType, int> CollectedItems => new Dictionary<CollectibleType, int>(collectedItems);

        private void Awake()
        {
            InitializeData();
            LoadHighScore();
        }

        private void Start()
        {
            // 注册收集品事件
            RegisterCollectibleEvents();
        }

        private void Update()
        {
            UpdateCombo();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            collectedItems = new Dictionary<CollectibleType, int>();

            // 初始化所有收集品类型的计数
            foreach (CollectibleType type in System.Enum.GetValues(typeof(CollectibleType)))
            {
                collectedItems[type] = 0;
            }
        }

        /// <summary>
        /// 注册收集品事件
        /// </summary>
        private void RegisterCollectibleEvents()
        {
            // 查找所有收集品并注册事件
            Collectible[] collectibles = FindObjectsOfType<Collectible>();
            foreach (var collectible in collectibles)
            {
                collectible.OnCollected += HandleItemCollected;
            }
        }

        /// <summary>
        /// 加载最高分
        /// </summary>
        private void LoadHighScore()
        {
            highScore = PlayerPrefs.GetInt(highScoreKey, 0);
        }

        /// <summary>
        /// 保存最高分
        /// </summary>
        private void SaveHighScore()
        {
            PlayerPrefs.SetInt(highScoreKey, highScore);
            PlayerPrefs.Save();
        }

        /// <summary>
        /// 更新连击系统
        /// </summary>
        private void UpdateCombo()
        {
            if (comboCount > 0 && Time.time - lastScoreTime > comboResetTime)
            {
                ResetCombo();
            }
        }

        /// <summary>
        /// 处理物品收集
        /// </summary>
        private void HandleItemCollected(CollectibleData data)
        {
            if (data == null) return;

            // 更新收集品计数
            if (collectedItems.ContainsKey(data.type))
            {
                collectedItems[data.type] += data.quantity;
            }

            // 计算分数
            int baseScore = data.GetTotalValue();
            int finalScore = CalculateScore(baseScore);

            // 添加分数
            AddScore(finalScore);

            // 更新连击
            UpdateComboCount();

            // 触发事件
            OnItemCollected?.Invoke(data.type, data.quantity);

            if (showDebugInfo)
            {
                Debug.Log($"收集物品: {data.itemName}, 基础分数: {baseScore}, 最终分数: {finalScore}, 连击: {comboCount}");
            }
        }

        /// <summary>
        /// 计算分数（包含倍数和连击）
        /// </summary>
        private int CalculateScore(int baseScore)
        {
            float totalMultiplier = scoreMultiplier * comboMultiplier;

            // 连击奖励
            if (comboCount > 1)
            {
                totalMultiplier *= (1f + (comboCount - 1) * 0.1f); // 每次连击增加10%
            }

            return Mathf.RoundToInt(baseScore * totalMultiplier);
        }

        /// <summary>
        /// 添加分数
        /// </summary>
        public void AddScore(int score)
        {
            if (score <= 0) return;

            int oldScore = currentScore;
            currentScore += score;
            lastScoreTime = Time.time;

            // 检查分数里程碑 (需求6.2: 当玩家达成里程碑时，应该显示适当的反馈)
            CheckScoreMilestones(oldScore, currentScore);

            // 检查是否创造新纪录 (需求6.4: 如果玩家创造新的最高分，应该保存并庆祝)
            if (currentScore > highScore)
            {
                int oldHighScore = highScore;
                highScore = currentScore;
                SaveHighScore();
                OnHighScoreChanged?.Invoke(highScore);

                if (showDebugInfo)
                {
                    Debug.Log($"新纪录! 从 {oldHighScore} 提升到 {highScore}");
                }
            }

            OnScoreChanged?.Invoke(currentScore);
        }

        /// <summary>
        /// 更新连击计数
        /// </summary>
        private void UpdateComboCount()
        {
            comboCount++;
            OnComboChanged?.Invoke(comboCount);

            // 更新连击倍数
            comboMultiplier = 1f + (comboCount - 1) * 0.05f; // 每次连击增加5%倍数
        }

        /// <summary>
        /// 重置连击
        /// </summary>
        private void ResetCombo()
        {
            if (comboCount > 0)
            {
                comboCount = 0;
                comboMultiplier = 1f;
                OnComboChanged?.Invoke(comboCount);

                if (showDebugInfo)
                {
                    Debug.Log("连击重置");
                }
            }
        }

        /// <summary>
        /// 设置分数倍数
        /// </summary>
        public void SetScoreMultiplier(float multiplier)
        {
            scoreMultiplier = Mathf.Max(0f, multiplier);
        }

        /// <summary>
        /// 重置当前分数
        /// </summary>
        public void ResetCurrentScore()
        {
            currentScore = 0;
            ResetCombo();
            OnScoreChanged?.Invoke(currentScore);
        }

        /// <summary>
        /// 重置所有数据
        /// </summary>
        public void ResetAllData()
        {
            ResetCurrentScore();

            // 重置收集品计数
            foreach (var key in new List<CollectibleType>(collectedItems.Keys))
            {
                collectedItems[key] = 0;
            }
        }

        // -----------------------------
        // 测试辅助API（仅用于测试场景调用）
        // -----------------------------

        /// <summary>
        /// 手动增加一次连击（测试辅助方法）
        /// 说明：某些测试用例需要直接模拟连击累加。
        /// </summary>
        public void AddCombo()
        {
            // 更新连击计数并刷新时间，避免被连击超时重置
            UpdateComboCount();
            lastScoreTime = Time.time;
        }

        /// <summary>
        /// 模拟一次收集品被收集（测试辅助方法）
        /// 说明：构造一个基础的 CollectibleData 以触发收集逻辑、加分与连击。
        /// </summary>
        public void AddCollectible()
        {
            var data = new CollectibleData
            {
                type = CollectibleType.Coin,
                itemName = "TestCoin",
                value = 10,
                quantity = 1,
                isConsumable = false
            };

            // 直接调用内部处理流程
            HandleItemCollected(data);
        }


        /// <summary>
        /// 获取收集品总数
        /// </summary>
        public int GetTotalCollectedItems()
        {
            int total = 0;
            foreach (var count in collectedItems.Values)
            {
                total += count;
            }
            return total;
        }

        /// <summary>
        /// 获取特定类型收集品数量
        /// </summary>
        public int GetCollectedItemCount(CollectibleType type)
        {
            return collectedItems.ContainsKey(type) ? collectedItems[type] : 0;
        }

        /// <summary>
        /// 检查分数里程碑
        /// 实现需求6.2: 当玩家达成里程碑时，应该显示适当的反馈
        /// </summary>
        private void CheckScoreMilestones(int oldScore, int newScore)
        {
            // 定义里程碑分数点
            int[] milestones = { 100, 250, 500, 1000, 2500, 5000, 10000, 25000, 50000, 100000 };

            foreach (int milestone in milestones)
            {
                if (oldScore < milestone && newScore >= milestone)
                {
                    OnMilestoneReached?.Invoke(milestone);

                    if (showDebugInfo)
                    {
                        Debug.Log($"分数里程碑达成: {milestone} 分!");
                    }
                    break; // 只触发一个里程碑
                }
            }
        }

        /// <summary>
        /// 获取分数统计信息
        /// </summary>
        public string GetScoreStats()
        {
            return $"当前分数: {currentScore}\n" +
                   $"最高分: {highScore}\n" +
                   $"连击: {comboCount}\n" +
                   $"分数倍数: {scoreMultiplier:F2}\n" +
                   $"连击倍数: {comboMultiplier:F2}\n" +
                   $"收集物品总数: {GetTotalCollectedItems()}";
        }

        #region 静态实例管理

        private static ScoreManager instance;
        public static ScoreManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<ScoreManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("ScoreManager");
                        instance = go.AddComponent<ScoreManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }

        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }

        #endregion
    }
}