using UnityEngine;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人控制器基类
    /// 使用状态机模式管理敌人行为
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
    public class EnemyController : MonoBehaviour
    {
        [Header("敌人数据")]
        [SerializeField] protected EnemyData enemyData;
        
        [Header("调试设置")]
        [SerializeField] protected bool showDebugInfo = false;
        [SerializeField] protected bool showGizmos = true;
        
        // 组件引用
        protected Rigidbody2D rb2d;
        protected Collider2D enemyCollider;
        protected SpriteRenderer spriteRenderer;
        protected Animator animator;
        
        // 状态机
        protected EnemyStateMachine stateMachine;
        
        // 目标和检测
        protected Transform player;
        protected Vector3 initialPosition;
        protected float lastAttackTime;
        
        // 事件
        public System.Action<int> OnHealthChanged;
        public System.Action OnDeath;
        public System.Action<Transform> OnPlayerDetected;
        public System.Action OnPlayerLost;
        public System.Action OnAttackPerformed;
        
        // 属性
        public EnemyData Data => enemyData;
        public EnemyStateMachine StateMachine => stateMachine;
        public Transform Player => player;
        public Vector3 InitialPosition => initialPosition;
        public bool IsAlive => enemyData.IsAlive();
        public float DistanceToPlayer => player != null ? Vector3.Distance(transform.position, player.position) : float.MaxValue;
        
        protected virtual void Awake()
        {
            InitializeComponents();
            InitializeData();
        }
        
        protected virtual void Start()
        {
            InitializeStateMachine();
            FindPlayer();
            initialPosition = transform.position;
        }
        
        protected virtual void Update()
        {
            if (!IsAlive) return;
            
            UpdateGroundCheck();
            stateMachine?.Update();
            
            if (showDebugInfo)
            {
                DebugCurrentState();
            }
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        protected virtual void InitializeComponents()
        {
            rb2d = GetComponent<Rigidbody2D>();
            enemyCollider = GetComponent<Collider2D>();
            spriteRenderer = GetComponent<SpriteRenderer>();
            animator = GetComponent<Animator>();
            
            // 配置Rigidbody2D
            if (rb2d != null)
            {
                rb2d.freezeRotation = true;
                rb2d.gravityScale = 1f;
            }
        }
        
        /// <summary>
        /// 初始化数据
        /// </summary>
        protected virtual void InitializeData()
        {
            if (enemyData == null)
            {
                enemyData = new EnemyData();
            }
            
            if (!enemyData.IsValid())
            {
                Debug.LogWarning($"敌人数据无效: {gameObject.name}");
            }
        }
        
        /// <summary>
        /// 初始化状态机
        /// </summary>
        protected virtual void InitializeStateMachine()
        {
            stateMachine = new EnemyStateMachine(this);
            stateMachine.OnStateChanged += OnStateChanged;
            stateMachine.Start(EnemyStateType.Patrol);
        }
        
        /// <summary>
        /// 寻找玩家
        /// </summary>
        protected virtual void FindPlayer()
        {
            GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
            if (playerObj != null)
            {
                player = playerObj.transform;
            }
        }
        
        /// <summary>
        /// 更新地面检测
        /// </summary>
        protected virtual void UpdateGroundCheck()
        {
            Vector3 rayStart = transform.position;
            RaycastHit2D hit = Physics2D.Raycast(rayStart, Vector2.down, enemyData.groundCheckDistance, enemyData.groundLayerMask);
            enemyData.isGrounded = hit.collider != null;
        }
        
        /// <summary>
        /// 状态改变回调
        /// </summary>
        protected virtual void OnStateChanged(EnemyStateType previousState, EnemyStateType newState)
        {
            if (showDebugInfo)
            {
                Debug.Log($"{gameObject.name} 状态改变: {previousState} -> {newState}");
            }
            
            // 更新动画
            UpdateAnimation(newState);
        }
        
        /// <summary>
        /// 更新动画
        /// </summary>
        protected virtual void UpdateAnimation(EnemyStateType state)
        {
            if (animator == null) return;
            
            switch (state)
            {
                case EnemyStateType.Idle:
                    animator.SetBool("IsMoving", false);
                    animator.SetBool("IsAttacking", false);
                    break;
                case EnemyStateType.Patrol:
                case EnemyStateType.Chase:
                    animator.SetBool("IsMoving", true);
                    animator.SetBool("IsAttacking", false);
                    break;
                case EnemyStateType.Attack:
                    animator.SetBool("IsMoving", false);
                    animator.SetBool("IsAttacking", true);
                    break;
                case EnemyStateType.Hurt:
                    animator.SetTrigger("Hurt");
                    break;
                case EnemyStateType.Death:
                    animator.SetTrigger("Death");
                    break;
            }
        }
        
        #region 移动和朝向
        
        /// <summary>
        /// 移动敌人
        /// </summary>
        public virtual void Move(Vector2 direction, float speed)
        {
            if (rb2d == null || !IsAlive) return;
            
            Vector2 velocity = rb2d.linearVelocity;
            velocity.x = direction.x * speed;
            rb2d.linearVelocity = velocity;
            
            // 更新朝向
            if (Mathf.Abs(direction.x) > 0.1f)
            {
                SetFacingDirection(direction.x > 0);
            }
        }
        
        /// <summary>
        /// 停止移动
        /// </summary>
        public virtual void StopMovement()
        {
            if (rb2d != null)
            {
                Vector2 velocity = rb2d.linearVelocity;
                velocity.x = 0;
                rb2d.linearVelocity = velocity;
            }
        }
        
        /// <summary>
        /// 设置朝向
        /// </summary>
        public virtual void SetFacingDirection(bool facingRight)
        {
            if (enemyData.facingRight != facingRight)
            {
                enemyData.facingRight = facingRight;
                
                if (spriteRenderer != null)
                {
                    spriteRenderer.flipX = !facingRight;
                }
            }
        }
        
        #endregion
        
        #region 战斗系统
        
        /// <summary>
        /// 受到伤害
        /// </summary>
        public virtual void TakeDamage(int damage)
        {
            if (!IsAlive) return;
            
            enemyData.currentHealth = Mathf.Max(0, enemyData.currentHealth - damage);
            OnHealthChanged?.Invoke(enemyData.currentHealth);
            
            if (enemyData.currentHealth <= 0)
            {
                Die();
            }
            else
            {
                // 切换到受伤状态
                stateMachine.ChangeState(EnemyStateType.Hurt);
            }
        }
        
        /// <summary>
        /// 执行攻击
        /// </summary>
        public virtual void PerformAttack()
        {
            if (!CanAttack()) return;
            
            lastAttackTime = Time.time;
            OnAttackPerformed?.Invoke();
            
            // 检测攻击范围内的玩家
            if (player != null && DistanceToPlayer <= enemyData.attackRange)
            {
                // 尝试对玩家造成伤害
                var playerController = player.GetComponent<ICharacterController>();
                if (playerController != null)
                {
                    playerController.TakeDamage(enemyData.damage);
                }
            }
        }
        
        /// <summary>
        /// 检查是否可以攻击
        /// </summary>
        public virtual bool CanAttack()
        {
            return IsAlive && Time.time >= lastAttackTime + enemyData.attackCooldown;
        }
        
        /// <summary>
        /// 死亡处理
        /// </summary>
        protected virtual void Die()
        {
            stateMachine.ForceChangeState(EnemyStateType.Death);
            OnDeath?.Invoke();
            
            // 禁用碰撞器
            if (enemyCollider != null)
            {
                enemyCollider.enabled = false;
            }
            
            // 停止移动
            StopMovement();
        }
        
        #endregion
        
        #region 检测系统
        
        /// <summary>
        /// 检测玩家是否在范围内
        /// </summary>
        public virtual bool IsPlayerInRange(float range)
        {
            return player != null && DistanceToPlayer <= range;
        }
        
        /// <summary>
        /// 检测玩家是否在视线内
        /// </summary>
        public virtual bool IsPlayerInSight()
        {
            if (player == null) return false;
            
            Vector3 directionToPlayer = (player.position - transform.position).normalized;
            RaycastHit2D hit = Physics2D.Raycast(transform.position, directionToPlayer, enemyData.detectionRange, enemyData.playerLayerMask);
            
            return hit.collider != null && hit.collider.transform == player;
        }
        
        /// <summary>
        /// 获取到玩家的方向
        /// </summary>
        public virtual Vector2 GetDirectionToPlayer()
        {
            if (player == null) return Vector2.zero;
            return (player.position - transform.position).normalized;
        }
        
        #endregion
        
        #region 调试
        
        /// <summary>
        /// 调试当前状态
        /// </summary>
        protected virtual void DebugCurrentState()
        {
            if (stateMachine != null)
            {
                Debug.Log($"{gameObject.name} - 状态: {stateMachine.CurrentStateType}, 生命值: {enemyData.currentHealth}, 到玩家距离: {DistanceToPlayer:F2}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        protected virtual void OnDrawGizmos()
        {
            if (!showGizmos || enemyData == null) return;

            // 绘制检测范围
            Gizmos.color = Color.yellow;
            DrawWireCircle(transform.position, enemyData.detectionRange);

            // 绘制攻击范围
            Gizmos.color = Color.red;
            DrawWireCircle(transform.position, enemyData.attackRange);

            // 绘制巡逻范围
            Gizmos.color = Color.blue;
            Vector3 patrolCenter = Application.isPlaying ? initialPosition : transform.position;
            Gizmos.DrawLine(patrolCenter + Vector3.left * enemyData.patrolDistance,
                           patrolCenter + Vector3.right * enemyData.patrolDistance);

            // 绘制地面检测
            Gizmos.color = Color.green;
            Gizmos.DrawLine(transform.position, transform.position + Vector3.down * enemyData.groundCheckDistance);
        }

        /// <summary>
        /// 绘制2D圆形线框 - 自定义实现
        /// </summary>
        private void DrawWireCircle(Vector3 center, float radius)
        {
            const int segments = 32; // 圆形分段数
            float angleStep = 360f / segments;

            Vector3 prevPoint = center + new Vector3(radius, 0, 0);

            for (int i = 1; i <= segments; i++)
            {
                float angle = i * angleStep * Mathf.Deg2Rad;
                Vector3 newPoint = center + new Vector3(
                    Mathf.Cos(angle) * radius,
                    Mathf.Sin(angle) * radius,
                    0
                );

                Gizmos.DrawLine(prevPoint, newPoint);
                prevPoint = newPoint;
            }
        }
        
        #endregion
        
        #region Unity事件
        
        protected virtual void OnTriggerEnter2D(Collider2D other)
        {
            if (other.CompareTag("Player"))
            {
                OnPlayerDetected?.Invoke(other.transform);
            }
        }
        
        protected virtual void OnTriggerExit2D(Collider2D other)
        {
            if (other.CompareTag("Player"))
            {
                OnPlayerLost?.Invoke();
            }
        }
        
        #endregion
    }
}