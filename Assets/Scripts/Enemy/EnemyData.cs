using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人数据结构 - 存储敌人的配置和状态信息
    /// </summary>
    [System.Serializable]
    public class EnemyData
    {
        [Header("基础属性")]
        public int maxHealth = 50;
        public int currentHealth = 50;
        public int damage = 10;
        
        [Header("移动参数")]
        public float moveSpeed = 2f;
        public float chaseSpeed = 4f;
        
        [Header("巡逻设置")]
        public float patrolDistance = 5f;
        public float patrolWaitTime = 2f;
        
        [Header("检测设置")]
        public float detectionRange = 8f;
        public float attackRange = 1.5f;
        public float loseTargetDistance = 12f;
        
        [Header("攻击设置")]
        public float attackCooldown = 1.5f;
        public float attackDuration = 0.5f;
        
        [Header("物理参数")]
        public float groundCheckDistance = 0.1f;
        public LayerMask groundLayerMask = 1;
        public LayerMask playerLayerMask = 1 << 6; // 假设玩家在第6层
        
        [Header("状态")]
        public bool facingRight = true;
        public bool isGrounded = false;
        
        /// <summary>
        /// 重置敌人数据到默认状态
        /// </summary>
        public void Reset()
        {
            currentHealth = maxHealth;
            isGrounded = false;
            facingRight = true;
        }
        
        /// <summary>
        /// 验证数据的有效性
        /// </summary>
        public bool IsValid()
        {
            return maxHealth > 0 && 
                   damage >= 0 && 
                   moveSpeed > 0 && 
                   chaseSpeed > 0 && 
                   patrolDistance > 0 && 
                   detectionRange > 0 && 
                   attackRange > 0 && 
                   attackCooldown > 0;
        }
        
        /// <summary>
        /// 获取当前生命值百分比
        /// </summary>
        public float GetHealthPercentage()
        {
            return maxHealth > 0 ? (float)currentHealth / maxHealth : 0f;
        }
        
        /// <summary>
        /// 是否存活
        /// </summary>
        public bool IsAlive()
        {
            return currentHealth > 0;
        }
    }
}