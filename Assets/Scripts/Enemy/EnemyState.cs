using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人状态枚举
    /// </summary>
    public enum EnemyStateType
    {
        Idle,       // 待机
        Patrol,     // 巡逻
        Chase,      // 追逐
        Attack,     // 攻击
        Hurt,       // 受伤
        Death       // 死亡
    }

    /// <summary>
    /// 敌人状态基类
    /// </summary>
    public abstract class EnemyState
    {
        protected EnemyController enemyController;
        protected EnemyStateType stateType;

        public EnemyStateType StateType => stateType;

        public EnemyState(EnemyController controller, EnemyStateType type)
        {
            enemyController = controller;
            stateType = type;
        }

        /// <summary>
        /// 进入状态时调用
        /// </summary>
        public virtual void OnEnter() { }

        /// <summary>
        /// 状态更新
        /// </summary>
        public virtual void OnUpdate() { }

        /// <summary>
        /// 退出状态时调用
        /// </summary>
        public virtual void OnExit() { }

        /// <summary>
        /// 检查是否可以转换到目标状态
        /// </summary>
        public virtual bool CanTransitionTo(EnemyStateType targetState)
        {
            return true;
        }
    }
}