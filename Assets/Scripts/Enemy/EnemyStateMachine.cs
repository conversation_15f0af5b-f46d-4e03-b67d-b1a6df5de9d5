using System.Collections.Generic;
using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人状态机
    /// 管理敌人状态转换和更新
    /// </summary>
    public class EnemyStateMachine
    {
        private Dictionary<EnemyStateType, EnemyState> states;
        private EnemyState currentState;
        private EnemyController enemyController;

        public EnemyState CurrentState => currentState;
        public EnemyStateType CurrentStateType => currentState?.StateType ?? EnemyStateType.Idle;

        // 状态变化事件
        public System.Action<EnemyStateType, EnemyStateType> OnStateChanged;

        public EnemyStateMachine(EnemyController controller)
        {
            enemyController = controller;
            states = new Dictionary<EnemyStateType, EnemyState>();
            InitializeStates();
        }

        /// <summary>
        /// 初始化所有状态
        /// </summary>
        private void InitializeStates()
        {
            states[EnemyStateType.Idle] = new EnemyIdleState(enemyController);
            states[EnemyStateType.Patrol] = new EnemyPatrolState(enemyController);
            states[EnemyStateType.Chase] = new EnemyChaseState(enemyController);
            states[EnemyStateType.Attack] = new EnemyAttackState(enemyController);
            states[EnemyStateType.Hurt] = new EnemyHurtState(enemyController);
            states[EnemyStateType.Death] = new EnemyDeathState(enemyController);
        }

        /// <summary>
        /// 设置初始状态
        /// </summary>
        public void Start(EnemyStateType initialState = EnemyStateType.Idle)
        {
            ChangeState(initialState);
        }

        /// <summary>
        /// 更新状态机
        /// </summary>
        public void Update()
        {
            currentState?.OnUpdate();
        }

        /// <summary>
        /// 改变状态
        /// </summary>
        public bool ChangeState(EnemyStateType newStateType)
        {
            if (states.TryGetValue(newStateType, out EnemyState newState))
            {
                // 检查是否可以转换
                if (currentState != null && !currentState.CanTransitionTo(newStateType))
                {
                    return false;
                }

                EnemyStateType previousStateType = CurrentStateType;
                
                // 退出当前状态
                currentState?.OnExit();
                
                // 切换到新状态
                currentState = newState;
                currentState.OnEnter();

                // 触发状态变化事件
                OnStateChanged?.Invoke(previousStateType, newStateType);
                
                return true;
            }

            Debug.LogWarning($"状态 {newStateType} 不存在");
            return false;
        }

        /// <summary>
        /// 强制改变状态（忽略转换检查）
        /// </summary>
        public void ForceChangeState(EnemyStateType newStateType)
        {
            if (states.TryGetValue(newStateType, out EnemyState newState))
            {
                EnemyStateType previousStateType = CurrentStateType;
                
                currentState?.OnExit();
                currentState = newState;
                currentState.OnEnter();

                OnStateChanged?.Invoke(previousStateType, newStateType);
            }
        }

        /// <summary>
        /// 获取指定类型的状态
        /// </summary>
        public T GetState<T>(EnemyStateType stateType) where T : EnemyState
        {
            if (states.TryGetValue(stateType, out EnemyState state))
            {
                return state as T;
            }
            return null;
        }
    }
}