using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人攻击状态
    /// </summary>
    public class EnemyAttackState : EnemyState
    {
        private float attackTimer;
        private bool hasAttacked;

        public EnemyAttackState(EnemyController controller) : base(controller, EnemyStateType.Attack)
        {
        }

        public override void OnEnter()
        {
            enemyController.StopMovement();
            attackTimer = 0f;
            hasAttacked = false;
        }

        public override void OnUpdate()
        {
            attackTimer += Time.deltaTime;

            // 执行攻击
            if (!hasAttacked && attackTimer >= 0.2f) // 攻击延迟
            {
                PerformAttack();
                hasAttacked = true;
            }

            // 攻击动画结束后决定下一个状态
            if (attackTimer >= enemyController.Data.attackDuration)
            {
                DecideNextState();
            }
        }

        /// <summary>
        /// 执行攻击
        /// </summary>
        private void PerformAttack()
        {
            enemyController.PerformAttack();
        }

        /// <summary>
        /// 决定下一个状态
        /// </summary>
        private void DecideNextState()
        {
            if (enemyController.Player == null)
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
                return;
            }

            float distanceToPlayer = enemyController.DistanceToPlayer;

            // 如果玩家仍在攻击范围内且可以攻击，继续攻击
            if (distanceToPlayer <= enemyController.Data.attackRange && 
                enemyController.CanAttack())
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Attack);
            }
            // 如果玩家在检测范围内，追逐
            else if (distanceToPlayer <= enemyController.Data.detectionRange && 
                     enemyController.IsPlayerInSight())
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Chase);
            }
            // 否则返回巡逻
            else
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
            }
        }

        public override bool CanTransitionTo(EnemyStateType targetState)
        {
            // 死亡状态可以从任何状态转换
            if (targetState == EnemyStateType.Death)
                return true;

            // 受伤状态可以从任何状态转换
            if (targetState == EnemyStateType.Hurt)
                return true;

            // 攻击状态下不能被打断，除非是特殊状态
            return targetState == EnemyStateType.Chase || 
                   targetState == EnemyStateType.Patrol ||
                   targetState == EnemyStateType.Attack; // 允许连续攻击
        }
    }
}