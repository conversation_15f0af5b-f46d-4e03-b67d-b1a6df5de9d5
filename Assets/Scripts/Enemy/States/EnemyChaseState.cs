using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人追逐状态
    /// </summary>
    public class EnemyChaseState : EnemyState
    {
        private float loseTargetTimer;
        private float maxLoseTargetTime = 3f;

        public EnemyChaseState(EnemyController controller) : base(controller, EnemyStateType.Chase)
        {
        }

        public override void OnEnter()
        {
            loseTargetTimer = 0f;
        }

        public override void OnUpdate()
        {
            if (enemyController.Player == null)
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
                return;
            }

            float distanceToPlayer = enemyController.DistanceToPlayer;

            // 检查是否进入攻击范围
            if (distanceToPlayer <= enemyController.Data.attackRange)
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Attack);
                return;
            }

            // 检查是否失去目标
            if (distanceToPlayer > enemyController.Data.loseTargetDistance || 
                !enemyController.IsPlayerInSight())
            {
                loseTargetTimer += Time.deltaTime;
                
                if (loseTargetTimer >= maxLoseTargetTime)
                {
                    enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
                    return;
                }
            }
            else
            {
                loseTargetTimer = 0f;
            }

            // 追逐玩家
            ChasePlayer();
        }

        /// <summary>
        /// 追逐玩家
        /// </summary>
        private void ChasePlayer()
        {
            Vector2 directionToPlayer = enemyController.GetDirectionToPlayer();
            
            // 只在水平方向追逐
            directionToPlayer.y = 0;
            directionToPlayer.Normalize();

            enemyController.Move(directionToPlayer, enemyController.Data.chaseSpeed);
        }

        public override bool CanTransitionTo(EnemyStateType targetState)
        {
            // 死亡状态可以从任何状态转换
            if (targetState == EnemyStateType.Death)
                return true;

            // 受伤状态可以从任何状态转换
            if (targetState == EnemyStateType.Hurt)
                return true;

            // 其他状态的转换条件
            return targetState == EnemyStateType.Attack || 
                   targetState == EnemyStateType.Patrol ||
                   targetState == EnemyStateType.Idle;
        }
    }
}