using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人死亡状态
    /// </summary>
    public class EnemyDeathState : EnemyState
    {
        private float deathDuration = 2f;
        private float deathTimer;
        private bool hasTriggeredDeathEffects;

        public EnemyDeathState(EnemyController controller) : base(controller, EnemyStateType.Death)
        {
        }

        public override void OnEnter()
        {
            enemyController.StopMovement();
            deathTimer = 0f;
            hasTriggeredDeathEffects = false;
            
            // 触发死亡效果
            TriggerDeathEffects();
        }

        public override void OnUpdate()
        {
            deathTimer += Time.deltaTime;

            // 死亡动画播放完毕后销毁对象
            if (deathTimer >= deathDuration)
            {
                DestroyEnemy();
            }
        }

        /// <summary>
        /// 触发死亡效果
        /// </summary>
        private void TriggerDeathEffects()
        {
            if (hasTriggeredDeathEffects) return;
            
            hasTriggeredDeathEffects = true;
            
            // 禁用碰撞器
            Collider2D collider = enemyController.GetComponent<Collider2D>();
            if (collider != null)
            {
                collider.enabled = false;
            }
            
            // 设置Rigidbody为静态
            Rigidbody2D rb = enemyController.GetComponent<Rigidbody2D>();
            if (rb != null)
            {
                rb.bodyType = RigidbodyType2D.Static;
            }
            
            // 改变颜色表示死亡
            SpriteRenderer spriteRenderer = enemyController.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                spriteRenderer.color = new Color(0.5f, 0.5f, 0.5f, 0.8f);
            }
            
            // 可以在这里添加死亡粒子效果、音效等
            PlayDeathEffects();
        }

        /// <summary>
        /// 播放死亡效果
        /// </summary>
        private void PlayDeathEffects()
        {
            // 这里可以添加：
            // - 粒子效果
            // - 音效
            // - 掉落物品
            // - 分数奖励等
            
            Debug.Log($"{enemyController.gameObject.name} 已死亡");
        }

        /// <summary>
        /// 销毁敌人
        /// </summary>
        private void DestroyEnemy()
        {
            // 可以添加淡出效果
            FadeOut();
            
            // 延迟销毁以确保效果播放完毕
            Object.Destroy(enemyController.gameObject, 0.5f);
        }

        /// <summary>
        /// 淡出效果
        /// </summary>
        private void FadeOut()
        {
            SpriteRenderer spriteRenderer = enemyController.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                // 简单的透明度变化
                Color color = spriteRenderer.color;
                color.a = Mathf.Lerp(color.a, 0f, Time.deltaTime * 2f);
                spriteRenderer.color = color;
            }
        }

        public override bool CanTransitionTo(EnemyStateType targetState)
        {
            // 死亡状态不能转换到其他状态
            return false;
        }
    }
}