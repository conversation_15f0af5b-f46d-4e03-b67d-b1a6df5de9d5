using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人受伤状态
    /// </summary>
    public class EnemyHurtState : EnemyState
    {
        private float hurtDuration = 0.5f;
        private float hurtTimer;

        public EnemyHurtState(EnemyController controller) : base(controller, EnemyStateType.Hurt)
        {
        }

        public override void OnEnter()
        {
            enemyController.StopMovement();
            hurtTimer = 0f;
            
            // 可以在这里添加受伤效果，如闪烁、击退等
            ApplyHurtEffects();
        }

        public override void OnUpdate()
        {
            hurtTimer += Time.deltaTime;

            // 受伤状态结束后决定下一个状态
            if (hurtTimer >= hurtDuration)
            {
                DecideNextState();
            }
        }

        public override void OnExit()
        {
            // 移除受伤效果
            RemoveHurtEffects();
        }

        /// <summary>
        /// 应用受伤效果
        /// </summary>
        private void ApplyHurtEffects()
        {
            // 可以添加击退效果
            ApplyKnockback();
            
            // 可以添加视觉效果，如闪烁
            StartFlashing();
        }

        /// <summary>
        /// 移除受伤效果
        /// </summary>
        private void RemoveHurtEffects()
        {
            StopFlashing();
        }

        /// <summary>
        /// 应用击退效果
        /// </summary>
        private void ApplyKnockback()
        {
            if (enemyController.Player != null)
            {
                Vector2 knockbackDirection = (enemyController.transform.position - enemyController.Player.position).normalized;
                float knockbackForce = 5f;
                
                // 应用击退力
                Rigidbody2D rb = enemyController.GetComponent<Rigidbody2D>();
                if (rb != null)
                {
                    rb.AddForce(knockbackDirection * knockbackForce, ForceMode2D.Impulse);
                }
            }
        }

        /// <summary>
        /// 开始闪烁效果
        /// </summary>
        private void StartFlashing()
        {
            SpriteRenderer spriteRenderer = enemyController.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                // 简单的颜色变化表示受伤
                spriteRenderer.color = Color.red;
            }
        }

        /// <summary>
        /// 停止闪烁效果
        /// </summary>
        private void StopFlashing()
        {
            SpriteRenderer spriteRenderer = enemyController.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null)
            {
                spriteRenderer.color = Color.white;
            }
        }

        /// <summary>
        /// 决定下一个状态
        /// </summary>
        private void DecideNextState()
        {
            if (enemyController.Player == null)
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
                return;
            }

            float distanceToPlayer = enemyController.DistanceToPlayer;

            // 如果玩家在攻击范围内
            if (distanceToPlayer <= enemyController.Data.attackRange)
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Attack);
            }
            // 如果玩家在检测范围内
            else if (distanceToPlayer <= enemyController.Data.detectionRange && 
                     enemyController.IsPlayerInSight())
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Chase);
            }
            // 否则返回巡逻
            else
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
            }
        }

        public override bool CanTransitionTo(EnemyStateType targetState)
        {
            // 死亡状态可以从任何状态转换
            if (targetState == EnemyStateType.Death)
                return true;

            // 受伤状态不能被其他受伤状态打断
            if (targetState == EnemyStateType.Hurt)
                return false;

            // 受伤状态结束后可以转换到任何状态
            return hurtTimer >= hurtDuration;
        }
    }
}