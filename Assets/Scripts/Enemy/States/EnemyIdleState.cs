using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人待机状态
    /// </summary>
    public class EnemyIdleState : EnemyState
    {
        private float idleTimer;
        private float maxIdleTime = 2f;

        public EnemyIdleState(EnemyController controller) : base(controller, EnemyStateType.Idle)
        {
        }

        public override void OnEnter()
        {
            enemyController.StopMovement();
            idleTimer = 0f;
        }

        public override void OnUpdate()
        {
            idleTimer += Time.deltaTime;

            // 检测玩家
            if (enemyController.IsPlayerInRange(enemyController.Data.detectionRange) && 
                enemyController.IsPlayerInSight())
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Chase);
                return;
            }

            // 待机时间结束后开始巡逻
            if (idleTimer >= maxIdleTime)
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
            }
        }

        public override bool CanTransitionTo(EnemyStateType targetState)
        {
            // 死亡状态可以从任何状态转换
            if (targetState == EnemyStateType.Death)
                return true;

            // 受伤状态可以从任何状态转换
            if (targetState == EnemyStateType.Hurt)
                return true;

            // 其他状态的转换条件
            return targetState == EnemyStateType.Patrol || 
                   targetState == EnemyStateType.Chase;
        }
    }
}