using UnityEngine;

namespace MobileScrollingGame.Enemy
{
    /// <summary>
    /// 敌人巡逻状态
    /// </summary>
    public class EnemyPatrolState : EnemyState
    {
        private Vector3 targetPosition;
        private bool movingRight = true;
        private float waitTimer;
        private bool isWaiting;

        public EnemyPatrolState(EnemyController controller) : base(controller, EnemyStateType.Patrol)
        {
        }

        public override void OnEnter()
        {
            SetNextPatrolTarget();
            isWaiting = false;
            waitTimer = 0f;
        }

        public override void OnUpdate()
        {
            // 检测玩家
            if (enemyController.IsPlayerInRange(enemyController.Data.detectionRange) && 
                enemyController.IsPlayerInSight())
            {
                enemyController.StateMachine.ChangeState(EnemyStateType.Chase);
                return;
            }

            if (isWaiting)
            {
                HandleWaiting();
            }
            else
            {
                HandleMovement();
            }
        }

        /// <summary>
        /// 处理等待逻辑
        /// </summary>
        private void HandleWaiting()
        {
            enemyController.StopMovement();
            waitTimer += Time.deltaTime;

            if (waitTimer >= enemyController.Data.patrolWaitTime)
            {
                isWaiting = false;
                waitTimer = 0f;
                
                // 改变方向
                movingRight = !movingRight;
                SetNextPatrolTarget();
            }
        }

        /// <summary>
        /// 处理移动逻辑
        /// </summary>
        private void HandleMovement()
        {
            Vector3 currentPosition = enemyController.transform.position;
            float distanceToTarget = Vector3.Distance(currentPosition, targetPosition);

            // 到达目标位置
            if (distanceToTarget < 0.5f)
            {
                isWaiting = true;
                return;
            }

            // 移动向目标
            Vector2 direction = (targetPosition - currentPosition).normalized;
            enemyController.Move(direction, enemyController.Data.moveSpeed);
        }

        /// <summary>
        /// 设置下一个巡逻目标
        /// </summary>
        private void SetNextPatrolTarget()
        {
            Vector3 initialPos = enemyController.InitialPosition;
            float patrolDistance = enemyController.Data.patrolDistance;

            if (movingRight)
            {
                targetPosition = initialPos + Vector3.right * patrolDistance;
            }
            else
            {
                targetPosition = initialPos + Vector3.left * patrolDistance;
            }

            // 确保目标位置在地面上
            targetPosition.y = initialPos.y;
        }

        public override bool CanTransitionTo(EnemyStateType targetState)
        {
            // 死亡状态可以从任何状态转换
            if (targetState == EnemyStateType.Death)
                return true;

            // 受伤状态可以从任何状态转换
            if (targetState == EnemyStateType.Hurt)
                return true;

            // 其他状态的转换条件
            return targetState == EnemyStateType.Idle || 
                   targetState == EnemyStateType.Chase;
        }
    }
}