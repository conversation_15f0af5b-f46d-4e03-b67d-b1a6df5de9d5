using UnityEngine;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Input
{
    /// <summary>
    /// 输入映射器
    /// 将不同输入源（触控、虚拟控制、键盘等）映射到统一的游戏动作
    /// </summary>
    public class InputMapper : MonoBehaviour, IInputHandler
    {
        [Header("输入源")]
        [SerializeField] private TouchInputHandler touchInputHandler;
        [SerializeField] private VirtualControls virtualControls;
        
        [Header("输入优先级")]
        [SerializeField] private bool preferVirtualControls = true;
        [SerializeField] private bool allowKeyboardInput = true;
        
        [Header("设置")]
        [SerializeField] private bool debugMode = false;
        
        // 输入状态缓存
        private Vector2 cachedMovementInput;
        private bool cachedJumpInput;
        private bool cachedActionInput;
        
        private void Awake()
        {
            // 自动查找输入组件
            if (touchInputHandler == null)
            {
                touchInputHandler = FindFirstObjectByType<TouchInputHandler>();
            }
            
            if (virtualControls == null)
            {
                virtualControls = FindFirstObjectByType<VirtualControls>();
            }
        }
        
        private void Update()
        {
            // 更新输入状态缓存
            UpdateInputCache();
        }
        
        private void UpdateInputCache()
        {
            // 重置缓存
            cachedMovementInput = Vector2.zero;
            cachedJumpInput = false;
            cachedActionInput = false;
            
            // 根据优先级合并输入
            if (preferVirtualControls && virtualControls != null)
            {
                // 优先使用虚拟控制
                cachedMovementInput = virtualControls.GetMovementInput();
                cachedJumpInput = virtualControls.GetJumpInput();
                cachedActionInput = virtualControls.GetActionInput();
                
                // 如果虚拟控制没有输入，则使用触控输入
                if (cachedMovementInput == Vector2.zero && touchInputHandler != null)
                {
                    cachedMovementInput = touchInputHandler.GetMovementInput();
                }
                
                if (!cachedJumpInput && touchInputHandler != null)
                {
                    cachedJumpInput = touchInputHandler.GetJumpInput();
                }
                
                if (!cachedActionInput && touchInputHandler != null)
                {
                    cachedActionInput = touchInputHandler.GetActionInput();
                }
            }
            else if (touchInputHandler != null)
            {
                // 优先使用触控输入
                cachedMovementInput = touchInputHandler.GetMovementInput();
                cachedJumpInput = touchInputHandler.GetJumpInput();
                cachedActionInput = touchInputHandler.GetActionInput();
                
                // 如果触控输入没有输入，则使用虚拟控制
                if (cachedMovementInput == Vector2.zero && virtualControls != null)
                {
                    cachedMovementInput = virtualControls.GetMovementInput();
                }
                
                if (!cachedJumpInput && virtualControls != null)
                {
                    cachedJumpInput = virtualControls.GetJumpInput();
                }
                
                if (!cachedActionInput && virtualControls != null)
                {
                    cachedActionInput = virtualControls.GetActionInput();
                }
            }
            
            // 添加键盘输入支持（用于测试）
            if (allowKeyboardInput && Application.isEditor)
            {
                AddKeyboardInput();
            }
            
            if (debugMode && HasActiveInput())
            {
                Debug.Log($"输入映射 - 移动: {cachedMovementInput}, 跳跃: {cachedJumpInput}, 动作: {cachedActionInput}");
            }
        }
        
        private void AddKeyboardInput()
        {
            // 键盘移动输入
            Vector2 keyboardMovement = Vector2.zero;
            
            if (global::UnityEngine.Input.GetKey(KeyCode.A) || global::UnityEngine.Input.GetKey(KeyCode.LeftArrow)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                keyboardMovement.x = -1f;
            }
            else if (global::UnityEngine.Input.GetKey(KeyCode.D) || global::UnityEngine.Input.GetKey(KeyCode.RightArrow)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                keyboardMovement.x = 1f;
            }
            
            if (global::UnityEngine.Input.GetKey(KeyCode.W) || global::UnityEngine.Input.GetKey(KeyCode.UpArrow)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                keyboardMovement.y = 1f;
            }
            else if (global::UnityEngine.Input.GetKey(KeyCode.S) || global::UnityEngine.Input.GetKey(KeyCode.DownArrow)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                keyboardMovement.y = -1f;
            }
            
            // 如果没有其他移动输入，使用键盘输入
            if (cachedMovementInput == Vector2.zero && keyboardMovement != Vector2.zero)
            {
                cachedMovementInput = keyboardMovement.normalized;
            }
            
            // 键盘跳跃输入
            if (!cachedJumpInput && global::UnityEngine.Input.GetKeyDown(KeyCode.Space)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                cachedJumpInput = true;
            }
            
            // 键盘动作输入
            if (!cachedActionInput && global::UnityEngine.Input.GetKeyDown(KeyCode.Return)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                cachedActionInput = true;
            }
        }
        
        #region IInputHandler 实现
        
        public Vector2 GetMovementInput()
        {
            return cachedMovementInput;
        }
        
        public bool GetJumpInput()
        {
            bool result = cachedJumpInput;
            cachedJumpInput = false; // 重置以防止重复触发
            return result;
        }
        
        public bool GetActionInput()
        {
            bool result = cachedActionInput;
            cachedActionInput = false; // 重置以防止重复触发
            return result;
        }
        
        public void EnableInput(bool enabled)
        {
            // 启用/禁用所有输入源
            if (touchInputHandler != null)
            {
                touchInputHandler.EnableInput(enabled);
            }
            
            if (virtualControls != null)
            {
                virtualControls.SetVisible(enabled);
            }
            
            if (!enabled)
            {
                ResetInputs();
            }
            
            if (debugMode)
            {
                Debug.Log($"输入映射器{(enabled ? "启用" : "禁用")}");
            }
        }
        
        public bool HasActiveInput()
        {
            return cachedMovementInput != Vector2.zero || cachedJumpInput || cachedActionInput;
        }
        
        public void ResetInputs()
        {
            cachedMovementInput = Vector2.zero;
            cachedJumpInput = false;
            cachedActionInput = false;
            
            // 重置所有输入源
            if (touchInputHandler != null)
            {
                touchInputHandler.ResetInputs();
            }
            
            if (virtualControls != null)
            {
                virtualControls.ResetInputs();
            }
            
            if (debugMode)
            {
                Debug.Log("输入映射器状态重置");
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置虚拟控制优先级
        /// </summary>
        public void SetVirtualControlsPriority(bool prefer)
        {
            preferVirtualControls = prefer;
        }
        
        /// <summary>
        /// 设置键盘输入支持
        /// </summary>
        public void SetKeyboardInputEnabled(bool enabled)
        {
            allowKeyboardInput = enabled;
        }
        
        /// <summary>
        /// 获取当前活动的输入源
        /// </summary>
        public string GetActiveInputSource()
        {
            if (virtualControls != null && virtualControls.HasActiveInput())
            {
                return "VirtualControls";
            }
            
            if (touchInputHandler != null && touchInputHandler.HasActiveInput())
            {
                return "TouchInput";
            }
            
            if (allowKeyboardInput && Application.isEditor)
            {
                if (global::UnityEngine.Input.anyKey) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
                {
                    return "Keyboard";
                }
            }
            
            return "None";
        }
        
        /// <summary>
        /// 设置调试模式
        /// </summary>
        public void SetDebugMode(bool debug)
        {
            debugMode = debug;
            
            if (touchInputHandler != null)
            {
                touchInputHandler.SetDebugMode(debug);
            }
            
            if (virtualControls != null)
            {
                virtualControls.SetDebugMode(debug);
            }
        }
        
        #endregion
    }
}