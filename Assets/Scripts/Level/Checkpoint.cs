using UnityEngine;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 检查点组件，处理玩家进度保存和重生点设置
    /// </summary>
    public class Checkpoint : MonoBehaviour
    {
        [Header("检查点配置")]
        [SerializeField] private string checkpointId;
        [SerializeField] private bool isActivated = false;
        [SerializeField] private bool autoActivate = true;
        [SerializeField] private float activationRadius = 2.0f;
        
        [Header("视觉反馈")]
        [SerializeField] private GameObject inactiveVisual;
        [SerializeField] private GameObject activeVisual;
        [SerializeField] private ParticleSystem activationEffect;
        
        [Header("音频")]
        [SerializeField] private AudioClip activationSound;
        [SerializeField] private float soundVolume = 1.0f;
        
        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool showGizmos = true;
        
        // 事件
        public System.Action<Checkpoint> OnCheckpointActivated;
        public System.Action<Checkpoint> OnPlayerRespawned;
        
        // 组件引用
        private AudioSource _audioSource;
        private Collider2D _collider;
        private ILevelManager _levelManager;
        
        // 状态
        private bool _playerInRange = false;
        
        private void Awake()
        {
            // 初始化组件
            _collider = GetComponent<Collider2D>();
            _audioSource = GetComponent<AudioSource>();
            
            // 如果没有AudioSource，添加一个
            if (_audioSource == null)
            {
                _audioSource = gameObject.AddComponent<AudioSource>();
                _audioSource.playOnAwake = false;
                _audioSource.volume = soundVolume;
            }
            
            // 如果没有Collider2D，添加一个触发器
            if (_collider == null)
            {
                _collider = gameObject.AddComponent<CircleCollider2D>();
                _collider.isTrigger = true;
                ((CircleCollider2D)_collider).radius = activationRadius;
            }
            
            // 生成唯一ID（如果没有设置）
            if (string.IsNullOrEmpty(checkpointId))
            {
                checkpointId = $"checkpoint_{transform.position.x}_{transform.position.y}_{GetInstanceID()}";
            }
        }
        
        private void Start()
        {
            // 获取LevelManager引用
            if (GameManager.Instance != null)
            {
                _levelManager = GameManager.Instance.GetLevelManager();
            }
            
            // 更新视觉状态
            UpdateVisualState();
            
            LogDebug($"检查点初始化: {checkpointId} 在位置 {transform.position}");
        }
        
        private void OnTriggerEnter2D(Collider2D other)
        {
            if (other.CompareTag("Player"))
            {
                _playerInRange = true;
                LogDebug($"玩家进入检查点范围: {checkpointId}");
                
                if (autoActivate && !isActivated)
                {
                    ActivateCheckpoint();
                }
            }
        }
        
        private void OnTriggerExit2D(Collider2D other)
        {
            if (other.CompareTag("Player"))
            {
                _playerInRange = false;
                LogDebug($"玩家离开检查点范围: {checkpointId}");
            }
        }
        
        /// <summary>
        /// 激活检查点
        /// </summary>
        public void ActivateCheckpoint()
        {
            if (isActivated)
            {
                LogDebug($"检查点已经激活: {checkpointId}");
                return;
            }
            
            isActivated = true;
            
            // 保存到LevelManager
            if (_levelManager != null)
            {
                _levelManager.SaveCheckpoint(transform.position);
            }
            
            // 播放激活效果
            PlayActivationEffects();
            
            // 更新视觉状态
            UpdateVisualState();
            
            LogDebug($"检查点已激活: {checkpointId} 在位置 {transform.position}");
            
            // 触发事件
            OnCheckpointActivated?.Invoke(this);
        }
        
        /// <summary>
        /// 手动激活检查点（用于非自动激活的检查点）
        /// </summary>
        public void ManualActivate()
        {
            if (!autoActivate && _playerInRange)
            {
                ActivateCheckpoint();
            }
        }
        
        /// <summary>
        /// 重置检查点状态
        /// </summary>
        public void ResetCheckpoint()
        {
            isActivated = false;
            UpdateVisualState();
            LogDebug($"检查点已重置: {checkpointId}");
        }
        
        /// <summary>
        /// 在此检查点重生玩家
        /// </summary>
        public void RespawnPlayer()
        {
            if (!isActivated)
            {
                LogDebug($"无法在未激活的检查点重生: {checkpointId}");
                return;
            }
            
            // 查找玩家对象
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                // 移动玩家到检查点位置
                player.transform.position = transform.position;
                
                // 重置玩家状态
                var healthComponent = player.GetComponent<Health>();
                if (healthComponent != null)
                {
                    healthComponent.Respawn();
                }
                
                LogDebug($"玩家已在检查点重生: {checkpointId}");
                OnPlayerRespawned?.Invoke(this);
            }
            else
            {
                LogDebug("未找到玩家对象，无法重生");
            }
        }
        
        /// <summary>
        /// 播放激活效果
        /// </summary>
        private void PlayActivationEffects()
        {
            // 播放粒子效果
            if (activationEffect != null)
            {
                activationEffect.Play();
            }
            
            // 播放音效
            if (activationSound != null && _audioSource != null)
            {
                _audioSource.clip = activationSound;
                _audioSource.volume = soundVolume;
                _audioSource.Play();
            }
        }
        
        /// <summary>
        /// 更新视觉状态
        /// </summary>
        private void UpdateVisualState()
        {
            if (inactiveVisual != null)
            {
                inactiveVisual.SetActive(!isActivated);
            }
            
            if (activeVisual != null)
            {
                activeVisual.SetActive(isActivated);
            }
        }
        
        /// <summary>
        /// 调试日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[Checkpoint] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showGizmos)
                return;
            
            // 绘制激活范围
            Gizmos.color = isActivated ? Color.green : Color.yellow;
            Gizmos.DrawWireSphere(transform.position, activationRadius);
            
            // 绘制检查点标识
            Gizmos.color = isActivated ? Color.green : Color.red;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!showGizmos)
                return;
            
            // 绘制详细信息
            Gizmos.color = Color.cyan;
            Gizmos.DrawSphere(transform.position, 0.2f);
            
            // 绘制ID文本（在Scene视图中）
            #if UNITY_EDITOR
            UnityEditor.Handles.Label(transform.position + Vector3.up * 0.5f, checkpointId);
            #endif
        }
        
        #region Public API
        
        /// <summary>
        /// 获取检查点ID
        /// </summary>
        public string GetCheckpointId()
        {
            return checkpointId;
        }
        
        /// <summary>
        /// 设置检查点ID
        /// </summary>
        public void SetCheckpointId(string id)
        {
            checkpointId = id;
        }
        
        /// <summary>
        /// 检查是否已激活
        /// </summary>
        public bool IsActivated()
        {
            return isActivated;
        }
        
        /// <summary>
        /// 获取检查点位置
        /// </summary>
        public Vector3 GetPosition()
        {
            return transform.position;
        }
        
        /// <summary>
        /// 检查玩家是否在范围内
        /// </summary>
        public bool IsPlayerInRange()
        {
            return _playerInRange;
        }
        
        /// <summary>
        /// 设置激活半径
        /// </summary>
        public void SetActivationRadius(float radius)
        {
            activationRadius = radius;
            
            // 更新碰撞器大小
            if (_collider is CircleCollider2D circleCollider)
            {
                circleCollider.radius = radius;
            }
        }
        
        /// <summary>
        /// 设置自动激活模式
        /// </summary>
        public void SetAutoActivate(bool auto)
        {
            autoActivate = auto;
        }
        
        #endregion
    }
}