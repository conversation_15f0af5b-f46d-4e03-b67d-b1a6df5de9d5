using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 检查点管理器，统一管理关卡中的所有检查点
    /// </summary>
    public class CheckpointManager : MonoBehaviour
    {
        [Header("检查点管理")]
        [SerializeField] private List<Checkpoint> checkpoints = new List<Checkpoint>();
        [SerializeField] private bool autoFindCheckpoints = true;
        [SerializeField] private bool sequentialActivation = false; // 是否需要按顺序激活
        
        [Header("重生设置")]
        [SerializeField] private float respawnDelay = 1.0f;
        [SerializeField] private bool showRespawnEffect = true;
        
        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        
        // 状态
        private Checkpoint _lastActivatedCheckpoint;
        private int _lastActivatedIndex = -1;
        
        // 事件
        public System.Action<Checkpoint> OnCheckpointActivated;
        public System.Action<Checkpoint> OnPlayerRespawned;
        public System.Action<int> OnAllCheckpointsActivated;
        
        // 组件引用
        private ILevelManager _levelManager;
        
        private void Awake()
        {
            // 自动查找检查点
            if (autoFindCheckpoints)
            {
                FindAllCheckpoints();
            }
            
            // 注册检查点事件
            RegisterCheckpointEvents();
        }
        
        private void Start()
        {
            // 获取LevelManager引用
            if (GameManager.Instance != null)
            {
                _levelManager = GameManager.Instance.GetLevelManager();
            }
            
            // 验证检查点设置
            ValidateCheckpoints();
            
            LogDebug($"检查点管理器初始化完成，共找到 {checkpoints.Count} 个检查点");
        }
        
        /// <summary>
        /// 自动查找场景中的所有检查点
        /// </summary>
        private void FindAllCheckpoints()
        {
            checkpoints.Clear();
            
            // 查找所有Checkpoint组件
            Checkpoint[] foundCheckpoints = FindObjectsByType<Checkpoint>(FindObjectsSortMode.None);
            
            // 按位置排序（从左到右）
            var sortedCheckpoints = foundCheckpoints.OrderBy(cp => cp.transform.position.x).ToList();
            
            checkpoints.AddRange(sortedCheckpoints);
            
            LogDebug($"自动找到 {checkpoints.Count} 个检查点");
        }
        
        /// <summary>
        /// 注册检查点事件
        /// </summary>
        private void RegisterCheckpointEvents()
        {
            foreach (var checkpoint in checkpoints)
            {
                if (checkpoint != null)
                {
                    checkpoint.OnCheckpointActivated += HandleCheckpointActivated;
                    checkpoint.OnPlayerRespawned += HandlePlayerRespawned;
                }
            }
        }
        
        /// <summary>
        /// 验证检查点设置
        /// </summary>
        private void ValidateCheckpoints()
        {
            // 检查重复ID
            var ids = checkpoints.Where(cp => cp != null).Select(cp => cp.GetCheckpointId()).ToList();
            var duplicates = ids.GroupBy(id => id).Where(g => g.Count() > 1).Select(g => g.Key);
            
            foreach (var duplicate in duplicates)
            {
                Debug.LogWarning($"[CheckpointManager] 发现重复的检查点ID: {duplicate}");
            }
            
            // 检查空引用
            for (int i = checkpoints.Count - 1; i >= 0; i--)
            {
                if (checkpoints[i] == null)
                {
                    checkpoints.RemoveAt(i);
                    Debug.LogWarning($"[CheckpointManager] 移除空的检查点引用，索引: {i}");
                }
            }
        }
        
        /// <summary>
        /// 处理检查点激活事件
        /// </summary>
        private void HandleCheckpointActivated(Checkpoint checkpoint)
        {
            int checkpointIndex = checkpoints.IndexOf(checkpoint);
            
            // 检查顺序激活
            if (sequentialActivation && checkpointIndex > _lastActivatedIndex + 1)
            {
                LogDebug($"检查点 {checkpoint.GetCheckpointId()} 不能跳跃激活，需要按顺序激活");
                checkpoint.ResetCheckpoint();
                return;
            }
            
            _lastActivatedCheckpoint = checkpoint;
            _lastActivatedIndex = checkpointIndex;
            
            LogDebug($"检查点已激活: {checkpoint.GetCheckpointId()} (索引: {checkpointIndex})");
            
            // 触发事件
            OnCheckpointActivated?.Invoke(checkpoint);
            
            // 检查是否所有检查点都已激活
            if (AreAllCheckpointsActivated())
            {
                LogDebug("所有检查点已激活！");
                OnAllCheckpointsActivated?.Invoke(checkpoints.Count);
            }
        }
        
        /// <summary>
        /// 处理玩家重生事件
        /// </summary>
        private void HandlePlayerRespawned(Checkpoint checkpoint)
        {
            LogDebug($"玩家在检查点重生: {checkpoint.GetCheckpointId()}");
            OnPlayerRespawned?.Invoke(checkpoint);
        }
        
        /// <summary>
        /// 在最后激活的检查点重生玩家
        /// </summary>
        public void RespawnAtLastCheckpoint()
        {
            if (_lastActivatedCheckpoint != null)
            {
                StartCoroutine(RespawnCoroutine(_lastActivatedCheckpoint));
            }
            else
            {
                // 如果没有激活的检查点，在第一个检查点重生
                if (checkpoints.Count > 0 && checkpoints[0] != null)
                {
                    StartCoroutine(RespawnCoroutine(checkpoints[0]));
                }
                else
                {
                    LogDebug("没有可用的检查点进行重生");
                }
            }
        }
        
        /// <summary>
        /// 在指定检查点重生玩家
        /// </summary>
        public void RespawnAtCheckpoint(string checkpointId)
        {
            var checkpoint = GetCheckpointById(checkpointId);
            if (checkpoint != null)
            {
                StartCoroutine(RespawnCoroutine(checkpoint));
            }
            else
            {
                LogDebug($"未找到检查点: {checkpointId}");
            }
        }
        
        /// <summary>
        /// 在指定索引的检查点重生玩家
        /// </summary>
        public void RespawnAtCheckpoint(int index)
        {
            if (index >= 0 && index < checkpoints.Count && checkpoints[index] != null)
            {
                StartCoroutine(RespawnCoroutine(checkpoints[index]));
            }
            else
            {
                LogDebug($"无效的检查点索引: {index}");
            }
        }
        
        /// <summary>
        /// 重生协程
        /// </summary>
        private System.Collections.IEnumerator RespawnCoroutine(Checkpoint checkpoint)
        {
            // 等待重生延迟
            if (respawnDelay > 0)
            {
                yield return new WaitForSeconds(respawnDelay);
            }
            
            // 执行重生
            checkpoint.RespawnPlayer();
            
            // 显示重生效果
            if (showRespawnEffect)
            {
                // 这里可以添加重生特效
                LogDebug("播放重生效果");
            }
        }
        
        /// <summary>
        /// 重置所有检查点
        /// </summary>
        public void ResetAllCheckpoints()
        {
            foreach (var checkpoint in checkpoints)
            {
                if (checkpoint != null)
                {
                    checkpoint.ResetCheckpoint();
                }
            }
            
            _lastActivatedCheckpoint = null;
            _lastActivatedIndex = -1;
            
            LogDebug("所有检查点已重置");
        }
        
        /// <summary>
        /// 激活指定检查点
        /// </summary>
        public void ActivateCheckpoint(string checkpointId)
        {
            var checkpoint = GetCheckpointById(checkpointId);
            if (checkpoint != null)
            {
                checkpoint.ActivateCheckpoint();
            }
        }
        
        /// <summary>
        /// 根据ID获取检查点
        /// </summary>
        public Checkpoint GetCheckpointById(string checkpointId)
        {
            return checkpoints.FirstOrDefault(cp => cp != null && cp.GetCheckpointId() == checkpointId);
        }
        
        /// <summary>
        /// 根据索引获取检查点
        /// </summary>
        public Checkpoint GetCheckpointByIndex(int index)
        {
            if (index >= 0 && index < checkpoints.Count)
            {
                return checkpoints[index];
            }
            return null;
        }
        
        /// <summary>
        /// 获取最后激活的检查点
        /// </summary>
        public Checkpoint GetLastActivatedCheckpoint()
        {
            return _lastActivatedCheckpoint;
        }
        
        /// <summary>
        /// 检查是否所有检查点都已激活
        /// </summary>
        public bool AreAllCheckpointsActivated()
        {
            return checkpoints.All(cp => cp == null || cp.IsActivated());
        }
        
        /// <summary>
        /// 获取已激活的检查点数量
        /// </summary>
        public int GetActivatedCheckpointCount()
        {
            return checkpoints.Count(cp => cp != null && cp.IsActivated());
        }
        
        /// <summary>
        /// 获取检查点总数
        /// </summary>
        public int GetTotalCheckpointCount()
        {
            return checkpoints.Count;
        }
        
        /// <summary>
        /// 添加检查点
        /// </summary>
        public void AddCheckpoint(Checkpoint checkpoint)
        {
            if (checkpoint != null && !checkpoints.Contains(checkpoint))
            {
                checkpoints.Add(checkpoint);
                checkpoint.OnCheckpointActivated += HandleCheckpointActivated;
                checkpoint.OnPlayerRespawned += HandlePlayerRespawned;
                LogDebug($"添加检查点: {checkpoint.GetCheckpointId()}");
            }
        }
        
        /// <summary>
        /// 移除检查点
        /// </summary>
        public void RemoveCheckpoint(Checkpoint checkpoint)
        {
            if (checkpoint != null && checkpoints.Contains(checkpoint))
            {
                checkpoint.OnCheckpointActivated -= HandleCheckpointActivated;
                checkpoint.OnPlayerRespawned -= HandlePlayerRespawned;
                checkpoints.Remove(checkpoint);
                LogDebug($"移除检查点: {checkpoint.GetCheckpointId()}");
            }
        }
        
        /// <summary>
        /// 调试日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[CheckpointManager] {message}");
            }
        }
        
        private void OnDestroy()
        {
            // 取消注册事件
            foreach (var checkpoint in checkpoints)
            {
                if (checkpoint != null)
                {
                    checkpoint.OnCheckpointActivated -= HandleCheckpointActivated;
                    checkpoint.OnPlayerRespawned -= HandlePlayerRespawned;
                }
            }
        }
        
        #region Editor Support
        
        /// <summary>
        /// 在编辑器中刷新检查点列表
        /// </summary>
        [ContextMenu("刷新检查点列表")]
        public void RefreshCheckpointList()
        {
            FindAllCheckpoints();
            ValidateCheckpoints();
        }
        
        /// <summary>
        /// 在编辑器中重置所有检查点
        /// </summary>
        [ContextMenu("重置所有检查点")]
        public void EditorResetAllCheckpoints()
        {
            ResetAllCheckpoints();
        }
        
        #endregion
    }
}