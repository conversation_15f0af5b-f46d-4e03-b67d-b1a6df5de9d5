using UnityEngine;
using System.Collections.Generic;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 难度系统，管理游戏难度递增和适应性调整
    /// </summary>
    public class DifficultySystem : MonoBehaviour
    {
        [Header("难度配置")]
        [SerializeField] private List<DifficultyLevel> difficultyLevels = new List<DifficultyLevel>();
        [SerializeField] private DifficultyMode difficultyMode = DifficultyMode.Progressive;
        [SerializeField] private bool enableAdaptiveDifficulty = true;
        
        [Header("适应性难度")]
        [SerializeField] private int failureThreshold = 3; // 失败次数阈值
        [SerializeField] private float difficultyAdjustmentRate = 0.1f; // 难度调整幅度
        [SerializeField] private float minDifficultyMultiplier = 0.5f;
        [SerializeField] private float maxDifficultyMultiplier = 2.0f;
        
        [Header("提示系统")]
        [SerializeField] private bool enableHintSystem = true;
        [SerializeField] private int hintTriggerFailures = 2; // 触发提示的失败次数
        [SerializeField] private float hintDisplayDuration = 5.0f;
        
        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        
        // 当前状态
        private int _currentDifficultyIndex = 0;
        private DifficultyLevel _currentDifficulty;
        private float _adaptiveDifficultyMultiplier = 1.0f;
        
        // 统计数据
        private int _currentLevelFailures = 0;
        private int _totalFailures = 0;
        private int _consecutiveSuccesses = 0;
        private float _averageCompletionTime = 0f;
        private List<float> _completionTimes = new List<float>();
        
        // 事件
        public System.Action<DifficultyLevel> OnDifficultyChanged;
        public System.Action<string> OnHintTriggered;
        public System.Action<float> OnDifficultyMultiplierChanged;
        
        // 组件引用
        private ILevelManager _levelManager;
        
        private void Awake()
        {
            // 初始化默认难度等级
            if (difficultyLevels.Count == 0)
            {
                InitializeDefaultDifficultyLevels();
            }
            
            // 设置初始难度
            if (difficultyLevels.Count > 0)
            {
                _currentDifficulty = difficultyLevels[0];
            }
        }
        
        private void Start()
        {
            // 获取LevelManager引用
            if (GameManager.Instance != null)
            {
                _levelManager = GameManager.Instance.GetLevelManager();
            }
            
            LogDebug($"难度系统初始化完成，当前难度: {_currentDifficulty?.name}");
        }
        
        /// <summary>
        /// 初始化默认难度等级
        /// </summary>
        private void InitializeDefaultDifficultyLevels()
        {
            difficultyLevels.Add(new DifficultyLevel
            {
                name = "简单",
                levelIndex = 0,
                enemySpeedMultiplier = 0.8f,
                enemyDamageMultiplier = 0.7f,
                enemyHealthMultiplier = 0.8f,
                collectibleSpawnRate = 1.2f,
                playerHealthMultiplier = 1.2f,
                enableHints = true,
                hintFrequency = HintFrequency.High
            });
            
            difficultyLevels.Add(new DifficultyLevel
            {
                name = "普通",
                levelIndex = 1,
                enemySpeedMultiplier = 1.0f,
                enemyDamageMultiplier = 1.0f,
                enemyHealthMultiplier = 1.0f,
                collectibleSpawnRate = 1.0f,
                playerHealthMultiplier = 1.0f,
                enableHints = true,
                hintFrequency = HintFrequency.Medium
            });
            
            difficultyLevels.Add(new DifficultyLevel
            {
                name = "困难",
                levelIndex = 2,
                enemySpeedMultiplier = 1.3f,
                enemyDamageMultiplier = 1.4f,
                enemyHealthMultiplier = 1.2f,
                collectibleSpawnRate = 0.8f,
                playerHealthMultiplier = 0.9f,
                enableHints = true,
                hintFrequency = HintFrequency.Low
            });
            
            difficultyLevels.Add(new DifficultyLevel
            {
                name = "专家",
                levelIndex = 3,
                enemySpeedMultiplier = 1.6f,
                enemyDamageMultiplier = 1.8f,
                enemyHealthMultiplier = 1.5f,
                collectibleSpawnRate = 0.6f,
                playerHealthMultiplier = 0.8f,
                enableHints = false,
                hintFrequency = HintFrequency.None
            });
        }
        
        /// <summary>
        /// 根据关卡更新难度
        /// </summary>
        public void UpdateDifficultyForLevel(int levelIndex)
        {
            DifficultyLevel newDifficulty = null;
            
            switch (difficultyMode)
            {
                case DifficultyMode.Fixed:
                    // 固定难度，不改变
                    return;
                    
                case DifficultyMode.Progressive:
                    // 渐进式难度，根据关卡索引选择
                    newDifficulty = GetProgressiveDifficulty(levelIndex);
                    break;
                    
                case DifficultyMode.Adaptive:
                    // 适应性难度，根据玩家表现调整
                    newDifficulty = GetAdaptiveDifficulty(levelIndex);
                    break;
            }
            
            if (newDifficulty != null && newDifficulty != _currentDifficulty)
            {
                SetDifficulty(newDifficulty);
            }
        }
        
        /// <summary>
        /// 获取渐进式难度
        /// </summary>
        private DifficultyLevel GetProgressiveDifficulty(int levelIndex)
        {
            // 每3个关卡提升一个难度等级
            int difficultyIndex = Mathf.Min(levelIndex / 3, difficultyLevels.Count - 1);
            return difficultyLevels[difficultyIndex];
        }
        
        /// <summary>
        /// 获取适应性难度
        /// </summary>
        private DifficultyLevel GetAdaptiveDifficulty(int levelIndex)
        {
            // 基于渐进式难度开始
            var baseDifficulty = GetProgressiveDifficulty(levelIndex);
            
            // 根据玩家表现调整
            if (_currentLevelFailures >= failureThreshold)
            {
                // 降低难度
                _adaptiveDifficultyMultiplier = Mathf.Max(
                    _adaptiveDifficultyMultiplier - difficultyAdjustmentRate,
                    minDifficultyMultiplier
                );
            }
            else if (_consecutiveSuccesses >= 2)
            {
                // 提高难度
                _adaptiveDifficultyMultiplier = Mathf.Min(
                    _adaptiveDifficultyMultiplier + difficultyAdjustmentRate,
                    maxDifficultyMultiplier
                );
            }
            
            // 创建调整后的难度
            var adaptedDifficulty = CreateAdaptedDifficulty(baseDifficulty, _adaptiveDifficultyMultiplier);
            return adaptedDifficulty;
        }
        
        /// <summary>
        /// 创建适应性调整后的难度
        /// </summary>
        private DifficultyLevel CreateAdaptedDifficulty(DifficultyLevel baseDifficulty, float multiplier)
        {
            return new DifficultyLevel
            {
                name = $"{baseDifficulty.name} (适应性)",
                levelIndex = baseDifficulty.levelIndex,
                enemySpeedMultiplier = baseDifficulty.enemySpeedMultiplier * multiplier,
                enemyDamageMultiplier = baseDifficulty.enemyDamageMultiplier * multiplier,
                enemyHealthMultiplier = baseDifficulty.enemyHealthMultiplier * multiplier,
                collectibleSpawnRate = baseDifficulty.collectibleSpawnRate / multiplier,
                playerHealthMultiplier = baseDifficulty.playerHealthMultiplier / multiplier,
                enableHints = baseDifficulty.enableHints || multiplier < 1.0f,
                hintFrequency = multiplier < 1.0f ? HintFrequency.High : baseDifficulty.hintFrequency
            };
        }
        
        /// <summary>
        /// 设置难度
        /// </summary>
        public void SetDifficulty(DifficultyLevel difficulty)
        {
            if (difficulty == null)
                return;
            
            _currentDifficulty = difficulty;
            _currentDifficultyIndex = difficultyLevels.IndexOf(difficulty);
            
            LogDebug($"难度已更改为: {difficulty.name}");
            OnDifficultyChanged?.Invoke(difficulty);
            
            // 应用难度设置
            ApplyDifficultySettings(difficulty);
        }
        
        /// <summary>
        /// 应用难度设置
        /// </summary>
        private void ApplyDifficultySettings(DifficultyLevel difficulty)
        {
            // 这里应该将难度设置应用到游戏中的各个系统
            // 例如：敌人系统、收集品系统、玩家系统等
            
            LogDebug($"应用难度设置: 敌人速度x{difficulty.enemySpeedMultiplier}, 敌人伤害x{difficulty.enemyDamageMultiplier}");
            
            // 触发难度倍数变化事件
            OnDifficultyMultiplierChanged?.Invoke(_adaptiveDifficultyMultiplier);
        }
        
        /// <summary>
        /// 记录关卡失败
        /// </summary>
        public void RecordLevelFailure()
        {
            _currentLevelFailures++;
            _totalFailures++;
            _consecutiveSuccesses = 0;
            
            LogDebug($"记录关卡失败，当前关卡失败次数: {_currentLevelFailures}");
            
            // 检查是否需要显示提示
            if (enableHintSystem && _currentLevelFailures >= hintTriggerFailures)
            {
                TriggerHint();
            }
            
            // 如果启用适应性难度，重新评估难度
            if (enableAdaptiveDifficulty && difficultyMode == DifficultyMode.Adaptive)
            {
                var currentLevel = _levelManager?.GetCurrentLevel() ?? 0;
                UpdateDifficultyForLevel(currentLevel);
            }
        }
        
        /// <summary>
        /// 记录关卡成功
        /// </summary>
        public void RecordLevelSuccess(float completionTime)
        {
            _consecutiveSuccesses++;
            _currentLevelFailures = 0; // 重置当前关卡失败次数
            
            // 记录完成时间
            _completionTimes.Add(completionTime);
            if (_completionTimes.Count > 10) // 只保留最近10次记录
            {
                _completionTimes.RemoveAt(0);
            }
            
            // 计算平均完成时间
            float totalTime = 0f;
            foreach (float time in _completionTimes)
            {
                totalTime += time;
            }
            _averageCompletionTime = totalTime / _completionTimes.Count;
            
            LogDebug($"记录关卡成功，完成时间: {completionTime:F2}s，连续成功: {_consecutiveSuccesses}");
            
            // 如果启用适应性难度，重新评估难度
            if (enableAdaptiveDifficulty && difficultyMode == DifficultyMode.Adaptive)
            {
                var currentLevel = _levelManager?.GetCurrentLevel() ?? 0;
                UpdateDifficultyForLevel(currentLevel + 1); // 为下一关卡调整难度
            }
        }
        
        /// <summary>
        /// 触发提示
        /// </summary>
        private void TriggerHint()
        {
            if (!enableHintSystem || _currentDifficulty == null || !_currentDifficulty.enableHints)
                return;
            
            string hint = GetHintForCurrentSituation();
            if (!string.IsNullOrEmpty(hint))
            {
                LogDebug($"触发提示: {hint}");
                OnHintTriggered?.Invoke(hint);
            }
        }
        
        /// <summary>
        /// 获取当前情况的提示
        /// </summary>
        private string GetHintForCurrentSituation()
        {
            // 根据失败次数和情况提供不同的提示
            var hints = new List<string>
            {
                "尝试跳跃来避开敌人",
                "收集更多道具来增加分数",
                "寻找检查点来保存进度",
                "观察敌人的移动模式",
                "使用平台来获得高度优势",
                "小心地面上的陷阱",
                "时机很重要，耐心等待机会"
            };
            
            if (hints.Count > 0)
            {
                int randomIndex = Random.Range(0, hints.Count);
                return hints[randomIndex];
            }
            
            return "";
        }
        
        /// <summary>
        /// 重置难度统计
        /// </summary>
        public void ResetDifficultyStats()
        {
            _currentLevelFailures = 0;
            _consecutiveSuccesses = 0;
            _adaptiveDifficultyMultiplier = 1.0f;
            _completionTimes.Clear();
            _averageCompletionTime = 0f;
            
            LogDebug("难度统计已重置");
        }
        
        /// <summary>
        /// 获取当前难度
        /// </summary>
        public DifficultyLevel GetCurrentDifficulty()
        {
            return _currentDifficulty;
        }
        
        /// <summary>
        /// 获取难度统计信息
        /// </summary>
        public DifficultyStats GetDifficultyStats()
        {
            return new DifficultyStats
            {
                currentLevelFailures = _currentLevelFailures,
                totalFailures = _totalFailures,
                consecutiveSuccesses = _consecutiveSuccesses,
                averageCompletionTime = _averageCompletionTime,
                adaptiveDifficultyMultiplier = _adaptiveDifficultyMultiplier
            };
        }
        
        /// <summary>
        /// 设置难度模式
        /// </summary>
        public void SetDifficultyMode(DifficultyMode mode)
        {
            difficultyMode = mode;
            LogDebug($"难度模式已设置为: {mode}");
        }
        
        /// <summary>
        /// 启用/禁用提示系统
        /// </summary>
        public void SetHintSystemEnabled(bool enabled)
        {
            enableHintSystem = enabled;
            LogDebug($"提示系统已{(enabled ? "启用" : "禁用")}");
        }
        
        /// <summary>
        /// 调试日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[DifficultySystem] {message}");
            }
        }
        
        #region Public API
        
        /// <summary>
        /// 获取所有可用的难度等级
        /// </summary>
        public List<DifficultyLevel> GetAvailableDifficultyLevels()
        {
            return new List<DifficultyLevel>(difficultyLevels);
        }
        
        /// <summary>
        /// 添加自定义难度等级
        /// </summary>
        public void AddDifficultyLevel(DifficultyLevel difficulty)
        {
            if (difficulty != null && !difficultyLevels.Contains(difficulty))
            {
                difficultyLevels.Add(difficulty);
                LogDebug($"添加难度等级: {difficulty.name}");
            }
        }
        
        /// <summary>
        /// 强制设置难度倍数（用于测试）
        /// </summary>
        public void SetAdaptiveDifficultyMultiplier(float multiplier)
        {
            _adaptiveDifficultyMultiplier = Mathf.Clamp(multiplier, minDifficultyMultiplier, maxDifficultyMultiplier);
            OnDifficultyMultiplierChanged?.Invoke(_adaptiveDifficultyMultiplier);
        }
        
        #endregion
    }
    
    /// <summary>
    /// 难度等级数据结构
    /// </summary>
    [System.Serializable]
    public class DifficultyLevel
    {
        public string name;
        public int levelIndex;
        
        [Range(0.1f, 3.0f)]
        public float enemySpeedMultiplier = 1.0f;
        
        [Range(0.1f, 3.0f)]
        public float enemyDamageMultiplier = 1.0f;
        
        [Range(0.1f, 3.0f)]
        public float enemyHealthMultiplier = 1.0f;
        
        [Range(0.1f, 2.0f)]
        public float collectibleSpawnRate = 1.0f;
        
        [Range(0.5f, 2.0f)]
        public float playerHealthMultiplier = 1.0f;
        
        public bool enableHints = true;
        public HintFrequency hintFrequency = HintFrequency.Medium;
    }
    
    /// <summary>
    /// 难度模式
    /// </summary>
    public enum DifficultyMode
    {
        Fixed,      // 固定难度
        Progressive, // 渐进式难度
        Adaptive    // 适应性难度
    }
    
    /// <summary>
    /// 提示频率
    /// </summary>
    public enum HintFrequency
    {
        None,   // 无提示
        Low,    // 低频率
        Medium, // 中等频率
        High    // 高频率
    }
    
    /// <summary>
    /// 难度统计信息
    /// </summary>
    [System.Serializable]
    public struct DifficultyStats
    {
        public int currentLevelFailures;
        public int totalFailures;
        public int consecutiveSuccesses;
        public float averageCompletionTime;
        public float adaptiveDifficultyMultiplier;
    }
}