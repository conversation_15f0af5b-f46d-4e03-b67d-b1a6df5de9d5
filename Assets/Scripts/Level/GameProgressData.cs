using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 游戏进度数据，用于保存和加载游戏状态
    /// </summary>
    [System.Serializable]
    public class GameProgressData
    {
        [Header("关卡进度")]
        public int currentLevel;
        public List<bool> levelCompleted = new List<bool>();
        
        [Header("检查点")]
        public Vector3 lastCheckpoint;
        public string lastCheckpointId;
        public int checkpointLevel;
        
        [Header("分数和统计")]
        public int highScore;
        public int currentScore;
        public int totalCollectibles;
        public int totalEnemiesDefeated;
        
        [Header("游戏设置")]
        public float masterVolume = 1.0f;
        public float sfxVolume = 1.0f;
        public float musicVolume = 1.0f;
        public bool hintsEnabled = true;
        
        [Header("时间戳")]
        public string lastSaveTime;
        
        /// <summary>
        /// 构造函数，初始化默认值
        /// </summary>
        public GameProgressData()
        {
            currentLevel = 0;
            levelCompleted = new List<bool>();
            lastCheckpoint = Vector3.zero;
            lastCheckpointId = "";
            checkpointLevel = 0;
            highScore = 0;
            currentScore = 0;
            totalCollectibles = 0;
            totalEnemiesDefeated = 0;
            lastSaveTime = System.DateTime.Now.ToString();
        }
        
        /// <summary>
        /// 标记关卡为已完成
        /// </summary>
        public void CompleteLevel(int levelIndex)
        {
            // 确保列表足够大
            while (levelCompleted.Count <= levelIndex)
            {
                levelCompleted.Add(false);
            }
            
            levelCompleted[levelIndex] = true;
            
            // 更新当前关卡
            if (levelIndex >= currentLevel)
            {
                currentLevel = levelIndex + 1;
            }
        }
        
        /// <summary>
        /// 检查关卡是否已完成
        /// </summary>
        public bool IsLevelCompleted(int levelIndex)
        {
            if (levelIndex < 0 || levelIndex >= levelCompleted.Count)
            {
                return false;
            }
            
            return levelCompleted[levelIndex];
        }
        
        /// <summary>
        /// 更新最高分
        /// </summary>
        public void UpdateHighScore(int newScore)
        {
            if (newScore > highScore)
            {
                highScore = newScore;
            }
        }
        
        /// <summary>
        /// 保存检查点
        /// </summary>
        public void SaveCheckpoint(Vector3 position, string checkpointId, int levelIndex)
        {
            lastCheckpoint = position;
            lastCheckpointId = checkpointId;
            checkpointLevel = levelIndex;
            lastSaveTime = System.DateTime.Now.ToString();
        }
        
        /// <summary>
        /// 重置关卡进度（保留总体统计）
        /// </summary>
        public void ResetLevelProgress()
        {
            currentScore = 0;
            lastCheckpoint = Vector3.zero;
            lastCheckpointId = "";
        }
        
        /// <summary>
        /// 完全重置游戏进度
        /// </summary>
        public void ResetAllProgress()
        {
            currentLevel = 0;
            levelCompleted.Clear();
            lastCheckpoint = Vector3.zero;
            lastCheckpointId = "";
            checkpointLevel = 0;
            currentScore = 0;
            totalCollectibles = 0;
            totalEnemiesDefeated = 0;
            // 保留最高分和设置
        }
    }
}