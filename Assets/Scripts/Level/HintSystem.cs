using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 提示系统，管理游戏中的提示显示和帮助信息
    /// </summary>
    public class HintSystem : MonoBehaviour
    {
        [Header("提示配置")]
        [SerializeField] private List<HintData> availableHints = new List<HintData>();
        [SerializeField] private float defaultDisplayDuration = 5.0f;
        [SerializeField] private float fadeInDuration = 0.5f;
        [SerializeField] private float fadeOutDuration = 0.5f;
        
        [Header("UI引用")]
        [SerializeField] private GameObject hintPanel;
        [SerializeField] private UnityEngine.UI.Text hintText;
        [SerializeField] private UnityEngine.UI.Image hintIcon;
        [SerializeField] private UnityEngine.UI.Button dismissButton;
        
        [Header("音频")]
        [SerializeField] private AudioClip hintSound;
        [SerializeField] private float soundVolume = 0.7f;
        
        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        
        // 状态
        private bool _isShowingHint = false;
        private Coroutine _currentHintCoroutine;
        private Queue<HintData> _hintQueue = new Queue<HintData>();
        
        // 事件
        public System.Action<string> OnHintShown;
        public System.Action OnHintDismissed;
        
        // 组件引用
        private AudioSource _audioSource;
        private CanvasGroup _hintCanvasGroup;
        
        private void Awake()
        {
            // 初始化组件
            _audioSource = GetComponent<AudioSource>();
            if (_audioSource == null)
            {
                _audioSource = gameObject.AddComponent<AudioSource>();
                _audioSource.playOnAwake = false;
                _audioSource.volume = soundVolume;
            }
            
            // 获取CanvasGroup组件
            if (hintPanel != null)
            {
                _hintCanvasGroup = hintPanel.GetComponent<CanvasGroup>();
                if (_hintCanvasGroup == null)
                {
                    _hintCanvasGroup = hintPanel.AddComponent<CanvasGroup>();
                }
            }
            
            // 初始化默认提示
            if (availableHints.Count == 0)
            {
                InitializeDefaultHints();
            }
        }
        
        private void Start()
        {
            // 设置UI初始状态
            if (hintPanel != null)
            {
                hintPanel.SetActive(false);
            }
            
            // 设置关闭按钮事件
            if (dismissButton != null)
            {
                dismissButton.onClick.AddListener(DismissCurrentHint);
            }
            
            LogDebug("提示系统初始化完成");
        }
        
        /// <summary>
        /// 初始化默认提示
        /// </summary>
        private void InitializeDefaultHints()
        {
            availableHints.AddRange(new List<HintData>
            {
                new HintData
                {
                    id = "movement_basic",
                    text = "使用屏幕左右两侧来移动角色",
                    category = HintCategory.Movement,
                    priority = HintPriority.High,
                    displayDuration = 4.0f
                },
                new HintData
                {
                    id = "jump_basic",
                    text = "点击跳跃按钮来跳过障碍物",
                    category = HintCategory.Movement,
                    priority = HintPriority.High,
                    displayDuration = 4.0f
                },
                new HintData
                {
                    id = "enemy_avoid",
                    text = "小心敌人！尝试跳跃或绕过它们",
                    category = HintCategory.Combat,
                    priority = HintPriority.Medium,
                    displayDuration = 5.0f
                },
                new HintData
                {
                    id = "collectible_gather",
                    text = "收集道具来增加分数和获得奖励",
                    category = HintCategory.Collectibles,
                    priority = HintPriority.Medium,
                    displayDuration = 4.0f
                },
                new HintData
                {
                    id = "checkpoint_save",
                    text = "通过检查点来保存你的进度",
                    category = HintCategory.Progress,
                    priority = HintPriority.High,
                    displayDuration = 5.0f
                },
                new HintData
                {
                    id = "platform_use",
                    text = "利用平台来到达更高的地方",
                    category = HintCategory.Navigation,
                    priority = HintPriority.Low,
                    displayDuration = 4.0f
                },
                new HintData
                {
                    id = "timing_important",
                    text = "时机很重要，观察敌人的移动模式",
                    category = HintCategory.Strategy,
                    priority = HintPriority.Medium,
                    displayDuration = 6.0f
                }
            });
        }
        
        /// <summary>
        /// 显示提示
        /// </summary>
        public void ShowHint(string hintId)
        {
            var hint = GetHintById(hintId);
            if (hint != null)
            {
                ShowHint(hint);
            }
            else
            {
                LogDebug($"未找到提示: {hintId}");
            }
        }
        
        /// <summary>
        /// 显示提示
        /// </summary>
        public void ShowHint(HintData hint)
        {
            if (hint == null)
                return;
            
            // 如果正在显示提示，加入队列
            if (_isShowingHint)
            {
                _hintQueue.Enqueue(hint);
                LogDebug($"提示已加入队列: {hint.text}");
                return;
            }
            
            // 开始显示提示
            _currentHintCoroutine = StartCoroutine(ShowHintCoroutine(hint));
        }
        
        /// <summary>
        /// 显示随机提示
        /// </summary>
        public void ShowRandomHint(HintCategory category = HintCategory.All)
        {
            var availableHintsInCategory = GetHintsByCategory(category);
            if (availableHintsInCategory.Count > 0)
            {
                int randomIndex = Random.Range(0, availableHintsInCategory.Count);
                ShowHint(availableHintsInCategory[randomIndex]);
            }
        }
        
        /// <summary>
        /// 显示提示协程
        /// </summary>
        private IEnumerator ShowHintCoroutine(HintData hint)
        {
            _isShowingHint = true;
            
            // 设置提示内容
            if (hintText != null)
            {
                hintText.text = hint.text;
            }
            
            if (hintIcon != null && hint.icon != null)
            {
                hintIcon.sprite = hint.icon;
                hintIcon.gameObject.SetActive(true);
            }
            else if (hintIcon != null)
            {
                hintIcon.gameObject.SetActive(false);
            }
            
            // 显示面板
            if (hintPanel != null)
            {
                hintPanel.SetActive(true);
            }
            
            // 淡入效果
            if (_hintCanvasGroup != null)
            {
                yield return StartCoroutine(FadeCanvasGroup(_hintCanvasGroup, 0f, 1f, fadeInDuration));
            }
            
            // 播放音效
            if (hintSound != null && _audioSource != null)
            {
                _audioSource.clip = hintSound;
                _audioSource.volume = soundVolume;
                _audioSource.Play();
            }
            
            LogDebug($"显示提示: {hint.text}");
            OnHintShown?.Invoke(hint.text);
            
            // 等待显示时间
            float displayTime = hint.displayDuration > 0 ? hint.displayDuration : defaultDisplayDuration;
            yield return new WaitForSeconds(displayTime);
            
            // 自动关闭提示
            yield return StartCoroutine(DismissHintCoroutine());
        }
        
        /// <summary>
        /// 关闭当前提示
        /// </summary>
        public void DismissCurrentHint()
        {
            if (_isShowingHint && _currentHintCoroutine != null)
            {
                StopCoroutine(_currentHintCoroutine);
                StartCoroutine(DismissHintCoroutine());
            }
        }
        
        /// <summary>
        /// 关闭提示协程
        /// </summary>
        private IEnumerator DismissHintCoroutine()
        {
            // 淡出效果
            if (_hintCanvasGroup != null)
            {
                yield return StartCoroutine(FadeCanvasGroup(_hintCanvasGroup, 1f, 0f, fadeOutDuration));
            }
            
            // 隐藏面板
            if (hintPanel != null)
            {
                hintPanel.SetActive(false);
            }
            
            _isShowingHint = false;
            _currentHintCoroutine = null;
            
            LogDebug("提示已关闭");
            OnHintDismissed?.Invoke();
            
            // 显示队列中的下一个提示
            if (_hintQueue.Count > 0)
            {
                var nextHint = _hintQueue.Dequeue();
                ShowHint(nextHint);
            }
        }
        
        /// <summary>
        /// 淡入淡出效果
        /// </summary>
        private IEnumerator FadeCanvasGroup(CanvasGroup canvasGroup, float startAlpha, float endAlpha, float duration)
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float alpha = Mathf.Lerp(startAlpha, endAlpha, elapsedTime / duration);
                canvasGroup.alpha = alpha;
                yield return null;
            }
            
            canvasGroup.alpha = endAlpha;
        }
        
        /// <summary>
        /// 根据ID获取提示
        /// </summary>
        private HintData GetHintById(string hintId)
        {
            return availableHints.Find(hint => hint.id == hintId);
        }
        
        /// <summary>
        /// 根据类别获取提示
        /// </summary>
        private List<HintData> GetHintsByCategory(HintCategory category)
        {
            if (category == HintCategory.All)
            {
                return new List<HintData>(availableHints);
            }
            
            return availableHints.FindAll(hint => hint.category == category);
        }
        
        /// <summary>
        /// 清空提示队列
        /// </summary>
        public void ClearHintQueue()
        {
            _hintQueue.Clear();
            LogDebug("提示队列已清空");
        }
        
        /// <summary>
        /// 添加自定义提示
        /// </summary>
        public void AddCustomHint(HintData hint)
        {
            if (hint != null && !availableHints.Exists(h => h.id == hint.id))
            {
                availableHints.Add(hint);
                LogDebug($"添加自定义提示: {hint.id}");
            }
        }
        
        /// <summary>
        /// 调试日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[HintSystem] {message}");
            }
        }
        
        private void OnDestroy()
        {
            // 清理事件
            if (dismissButton != null)
            {
                dismissButton.onClick.RemoveListener(DismissCurrentHint);
            }
        }
        
        #region Public API
        
        /// <summary>
        /// 检查是否正在显示提示
        /// </summary>
        public bool IsShowingHint()
        {
            return _isShowingHint;
        }
        
        /// <summary>
        /// 获取队列中的提示数量
        /// </summary>
        public int GetQueuedHintCount()
        {
            return _hintQueue.Count;
        }
        
        /// <summary>
        /// 设置默认显示时间
        /// </summary>
        public void SetDefaultDisplayDuration(float duration)
        {
            defaultDisplayDuration = duration;
        }
        
        #endregion
    }
    
    /// <summary>
    /// 提示数据结构
    /// </summary>
    [System.Serializable]
    public class HintData
    {
        public string id;
        public string text;
        public Sprite icon;
        public HintCategory category = HintCategory.General;
        public HintPriority priority = HintPriority.Medium;
        public float displayDuration = 5.0f;
    }
    
    /// <summary>
    /// 提示类别
    /// </summary>
    public enum HintCategory
    {
        All,
        General,
        Movement,
        Combat,
        Collectibles,
        Progress,
        Navigation,
        Strategy
    }
    
    /// <summary>
    /// 提示优先级
    /// </summary>
    public enum HintPriority
    {
        Low,
        Medium,
        High,
        Critical
    }
}