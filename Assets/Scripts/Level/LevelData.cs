using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 关卡数据结构，包含关卡的所有配置信息
    /// </summary>
    [System.Serializable]
    public class LevelData
    {
        [Header("基本信息")]
        public string levelName;
        public int levelIndex;
        public string sceneName;
        
        [Header("生成点")]
        public Vector3 spawnPoint;
        
        [Header("检查点")]
        public List<CheckpointData> checkpoints = new List<CheckpointData>();
        
        [Header("敌人生成")]
        public List<EnemySpawnData> enemies = new List<EnemySpawnData>();
        
        [Header("收集品")]
        public List<CollectibleSpawnData> collectibles = new List<CollectibleSpawnData>();
        
        [Header("关卡目标")]
        public LevelObjective objective;
        public int targetScore;
        public float timeLimit; // 0表示无时间限制
        
        [Header("难度设置")]
        public DifficultySettings difficulty;
    }
    
    /// <summary>
    /// 检查点数据
    /// </summary>
    [System.Serializable]
    public class CheckpointData
    {
        public Vector3 position;
        public bool isActivated;
        public string checkpointId;
    }
    
    /// <summary>
    /// 敌人生成数据
    /// </summary>
    [System.Serializable]
    public class EnemySpawnData
    {
        public Vector3 spawnPosition;
        public string enemyType;
        public bool isActive;
    }
    
    /// <summary>
    /// 收集品生成数据
    /// </summary>
    [System.Serializable]
    public class CollectibleSpawnData
    {
        public Vector3 spawnPosition;
        public string collectibleType;
        public int value;
        public bool isCollected;
    }
    
    /// <summary>
    /// 关卡目标类型
    /// </summary>
    public enum LevelObjective
    {
        ReachEnd,      // 到达终点
        CollectItems,  // 收集物品
        DefeatEnemies, // 击败敌人
        Survival,      // 生存时间
        ScoreTarget    // 达到目标分数
    }
    
    /// <summary>
    /// 难度设置
    /// </summary>
    [System.Serializable]
    public class DifficultySettings
    {
        [Range(0.5f, 2.0f)]
        public float enemySpeedMultiplier = 1.0f;
        
        [Range(0.5f, 2.0f)]
        public float enemyDamageMultiplier = 1.0f;
        
        [Range(1, 10)]
        public int enemyCount = 1;
        
        [Range(0.1f, 1.0f)]
        public float collectibleSpawnRate = 1.0f;
        
        public bool enableHints = true;
    }
}