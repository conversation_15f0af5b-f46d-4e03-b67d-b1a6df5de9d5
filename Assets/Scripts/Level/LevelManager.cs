using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using System.Collections.Generic;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Level
{
    /// <summary>
    /// 关卡管理器实现，处理关卡加载、进度跟踪和检查点系统
    /// </summary>
    public class LevelManager : MonoBeh<PERSON>our, ILevelManager
    {
        [Header("关卡配置")]
        [SerializeField] private List<LevelData> levels = new List<LevelData>();
        [SerializeField] private int maxLevels = 10;
        
        [Header("加载设置")]
        [SerializeField] private float loadingDelay = 1.0f;
        [SerializeField] private bool showLoadingScreen = true;
        
        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        
        // 当前状态
        private GameProgressData _gameProgress;
        private LevelData _currentLevelData;
        private bool _isLoading = false;
        
        // 事件
        public System.Action<int> OnLevelStarted;
        public System.Action<int> OnLevelCompleted;
        public System.Action<Vector3> OnCheckpointSaved;
        public System.Action OnPlayerRespawned;
        public System.Action<float> OnLoadingProgress;
        
        // 组件引用
        private GameObject _player;
        
        private void Awake()
        {
            // 初始化游戏进度
            _gameProgress = new GameProgressData();
            
            // 注册到GameManager
            if (GameManager.Instance != null)
            {
                GameManager.Instance.RegisterLevelManager(this);
            }
            
            // 加载保存的进度
            LoadGameProgress();
        }
        
        private void Start()
        {
            // 查找玩家对象
            _player = GameObject.FindGameObjectWithTag("Player");
            
            if (_player == null)
            {
                LogDebug("警告: 未找到带有'Player'标签的对象");
            }
        } 
       
        #region ILevelManager Implementation
        
        /// <summary>
        /// 异步加载关卡
        /// </summary>
        public IEnumerator LoadLevel(int levelIndex)
        {
            if (_isLoading)
            {
                LogDebug($"关卡加载中，忽略加载请求: {levelIndex}");
                yield break;
            }
            
            if (levelIndex < 0 || levelIndex >= levels.Count)
            {
                LogDebug($"无效的关卡索引: {levelIndex}");
                yield break;
            }
            
            _isLoading = true;
            _currentLevelData = levels[levelIndex];
            
            LogDebug($"开始加载关卡: {_currentLevelData.levelName} (索引: {levelIndex})");
            
            // 显示加载进度
            OnLoadingProgress?.Invoke(0.0f);
            
            // 如果需要加载新场景
            if (!string.IsNullOrEmpty(_currentLevelData.sceneName))
            {
                AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(_currentLevelData.sceneName);
                asyncLoad.allowSceneActivation = false;
                
                // 等待场景加载完成
                while (asyncLoad.progress < 0.9f)
                {
                    OnLoadingProgress?.Invoke(asyncLoad.progress * 0.8f);
                    yield return null;
                }
                
                // 激活场景
                asyncLoad.allowSceneActivation = true;
                yield return asyncLoad;
            }
            
            // 加载延迟
            if (loadingDelay > 0)
            {
                yield return new WaitForSeconds(loadingDelay);
            }
            
            // 初始化关卡
            yield return StartCoroutine(InitializeLevel(levelIndex));
            
            OnLoadingProgress?.Invoke(1.0f);
            _isLoading = false;
            
            LogDebug($"关卡加载完成: {_currentLevelData.levelName}");
            OnLevelStarted?.Invoke(levelIndex);
        }
        
        /// <summary>
        /// 保存检查点
        /// </summary>
        public void SaveCheckpoint(Vector3 checkpointPosition)
        {
            if (_currentLevelData == null)
            {
                LogDebug("无法保存检查点: 当前关卡数据为空");
                return;
            }
            
            string checkpointId = $"checkpoint_{checkpointPosition.x}_{checkpointPosition.y}";
            int currentLevel = GetCurrentLevel();
            
            _gameProgress.SaveCheckpoint(checkpointPosition, checkpointId, currentLevel);
            
            // 更新关卡数据中的检查点状态
            UpdateCheckpointInLevelData(checkpointPosition);
            
            // 保存游戏进度
            SaveGameProgress();
            
            LogDebug($"检查点已保存: {checkpointPosition} (关卡: {currentLevel})");
            OnCheckpointSaved?.Invoke(checkpointPosition);
        }   
     
        /// <summary>
        /// 在检查点重生
        /// </summary>
        public void RespawnAtCheckpoint()
        {
            if (_player == null)
            {
                LogDebug("无法重生: 玩家对象未找到");
                return;
            }
            
            Vector3 respawnPosition = _gameProgress.lastCheckpoint;
            
            // 如果没有保存的检查点，使用关卡起始点
            if (respawnPosition == Vector3.zero && _currentLevelData != null)
            {
                respawnPosition = _currentLevelData.spawnPoint;
            }
            
            // 移动玩家到重生位置
            _player.transform.position = respawnPosition;
            
            // 重置玩家状态（如果有Health组件）
            var healthComponent = _player.GetComponent<Health>();
            if (healthComponent != null)
            {
                healthComponent.Respawn();
            }
            
            LogDebug($"玩家已在检查点重生: {respawnPosition}");
            OnPlayerRespawned?.Invoke();
        }
        
        /// <summary>
        /// 完成当前关卡
        /// </summary>
        public void CompleteLevel()
        {
            int currentLevel = GetCurrentLevel();
            
            if (currentLevel < 0)
            {
                LogDebug("无法完成关卡: 当前关卡无效");
                return;
            }
            
            // 标记关卡为已完成
            _gameProgress.CompleteLevel(currentLevel);
            
            // 保存进度
            SaveGameProgress();
            
            LogDebug($"关卡完成: {currentLevel}");
            OnLevelCompleted?.Invoke(currentLevel);
            
            // 自动加载下一关卡（如果存在）
            int nextLevel = currentLevel + 1;
            if (nextLevel < levels.Count)
            {
                StartCoroutine(LoadLevel(nextLevel));
            }
            else
            {
                LogDebug("所有关卡已完成！");
                // 可以触发游戏完成事件
            }
        }
        
        /// <summary>
        /// 获取当前关卡索引
        /// </summary>
        public int GetCurrentLevel()
        {
            return _gameProgress.currentLevel;
        }
        
        /// <summary>
        /// 检查关卡是否已完成
        /// </summary>
        public bool IsLevelCompleted(int levelIndex)
        {
            return _gameProgress.IsLevelCompleted(levelIndex);
        }
        
        /// <summary>
        /// 重置当前关卡
        /// </summary>
        public void ResetLevel()
        {
            int currentLevel = GetCurrentLevel();
            
            if (currentLevel >= 0 && currentLevel < levels.Count)
            {
                _gameProgress.ResetLevelProgress();
                StartCoroutine(LoadLevel(currentLevel));
                LogDebug($"关卡已重置: {currentLevel}");
            }
        }
        
        #endregion 
       
        #region Private Methods
        
        /// <summary>
        /// 初始化关卡
        /// </summary>
        private IEnumerator InitializeLevel(int levelIndex)
        {
            if (_currentLevelData == null)
                yield break;
            
            // 设置玩家生成位置
            if (_player != null)
            {
                Vector3 spawnPosition = _gameProgress.lastCheckpoint != Vector3.zero 
                    ? _gameProgress.lastCheckpoint 
                    : _currentLevelData.spawnPoint;
                    
                _player.transform.position = spawnPosition;
            }
            
            // 生成敌人
            yield return StartCoroutine(SpawnEnemies());
            
            // 生成收集品
            yield return StartCoroutine(SpawnCollectibles());
            
            // 激活检查点
            yield return StartCoroutine(SetupCheckpoints());
            
            LogDebug($"关卡初始化完成: {_currentLevelData.levelName}");
        }
        
        /// <summary>
        /// 生成敌人
        /// </summary>
        private IEnumerator SpawnEnemies()
        {
            if (_currentLevelData.enemies == null)
                yield break;
            
            foreach (var enemyData in _currentLevelData.enemies)
            {
                if (enemyData.isActive)
                {
                    // 这里应该实例化敌人预制体
                    // GameObject enemy = Instantiate(enemyPrefab, enemyData.spawnPosition, Quaternion.identity);
                    LogDebug($"生成敌人: {enemyData.enemyType} 在位置 {enemyData.spawnPosition}");
                }
                yield return null; // 分帧处理
            }
        }
        
        /// <summary>
        /// 生成收集品
        /// </summary>
        private IEnumerator SpawnCollectibles()
        {
            if (_currentLevelData.collectibles == null)
                yield break;
            
            foreach (var collectibleData in _currentLevelData.collectibles)
            {
                if (!collectibleData.isCollected)
                {
                    // 这里应该实例化收集品预制体
                    // GameObject collectible = Instantiate(collectiblePrefab, collectibleData.spawnPosition, Quaternion.identity);
                    LogDebug($"生成收集品: {collectibleData.collectibleType} 在位置 {collectibleData.spawnPosition}");
                }
                yield return null; // 分帧处理
            }
        }
        
        /// <summary>
        /// 设置检查点
        /// </summary>
        private IEnumerator SetupCheckpoints()
        {
            if (_currentLevelData.checkpoints == null)
                yield break;
            
            foreach (var checkpointData in _currentLevelData.checkpoints)
            {
                // 这里应该设置检查点对象
                LogDebug($"设置检查点: {checkpointData.checkpointId} 在位置 {checkpointData.position}");
                yield return null; // 分帧处理
            }
        }   
     
        /// <summary>
        /// 更新关卡数据中的检查点状态
        /// </summary>
        private void UpdateCheckpointInLevelData(Vector3 position)
        {
            if (_currentLevelData?.checkpoints == null)
                return;
            
            foreach (var checkpoint in _currentLevelData.checkpoints)
            {
                if (Vector3.Distance(checkpoint.position, position) < 1.0f)
                {
                    checkpoint.isActivated = true;
                    break;
                }
            }
        }
        
        /// <summary>
        /// 保存游戏进度到本地存储
        /// </summary>
        private void SaveGameProgress()
        {
            try
            {
                string json = JsonUtility.ToJson(_gameProgress, true);
                PlayerPrefs.SetString("GameProgress", json);
                PlayerPrefs.Save();
                LogDebug("游戏进度已保存");
            }
            catch (System.Exception e)
            {
                LogDebug($"保存游戏进度失败: {e.Message}");
            }
        }
        
        /// <summary>
        /// 从本地存储加载游戏进度
        /// </summary>
        private void LoadGameProgress()
        {
            try
            {
                if (PlayerPrefs.HasKey("GameProgress"))
                {
                    string json = PlayerPrefs.GetString("GameProgress");
                    _gameProgress = JsonUtility.FromJson<GameProgressData>(json);
                    LogDebug("游戏进度已加载");
                }
                else
                {
                    _gameProgress = new GameProgressData();
                    LogDebug("创建新的游戏进度");
                }
            }
            catch (System.Exception e)
            {
                LogDebug($"加载游戏进度失败: {e.Message}");
                _gameProgress = new GameProgressData();
            }
        }
        
        /// <summary>
        /// 调试日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[LevelManager] {message}");
            }
        }
        
        #endregion 
       
        #region Public API
        
        /// <summary>
        /// 获取关卡数据
        /// </summary>
        public LevelData GetLevelData(int levelIndex)
        {
            if (levelIndex >= 0 && levelIndex < levels.Count)
            {
                return levels[levelIndex];
            }
            return null;
        }
        
        /// <summary>
        /// 获取当前关卡数据
        /// </summary>
        public LevelData GetCurrentLevelData()
        {
            return _currentLevelData;
        }
        
        /// <summary>
        /// 获取游戏进度数据
        /// </summary>
        public GameProgressData GetGameProgress()
        {
            return _gameProgress;
        }
        
        /// <summary>
        /// 获取总关卡数
        /// </summary>
        public int GetTotalLevels()
        {
            return levels.Count;
        }
        
        /// <summary>
        /// 添加新关卡数据
        /// </summary>
        public void AddLevelData(LevelData levelData)
        {
            if (levels.Count < maxLevels)
            {
                levels.Add(levelData);
                LogDebug($"添加新关卡: {levelData.levelName}");
            }
        }
        
        /// <summary>
        /// 清除所有游戏进度
        /// </summary>
        public void ClearAllProgress()
        {
            _gameProgress.ResetAllProgress();
            SaveGameProgress();
            LogDebug("所有游戏进度已清除");
        }
        
        #endregion
    }
}