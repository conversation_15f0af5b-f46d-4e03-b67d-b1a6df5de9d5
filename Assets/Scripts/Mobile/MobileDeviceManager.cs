using UnityEngine;
using System.Collections;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Mobile
{
    /// <summary>
    /// 移动设备管理器
    /// 处理移动设备特定功能，包括应用生命周期、设备方向、性能优化等
    /// </summary>
    public class MobileDeviceManager : MonoBehaviour
    {
        [Header("应用生命周期设置")]
        [SerializeField] private bool enableApplicationPauseHandling = true;
        [SerializeField] private bool enableApplicationFocusHandling = true;
        [SerializeField] private float pauseDelayTime = 0.5f;
        
        [Header("设备方向设置")]
        [SerializeField] private bool enableOrientationHandling = true;
        [SerializeField] private ScreenOrientation[] supportedOrientations = { ScreenOrientation.LandscapeLeft, ScreenOrientation.LandscapeRight };
        [SerializeField] private bool autoRotateToPortrait = false;
        [SerializeField] private bool autoRotateToPortraitUpsideDown = false;
        [SerializeField] private bool autoRotateToLandscapeLeft = true;
        [SerializeField] private bool autoRotateToLandscapeRight = true;
        
        [Header("性能优化设置")]
        [SerializeField] private bool enablePerformanceOptimization = true;
        [SerializeField] private bool enableLowPowerMode = true;
        [SerializeField] private float lowBatteryThreshold = 0.2f;
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private int lowPowerFrameRate = 30;
        
        [Header("内存管理设置")]
        [SerializeField] private bool enableMemoryManagement = true;
        [SerializeField] private float memoryCheckInterval = 5f;
        [SerializeField] private long memoryWarningThreshold = 100 * 1024 * 1024; // 100MB
        [SerializeField] private bool enableGarbageCollection = true;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logDeviceInfo = true;
        
        // 状态变量
        private bool isApplicationPaused = false;
        private bool isApplicationFocused = true;
        private bool isLowPowerModeActive = false;
        private ScreenOrientation currentOrientation;
        private float lastMemoryCheck = 0f;
        
        // 组件引用
        private GameManager gameManager;
        private AudioManager audioManager;
        
        // 事件
        public System.Action OnApplicationPauseEvent;
        public System.Action OnApplicationResumeEvent;
        public System.Action OnApplicationFocusLostEvent;
        public System.Action OnApplicationFocusGainedEvent;
        public System.Action<ScreenOrientation> OnOrientationChanged;
        public System.Action OnLowPowerModeActivated;
        public System.Action OnLowPowerModeDeactivated;
        public System.Action OnMemoryWarning;
        
        // 属性
        public bool IsApplicationPaused => isApplicationPaused;
        public bool IsApplicationFocused => isApplicationFocused;
        public bool IsLowPowerModeActive => isLowPowerModeActive;
        public ScreenOrientation CurrentOrientation => currentOrientation;
        
        private void Awake()
        {
            InitializeMobileManager();
        }
        
        private void Start()
        {
            RegisterWithGameSystems();
            LogDeviceInformation();
            SetupInitialConfiguration();
        }
        
        private void Update()
        {
            if (enablePerformanceOptimization)
            {
                CheckBatteryLevel();
                CheckMemoryUsage();
            }
            
            if (enableOrientationHandling)
            {
                CheckOrientationChange();
            }
        }
        
        /// <summary>
        /// 初始化移动设备管理器
        /// </summary>
        private void InitializeMobileManager()
        {
            // 确保只在移动设备上运行
            if (!Application.isMobilePlatform)
            {
                if (showDebugInfo)
                {
                    Debug.Log("MobileDeviceManager: 非移动平台，部分功能将被禁用");
                }
            }
            
            currentOrientation = Screen.orientation;
        }
        
        /// <summary>
        /// 注册到游戏系统
        /// </summary>
        private void RegisterWithGameSystems()
        {
            gameManager = GameManager.Instance;
            audioManager = AudioManager.Instance;
        }
        
        /// <summary>
        /// 记录设备信息
        /// </summary>
        private void LogDeviceInformation()
        {
            if (!logDeviceInfo) return;
            
            Debug.Log($"设备信息:");
            Debug.Log($"- 设备型号: {SystemInfo.deviceModel}");
            Debug.Log($"- 操作系统: {SystemInfo.operatingSystem}");
            Debug.Log($"- 处理器: {SystemInfo.processorType} ({SystemInfo.processorCount} 核心)");
            Debug.Log($"- 内存: {SystemInfo.systemMemorySize} MB");
            Debug.Log($"- 显卡: {SystemInfo.graphicsDeviceName}");
            Debug.Log($"- 显存: {SystemInfo.graphicsMemorySize} MB");
            Debug.Log($"- 屏幕分辨率: {Screen.width}x{Screen.height}");
            Debug.Log($"- 屏幕DPI: {Screen.dpi}");
            Debug.Log($"- 电池电量: {SystemInfo.batteryLevel * 100:F1}%");
            Debug.Log($"- 电池状态: {SystemInfo.batteryStatus}");
        }
        
        /// <summary>
        /// 设置初始配置
        /// </summary>
        private void SetupInitialConfiguration()
        {
            // 设置目标帧率
            Application.targetFrameRate = targetFrameRate;
            
            // 设置屏幕方向
            if (enableOrientationHandling)
            {
                SetupScreenOrientation();
            }
            
            // 设置性能优化
            if (enablePerformanceOptimization)
            {
                SetupPerformanceOptimization();
            }
        }
        
        /// <summary>
        /// 设置屏幕方向
        /// </summary>
        private void SetupScreenOrientation()
        {
            Screen.autorotateToPortrait = autoRotateToPortrait;
            Screen.autorotateToPortraitUpsideDown = autoRotateToPortraitUpsideDown;
            Screen.autorotateToLandscapeLeft = autoRotateToLandscapeLeft;
            Screen.autorotateToLandscapeRight = autoRotateToLandscapeRight;
            
            // 如果只支持一个方向，直接设置
            if (supportedOrientations.Length == 1)
            {
                Screen.orientation = supportedOrientations[0];
            }
            else
            {
                Screen.orientation = ScreenOrientation.AutoRotation;
            }
        }
        
        /// <summary>
        /// 设置性能优化
        /// </summary>
        private void SetupPerformanceOptimization()
        {
            // 设置质量等级
            if (Application.isMobilePlatform)
            {
                // 根据设备性能调整质量
                AdjustQualityBasedOnDevice();
            }
        }
        
        /// <summary>
        /// 根据设备性能调整质量
        /// </summary>
        private void AdjustQualityBasedOnDevice()
        {
            int memoryMB = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;
            
            if (memoryMB < 2048 || processorCount < 4)
            {
                // 低端设备
                QualitySettings.SetQualityLevel(0, true); // 最低质量
                Application.targetFrameRate = 30;
            }
            else if (memoryMB < 4096 || processorCount < 6)
            {
                // 中端设备
                QualitySettings.SetQualityLevel(2, true); // 中等质量
                Application.targetFrameRate = 45;
            }
            else
            {
                // 高端设备
                QualitySettings.SetQualityLevel(4, true); // 高质量
                Application.targetFrameRate = 60;
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"设备性能评估: 内存{memoryMB}MB, CPU{processorCount}核, 质量等级{QualitySettings.GetQualityLevel()}");
            }
        }
        
        /// <summary>
        /// 检查电池电量
        /// </summary>
        private void CheckBatteryLevel()
        {
            if (!enableLowPowerMode) return;
            
            float batteryLevel = SystemInfo.batteryLevel;
            bool shouldActivateLowPower = batteryLevel > 0 && batteryLevel < lowBatteryThreshold;
            
            if (shouldActivateLowPower && !isLowPowerModeActive)
            {
                ActivateLowPowerMode();
            }
            else if (!shouldActivateLowPower && isLowPowerModeActive)
            {
                DeactivateLowPowerMode();
            }
        }
        
        /// <summary>
        /// 激活低电量模式
        /// </summary>
        private void ActivateLowPowerMode()
        {
            isLowPowerModeActive = true;
            
            // 降低帧率
            Application.targetFrameRate = lowPowerFrameRate;
            
            // 降低质量设置
            int currentQuality = QualitySettings.GetQualityLevel();
            if (currentQuality > 0)
            {
                QualitySettings.SetQualityLevel(currentQuality - 1, true);
            }
            
            OnLowPowerModeActivated?.Invoke();
            
            if (showDebugInfo)
            {
                Debug.Log($"低电量模式已激活 (电量: {SystemInfo.batteryLevel * 100:F1}%)");
            }
        }
        
        /// <summary>
        /// 停用低电量模式
        /// </summary>
        private void DeactivateLowPowerMode()
        {
            isLowPowerModeActive = false;
            
            // 恢复正常帧率
            Application.targetFrameRate = targetFrameRate;
            
            // 恢复质量设置
            AdjustQualityBasedOnDevice();
            
            OnLowPowerModeDeactivated?.Invoke();
            
            if (showDebugInfo)
            {
                Debug.Log($"低电量模式已停用 (电量: {SystemInfo.batteryLevel * 100:F1}%)");
            }
        }
        
        /// <summary>
        /// 检查内存使用情况
        /// </summary>
        private void CheckMemoryUsage()
        {
            if (!enableMemoryManagement) return;
            if (Time.time - lastMemoryCheck < memoryCheckInterval) return;
            
            lastMemoryCheck = Time.time;
            
            long memoryUsage = System.GC.GetTotalMemory(false);
            
            if (memoryUsage > memoryWarningThreshold)
            {
                OnMemoryWarning?.Invoke();
                
                if (enableGarbageCollection)
                {
                    System.GC.Collect();
                    Resources.UnloadUnusedAssets();
                }
                
                if (showDebugInfo)
                {
                    Debug.LogWarning($"内存使用警告: {memoryUsage / (1024 * 1024)}MB");
                }
            }
        }
        
        /// <summary>
        /// 检查屏幕方向变化
        /// </summary>
        private void CheckOrientationChange()
        {
            if (Screen.orientation != currentOrientation)
            {
                ScreenOrientation oldOrientation = currentOrientation;
                currentOrientation = Screen.orientation;
                
                OnOrientationChanged?.Invoke(currentOrientation);
                
                if (showDebugInfo)
                {
                    Debug.Log($"屏幕方向变化: {oldOrientation} -> {currentOrientation}");
                }
            }
        }
        
        #region Unity应用生命周期事件
        
        /// <summary>
        /// 应用暂停事件
        /// </summary>
        private void OnApplicationPause(bool pauseStatus)
        {
            if (!enableApplicationPauseHandling) return;
            
            if (pauseStatus)
            {
                StartCoroutine(HandleApplicationPause());
            }
            else
            {
                HandleApplicationResume();
            }
        }
        
        /// <summary>
        /// 应用焦点事件
        /// </summary>
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!enableApplicationFocusHandling) return;
            
            isApplicationFocused = hasFocus;
            
            if (hasFocus)
            {
                OnApplicationFocusGainedEvent?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log("应用获得焦点");
                }
            }
            else
            {
                OnApplicationFocusLostEvent?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log("应用失去焦点");
                }
            }
        }
        
        /// <summary>
        /// 处理应用暂停
        /// </summary>
        private IEnumerator HandleApplicationPause()
        {
            yield return new WaitForSeconds(pauseDelayTime);
            
            if (!isApplicationFocused)
            {
                isApplicationPaused = true;
                
                // 暂停游戏
                if (gameManager != null)
                {
                    gameManager.PauseGame();
                }
                
                // 暂停音频
                if (audioManager != null)
                {
                    AudioListener.pause = true;
                }
                
                OnApplicationPauseEvent?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log("应用已暂停");
                }
            }
        }
        
        /// <summary>
        /// 处理应用恢复
        /// </summary>
        private void HandleApplicationResume()
        {
            if (isApplicationPaused)
            {
                isApplicationPaused = false;
                
                // 恢复音频
                if (audioManager != null)
                {
                    AudioListener.pause = false;
                }
                
                OnApplicationResumeEvent?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log("应用已恢复");
                }
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 强制垃圾回收
        /// </summary>
        public void ForceGarbageCollection()
        {
            System.GC.Collect();
            Resources.UnloadUnusedAssets();
            
            if (showDebugInfo)
            {
                Debug.Log("强制垃圾回收完成");
            }
        }
        
        /// <summary>
        /// 设置目标帧率
        /// </summary>
        public void SetTargetFrameRate(int frameRate)
        {
            targetFrameRate = frameRate;
            if (!isLowPowerModeActive)
            {
                Application.targetFrameRate = frameRate;
            }
        }
        
        /// <summary>
        /// 获取设备性能等级
        /// </summary>
        public DevicePerformanceLevel GetDevicePerformanceLevel()
        {
            int memoryMB = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;
            
            if (memoryMB < 2048 || processorCount < 4)
            {
                return DevicePerformanceLevel.Low;
            }
            else if (memoryMB < 4096 || processorCount < 6)
            {
                return DevicePerformanceLevel.Medium;
            }
            else
            {
                return DevicePerformanceLevel.High;
            }
        }
        
        /// <summary>
        /// 获取内存使用信息
        /// </summary>
        public MemoryInfo GetMemoryInfo()
        {
            return new MemoryInfo
            {
                totalSystemMemory = SystemInfo.systemMemorySize,
                usedMemory = System.GC.GetTotalMemory(false),
                availableMemory = SystemInfo.systemMemorySize * 1024 * 1024 - System.GC.GetTotalMemory(false)
            };
        }
        
        #endregion
        
        #region 静态实例管理
        
        private static MobileDeviceManager instance;
        public static MobileDeviceManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<MobileDeviceManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("MobileDeviceManager");
                        instance = go.AddComponent<MobileDeviceManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 设备性能等级
    /// </summary>
    public enum DevicePerformanceLevel
    {
        Low,
        Medium,
        High
    }
    
    /// <summary>
    /// 内存信息
    /// </summary>
    public struct MemoryInfo
    {
        public int totalSystemMemory;
        public long usedMemory;
        public long availableMemory;
    }
}
