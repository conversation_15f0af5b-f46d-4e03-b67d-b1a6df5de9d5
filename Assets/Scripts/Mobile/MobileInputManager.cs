using UnityEngine;
using System.Collections.Generic;
using MobileScrollingGame.Core;
using MobileScrollingGame.UI;
using UnityInput = UnityEngine.Input;
using UnityCamera = UnityEngine.Camera;

namespace MobileScrollingGame.Mobile
{
    /// <summary>
    /// 移动输入管理器
    /// 处理触摸输入、手势识别和移动设备特定的输入功能
    /// </summary>
    public class MobileInputManager : MonoBehaviour, IInputHandler
    {
        [Header("触摸设置")]
        [SerializeField] private bool enableTouchInput = true;
        [SerializeField] private float touchSensitivity = 1f;
        [SerializeField] private float swipeThreshold = 50f;
        [SerializeField] private float tapTimeThreshold = 0.3f;
        [SerializeField] private float doubleTapTimeThreshold = 0.5f;
        
        [Header("手势识别")]
        [SerializeField] private bool enableSwipeGestures = true;
        [SerializeField] private bool enablePinchGestures = true;
        [SerializeField] private bool enableTapGestures = true;
        [SerializeField] private float pinchSensitivity = 1f;
        
        [Header("虚拟控制")]
        [SerializeField] private bool enableVirtualControls = true;
        [SerializeField] private GameObject virtualJoystickPrefab;
        [SerializeField] private GameObject virtualButtonsPrefab;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool showTouchVisualization = false;
        
        // 触摸状态
        private Dictionary<int, TouchInfo> activeTouches = new Dictionary<int, TouchInfo>();
        private List<SwipeGesture> detectedSwipes = new List<SwipeGesture>();
        private float lastTapTime = 0f;
        private Vector2 lastTapPosition = Vector2.zero;
        
        // 输入状态
        private Vector2 movementInput = Vector2.zero;
        private bool jumpInput = false;
        private bool attackInput = false;
        private bool pauseInput = false;
        private bool isInputEnabled = true;
        
        // 虚拟控制组件
        private GameObject virtualJoystick;
        private GameObject virtualButtons;
        
        /// <summary>
        /// 触摸信息
        /// </summary>
        private class TouchInfo
        {
            public int fingerId;
            public Vector2 startPosition;
            public Vector2 currentPosition;
            public Vector2 deltaPosition;
            public float startTime;
            public float duration;
            public TouchPhase phase;
            public bool isSwipe;
            
            public TouchInfo(Touch touch)
            {
                fingerId = touch.fingerId;
                startPosition = touch.position;
                currentPosition = touch.position;
                deltaPosition = Vector2.zero;
                startTime = Time.time;
                duration = 0f;
                phase = touch.phase;
                isSwipe = false;
            }
            
            public void UpdateTouch(Touch touch)
            {
                Vector2 previousPosition = currentPosition;
                currentPosition = touch.position;
                deltaPosition = currentPosition - previousPosition;
                duration = Time.time - startTime;
                phase = touch.phase;
            }
            
            public Vector2 GetSwipeDirection()
            {
                return (currentPosition - startPosition).normalized;
            }
            
            public float GetSwipeDistance()
            {
                return Vector2.Distance(startPosition, currentPosition);
            }
        }
        
        /// <summary>
        /// 滑动手势
        /// </summary>
        public struct SwipeGesture
        {
            public Vector2 startPosition;
            public Vector2 endPosition;
            public Vector2 direction;
            public float distance;
            public float duration;
            public SwipeDirection swipeDirection;
        }
        
        /// <summary>
        /// 滑动方向
        /// </summary>
        public enum SwipeDirection
        {
            None,
            Up,
            Down,
            Left,
            Right,
            UpLeft,
            UpRight,
            DownLeft,
            DownRight
        }
        
        // 事件
        public System.Action<SwipeGesture> OnSwipeDetected;
        public System.Action<Vector2> OnTap;
        public System.Action<Vector2> OnDoubleTap;
        public System.Action<float> OnPinch;
        public System.Action<Vector2> OnMovementInput;
        public System.Action OnJumpInput;
        public System.Action OnAttackInput;
        public System.Action OnPauseInput;
        
        // IInputHandler 属性
        public Vector2 MovementInput => movementInput;
        public bool JumpInput => jumpInput;
        public bool AttackInput => attackInput;
        public bool PauseInput => pauseInput;
        public bool IsInputEnabled => isInputEnabled;
        
        private void Awake()
        {
            InitializeMobileInput();
        }
        
        private void Start()
        {
            RegisterWithGameManager();
            SetupVirtualControls();
        }
        
        private void Update()
        {
            if (!isInputEnabled) return;
            
            if (enableTouchInput)
            {
                ProcessTouchInput();
            }
            
            ProcessVirtualControls();
            
            // 重置单帧输入
            jumpInput = false;
            attackInput = false;
            pauseInput = false;
        }
        
        /// <summary>
        /// 初始化移动输入
        /// </summary>
        private void InitializeMobileInput()
        {
            // 在移动设备上启用多点触控
            UnityInput.multiTouchEnabled = true;

            // 设置触摸模拟
            if (!Application.isMobilePlatform)
            {
                UnityInput.simulateMouseWithTouches = true;
            }
        }
        
        /// <summary>
        /// 注册到游戏管理器
        /// </summary>
        private void RegisterWithGameManager()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.RegisterInputHandler(this);
            }
        }
        
        /// <summary>
        /// 设置虚拟控制
        /// </summary>
        private void SetupVirtualControls()
        {
            if (!enableVirtualControls) return;
            
            // 创建虚拟摇杆
            if (virtualJoystickPrefab != null)
            {
                virtualJoystick = Instantiate(virtualJoystickPrefab);
                virtualJoystick.transform.SetParent(transform);
            }
            
            // 创建虚拟按钮
            if (virtualButtonsPrefab != null)
            {
                virtualButtons = Instantiate(virtualButtonsPrefab);
                virtualButtons.transform.SetParent(transform);
            }
        }
        
        /// <summary>
        /// 处理触摸输入
        /// </summary>
        private void ProcessTouchInput()
        {
            // 处理所有触摸点
            for (int i = 0; i < UnityInput.touchCount; i++)
            {
                Touch touch = UnityInput.GetTouch(i);
                ProcessTouch(touch);
            }
            
            // 清理结束的触摸
            CleanupFinishedTouches();
            
            // 检测手势
            if (enableSwipeGestures)
            {
                DetectSwipeGestures();
            }
            
            if (enablePinchGestures)
            {
                DetectPinchGestures();
            }
        }
        
        /// <summary>
        /// 处理单个触摸
        /// </summary>
        private void ProcessTouch(Touch touch)
        {
            int fingerId = touch.fingerId;
            
            switch (touch.phase)
            {
                case TouchPhase.Began:
                    HandleTouchBegan(touch);
                    break;
                    
                case TouchPhase.Moved:
                    HandleTouchMoved(touch);
                    break;
                    
                case TouchPhase.Stationary:
                    HandleTouchStationary(touch);
                    break;
                    
                case TouchPhase.Ended:
                    HandleTouchEnded(touch);
                    break;
                    
                case TouchPhase.Canceled:
                    HandleTouchCanceled(touch);
                    break;
            }
        }
        
        /// <summary>
        /// 处理触摸开始
        /// </summary>
        private void HandleTouchBegan(Touch touch)
        {
            TouchInfo touchInfo = new TouchInfo(touch);
            activeTouches[touch.fingerId] = touchInfo;
            
            if (showDebugInfo)
            {
                Debug.Log($"触摸开始: ID={touch.fingerId}, 位置={touch.position}");
            }
        }
        
        /// <summary>
        /// 处理触摸移动
        /// </summary>
        private void HandleTouchMoved(Touch touch)
        {
            if (activeTouches.ContainsKey(touch.fingerId))
            {
                TouchInfo touchInfo = activeTouches[touch.fingerId];
                touchInfo.UpdateTouch(touch);
                
                // 检查是否是滑动
                if (!touchInfo.isSwipe && touchInfo.GetSwipeDistance() > swipeThreshold)
                {
                    touchInfo.isSwipe = true;
                }
            }
        }
        
        /// <summary>
        /// 处理触摸静止
        /// </summary>
        private void HandleTouchStationary(Touch touch)
        {
            if (activeTouches.ContainsKey(touch.fingerId))
            {
                TouchInfo touchInfo = activeTouches[touch.fingerId];
                touchInfo.UpdateTouch(touch);
            }
        }
        
        /// <summary>
        /// 处理触摸结束
        /// </summary>
        private void HandleTouchEnded(Touch touch)
        {
            if (activeTouches.ContainsKey(touch.fingerId))
            {
                TouchInfo touchInfo = activeTouches[touch.fingerId];
                touchInfo.UpdateTouch(touch);
                
                // 检测点击
                if (enableTapGestures && !touchInfo.isSwipe && touchInfo.duration < tapTimeThreshold)
                {
                    HandleTap(touch.position);
                }
                
                // 移除触摸信息
                activeTouches.Remove(touch.fingerId);
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"触摸结束: ID={touch.fingerId}, 位置={touch.position}");
            }
        }
        
        /// <summary>
        /// 处理触摸取消
        /// </summary>
        private void HandleTouchCanceled(Touch touch)
        {
            if (activeTouches.ContainsKey(touch.fingerId))
            {
                activeTouches.Remove(touch.fingerId);
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"触摸取消: ID={touch.fingerId}");
            }
        }
        
        /// <summary>
        /// 处理点击
        /// </summary>
        private void HandleTap(Vector2 position)
        {
            float currentTime = Time.time;
            
            // 检测双击
            if (currentTime - lastTapTime < doubleTapTimeThreshold && 
                Vector2.Distance(position, lastTapPosition) < swipeThreshold)
            {
                OnDoubleTap?.Invoke(position);
                
                if (showDebugInfo)
                {
                    Debug.Log($"双击检测: 位置={position}");
                }
            }
            else
            {
                OnTap?.Invoke(position);
                
                if (showDebugInfo)
                {
                    Debug.Log($"单击检测: 位置={position}");
                }
            }
            
            lastTapTime = currentTime;
            lastTapPosition = position;
        }
        
        /// <summary>
        /// 清理结束的触摸
        /// </summary>
        private void CleanupFinishedTouches()
        {
            List<int> touchesToRemove = new List<int>();
            
            foreach (var kvp in activeTouches)
            {
                TouchInfo touchInfo = kvp.Value;
                if (touchInfo.phase == TouchPhase.Ended || touchInfo.phase == TouchPhase.Canceled)
                {
                    touchesToRemove.Add(kvp.Key);
                }
            }
            
            foreach (int fingerId in touchesToRemove)
            {
                activeTouches.Remove(fingerId);
            }
        }
        
        /// <summary>
        /// 检测滑动手势
        /// </summary>
        private void DetectSwipeGestures()
        {
            foreach (var touchInfo in activeTouches.Values)
            {
                if (touchInfo.isSwipe && touchInfo.phase == TouchPhase.Ended)
                {
                    SwipeGesture swipe = new SwipeGesture
                    {
                        startPosition = touchInfo.startPosition,
                        endPosition = touchInfo.currentPosition,
                        direction = touchInfo.GetSwipeDirection(),
                        distance = touchInfo.GetSwipeDistance(),
                        duration = touchInfo.duration,
                        swipeDirection = GetSwipeDirection(touchInfo.GetSwipeDirection())
                    };
                    
                    detectedSwipes.Add(swipe);
                    OnSwipeDetected?.Invoke(swipe);
                    
                    // 根据滑动方向触发相应的输入
                    ProcessSwipeInput(swipe);
                    
                    if (showDebugInfo)
                    {
                        Debug.Log($"滑动检测: 方向={swipe.swipeDirection}, 距离={swipe.distance:F1}");
                    }
                }
            }
            
            // 清理检测到的滑动
            detectedSwipes.Clear();
        }
        
        /// <summary>
        /// 获取滑动方向
        /// </summary>
        private SwipeDirection GetSwipeDirection(Vector2 direction)
        {
            float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
            
            if (angle < 0) angle += 360;
            
            if (angle >= 337.5f || angle < 22.5f)
                return SwipeDirection.Right;
            else if (angle >= 22.5f && angle < 67.5f)
                return SwipeDirection.UpRight;
            else if (angle >= 67.5f && angle < 112.5f)
                return SwipeDirection.Up;
            else if (angle >= 112.5f && angle < 157.5f)
                return SwipeDirection.UpLeft;
            else if (angle >= 157.5f && angle < 202.5f)
                return SwipeDirection.Left;
            else if (angle >= 202.5f && angle < 247.5f)
                return SwipeDirection.DownLeft;
            else if (angle >= 247.5f && angle < 292.5f)
                return SwipeDirection.Down;
            else if (angle >= 292.5f && angle < 337.5f)
                return SwipeDirection.DownRight;
            
            return SwipeDirection.None;
        }
        
        /// <summary>
        /// 处理滑动输入
        /// </summary>
        private void ProcessSwipeInput(SwipeGesture swipe)
        {
            switch (swipe.swipeDirection)
            {
                case SwipeDirection.Up:
                    jumpInput = true;
                    OnJumpInput?.Invoke();
                    break;
                    
                case SwipeDirection.Left:
                case SwipeDirection.Right:
                    // 可以用于快速移动或攻击
                    attackInput = true;
                    OnAttackInput?.Invoke();
                    break;
                    
                case SwipeDirection.Down:
                    // 可以用于下蹲或特殊动作
                    break;
            }
        }
        
        /// <summary>
        /// 检测捏合手势
        /// </summary>
        private void DetectPinchGestures()
        {
            if (UnityInput.touchCount == 2)
            {
                Touch touch1 = UnityInput.GetTouch(0);
                Touch touch2 = UnityInput.GetTouch(1);
                
                Vector2 touch1PrevPos = touch1.position - touch1.deltaPosition;
                Vector2 touch2PrevPos = touch2.position - touch2.deltaPosition;
                
                float prevTouchDeltaMag = (touch1PrevPos - touch2PrevPos).magnitude;
                float touchDeltaMag = (touch1.position - touch2.position).magnitude;
                
                float deltaMagnitudeDiff = prevTouchDeltaMag - touchDeltaMag;
                float pinchAmount = deltaMagnitudeDiff * pinchSensitivity;
                
                if (Mathf.Abs(pinchAmount) > 0.1f)
                {
                    OnPinch?.Invoke(pinchAmount);
                    
                    if (showDebugInfo)
                    {
                        Debug.Log($"捏合检测: 量={pinchAmount:F2}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 处理虚拟控制
        /// </summary>
        private void ProcessVirtualControls()
        {
            // 这里应该从虚拟摇杆和按钮获取输入
            // 由于VirtualJoystick组件已经实现，这里可以获取其输入
            
            if (virtualJoystick != null)
            {
                var joystick = virtualJoystick.GetComponent<VirtualJoystick>();
                if (joystick != null)
                {
                    movementInput = joystick.InputVector;
                    OnMovementInput?.Invoke(movementInput);
                }
            }
        }
        
        #region IInputHandler Implementation
        
        public void EnableInput(bool enabled)
        {
            isInputEnabled = enabled;
            
            if (virtualJoystick != null)
            {
                virtualJoystick.SetActive(enabled);
            }
            
            if (virtualButtons != null)
            {
                virtualButtons.SetActive(enabled);
            }
        }
        
        public Vector2 GetMovementInput()
        {
            return movementInput;
        }
        
        public bool GetJumpInput()
        {
            return jumpInput;
        }
        
        public bool GetAttackInput()
        {
            return attackInput;
        }
        
        public bool GetPauseInput()
        {
            return pauseInput;
        }

        public bool GetActionInput()
        {
            return attackInput;
        }

        public bool HasActiveInput()
        {
            return isInputEnabled && (movementInput != Vector2.zero || jumpInput || attackInput || pauseInput);
        }

        public void ResetInputs()
        {
            movementInput = Vector2.zero;
            jumpInput = false;
            attackInput = false;
            pauseInput = false;

            // 清理触摸状态
            activeTouches.Clear();
            detectedSwipes.Clear();

            if (showDebugInfo)
            {
                Debug.Log("输入状态已重置");
            }
        }

        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置触摸敏感度
        /// </summary>
        public void SetTouchSensitivity(float sensitivity)
        {
            touchSensitivity = Mathf.Clamp01(sensitivity);
        }
        
        /// <summary>
        /// 设置滑动阈值
        /// </summary>
        public void SetSwipeThreshold(float threshold)
        {
            swipeThreshold = Mathf.Max(0f, threshold);
        }
        
        /// <summary>
        /// 获取活动触摸数量
        /// </summary>
        public int GetActiveTouchCount()
        {
            return activeTouches.Count;
        }
        
        /// <summary>
        /// 获取最近的滑动手势
        /// </summary>
        public SwipeGesture[] GetRecentSwipes()
        {
            return detectedSwipes.ToArray();
        }
        
        #endregion
        
        #region 调试和可视化
        
        private void OnGUI()
        {
            if (!showTouchVisualization) return;
            
            // 显示触摸点
            foreach (var touchInfo in activeTouches.Values)
            {
                Vector2 screenPos = UnityCamera.main.WorldToScreenPoint(touchInfo.currentPosition);
                screenPos.y = Screen.height - screenPos.y; // 翻转Y坐标

                GUI.Box(new Rect(screenPos.x - 25, screenPos.y - 25, 50, 50), touchInfo.fingerId.ToString());

                // 显示滑动轨迹
                if (touchInfo.isSwipe)
                {
                    Vector2 startScreenPos = UnityCamera.main.WorldToScreenPoint(touchInfo.startPosition);
                    startScreenPos.y = Screen.height - startScreenPos.y;
                    
                    // 这里可以绘制线条，但GUI没有直接的线条绘制方法
                    // 在实际项目中可以使用Gizmos或其他方法
                }
            }
        }
        
        #endregion
        
        #region 静态实例管理
        
        private static MobileInputManager instance;
        public static MobileInputManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<MobileInputManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("MobileInputManager");
                        instance = go.AddComponent<MobileInputManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
}
