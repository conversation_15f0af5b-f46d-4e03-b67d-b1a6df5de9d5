using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Mobile
{
    /// <summary>
    /// 移动设备中断处理器
    /// 处理通知、来电、应用最小化等中断事件，并管理游戏状态保存和恢复
    /// </summary>
    public class MobileInterruptHandler : MonoBehaviour
    {
        [Header("中断处理设置")]
        [SerializeField] private bool enableInterruptHandling = true;
        [SerializeField] private bool enableNotificationHandling = true;
        [SerializeField] private bool enableCallHandling = true;
        [SerializeField] private bool enableMinimizeHandling = true;
        
        [Header("状态保存设置")]
        [SerializeField] private bool enableAutoSave = true;
        [SerializeField] private float autoSaveInterval = 30f;
        [SerializeField] private int maxSaveSlots = 5;
        [SerializeField] private bool compressGameState = true;
        
        [Header("恢复设置")]
        [SerializeField] private bool enableAutoRestore = true;
        [SerializeField] private float restoreDelay = 1f;
        [SerializeField] private bool showRestorePrompt = true;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logInterruptEvents = true;
        
        // 中断状态
        private bool isInterrupted = false;
        private InterruptType currentInterruptType = InterruptType.None;
        private float interruptStartTime = 0f;
        private float lastAutoSaveTime = 0f;
        
        // 游戏状态
        private GameStateData savedGameState;
        private bool hasUnsavedChanges = false;
        
        // 组件引用
        private GameManager gameManager;
        private MobileDeviceManager mobileDeviceManager;
        
        /// <summary>
        /// 中断类型
        /// </summary>
        public enum InterruptType
        {
            None,
            Notification,
            PhoneCall,
            ApplicationMinimized,
            ApplicationPaused,
            LowMemory,
            Other
        }
        
        /// <summary>
        /// 游戏状态数据
        /// </summary>
        [System.Serializable]
        public class GameStateData
        {
            [Header("基本信息")]
            public string saveTime;
            public float gameTime;
            public int currentLevel;
            public bool isGameActive;
            
            [Header("玩家数据")]
            public Vector3 playerPosition;
            public int playerHealth;
            public int playerScore;
            public int playerLives;
            
            [Header("游戏进度")]
            public int currentScore;
            public int highScore;
            public int collectiblesCollected;
            public int enemiesDefeated;
            
            [Header("设置数据")]
            public float masterVolume;
            public float musicVolume;
            public float sfxVolume;
            public int qualityLevel;
            
            [Header("其他数据")]
            public Dictionary<string, object> customData;
            
            public GameStateData()
            {
                customData = new Dictionary<string, object>();
                saveTime = System.DateTime.Now.ToString();
            }
        }
        
        // 事件
        public System.Action<InterruptType> OnInterruptDetected;
        public System.Action<InterruptType> OnInterruptResolved;
        public System.Action<GameStateData> OnGameStateSaved;
        public System.Action<GameStateData> OnGameStateRestored;
        public System.Action OnAutoSaveTriggered;
        
        // 属性
        public bool IsInterrupted => isInterrupted;
        public InterruptType CurrentInterruptType => currentInterruptType;
        public bool HasUnsavedChanges => hasUnsavedChanges;
        public GameStateData SavedGameState => savedGameState;
        
        private void Awake()
        {
            InitializeInterruptHandler();
        }
        
        private void Start()
        {
            RegisterWithSystems();
            
            if (enableAutoSave)
            {
                InvokeRepeating(nameof(AutoSave), autoSaveInterval, autoSaveInterval);
            }
            
            // 检查是否有需要恢复的游戏状态
            if (enableAutoRestore)
            {
                StartCoroutine(CheckForGameStateRestore());
            }
        }
        
        private void Update()
        {
            if (enableInterruptHandling)
            {
                DetectInterrupts();
            }
        }
        
        /// <summary>
        /// 初始化中断处理器
        /// </summary>
        private void InitializeInterruptHandler()
        {
            if (!Application.isMobilePlatform)
            {
                if (showDebugInfo)
                {
                    Debug.Log("MobileInterruptHandler: 非移动平台，部分功能将被禁用");
                }
            }
        }
        
        /// <summary>
        /// 注册到系统
        /// </summary>
        private void RegisterWithSystems()
        {
            gameManager = GameManager.Instance;
            mobileDeviceManager = MobileDeviceManager.Instance;
            
            // 注册移动设备事件
            if (mobileDeviceManager != null)
            {
                mobileDeviceManager.OnApplicationPauseEvent += () => HandleInterrupt(InterruptType.ApplicationPaused);
                mobileDeviceManager.OnApplicationResumeEvent += () => ResolveInterrupt(InterruptType.ApplicationPaused);
                mobileDeviceManager.OnMemoryWarning += () => HandleInterrupt(InterruptType.LowMemory);
            }
        }
        
        /// <summary>
        /// 检测中断
        /// </summary>
        private void DetectInterrupts()
        {
            // 检测应用最小化
            if (enableMinimizeHandling && !Application.isFocused && !isInterrupted)
            {
                HandleInterrupt(InterruptType.ApplicationMinimized);
            }
            else if (Application.isFocused && isInterrupted && currentInterruptType == InterruptType.ApplicationMinimized)
            {
                ResolveInterrupt(InterruptType.ApplicationMinimized);
            }
            
            // 在实际项目中，这里可以添加更多的中断检测逻辑
            // 例如通过原生插件检测来电、通知等
        }
        
        /// <summary>
        /// 处理中断
        /// </summary>
        public void HandleInterrupt(InterruptType interruptType)
        {
            if (isInterrupted && currentInterruptType == interruptType)
            {
                return; // 避免重复处理相同的中断
            }
            
            isInterrupted = true;
            currentInterruptType = interruptType;
            interruptStartTime = Time.time;
            
            // 保存游戏状态
            if (enableAutoSave)
            {
                SaveGameState();
            }
            
            // 暂停游戏
            if (gameManager != null && gameManager.isGameStarted)
            {
                gameManager.PauseGame();
            }
            
            // 处理特定类型的中断
            HandleSpecificInterrupt(interruptType);
            
            OnInterruptDetected?.Invoke(interruptType);
            
            if (logInterruptEvents)
            {
                Debug.Log($"MobileInterruptHandler: 检测到中断 - {interruptType}");
            }
        }
        
        /// <summary>
        /// 处理特定类型的中断
        /// </summary>
        private void HandleSpecificInterrupt(InterruptType interruptType)
        {
            switch (interruptType)
            {
                case InterruptType.PhoneCall:
                    HandlePhoneCallInterrupt();
                    break;
                case InterruptType.Notification:
                    HandleNotificationInterrupt();
                    break;
                case InterruptType.ApplicationMinimized:
                    HandleMinimizeInterrupt();
                    break;
                case InterruptType.LowMemory:
                    HandleLowMemoryInterrupt();
                    break;
            }
        }
        
        /// <summary>
        /// 处理来电中断
        /// </summary>
        private void HandlePhoneCallInterrupt()
        {
            // 降低音量或静音
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMasterVolume(0.1f);
            }
            
            // 可以添加更多来电特定的处理逻辑
        }
        
        /// <summary>
        /// 处理通知中断
        /// </summary>
        private void HandleNotificationInterrupt()
        {
            // 通知中断通常比较短暂，可能不需要特殊处理
            // 但可以记录通知事件用于分析
        }
        
        /// <summary>
        /// 处理应用最小化中断
        /// </summary>
        private void HandleMinimizeInterrupt()
        {
            // 确保游戏状态已保存
            SaveGameState();
            
            // 可以清理一些临时资源
            if (ObjectPool.Instance != null)
            {
                // 清理对象池中的非必要对象
                System.GC.Collect();
            }
        }
        
        /// <summary>
        /// 处理低内存中断
        /// </summary>
        private void HandleLowMemoryInterrupt()
        {
            // 强制垃圾回收
            System.GC.Collect();
            Resources.UnloadUnusedAssets();
            
            // 降低质量设置
            if (PerformanceMonitor.Instance != null)
            {
                int currentQuality = QualitySettings.GetQualityLevel();
                if (currentQuality > 0)
                {
                    PerformanceMonitor.Instance.SetQualityLevel(currentQuality - 1);
                }
            }
        }
        
        /// <summary>
        /// 解决中断
        /// </summary>
        public void ResolveInterrupt(InterruptType interruptType)
        {
            if (!isInterrupted || currentInterruptType != interruptType)
            {
                return;
            }
            
            float interruptDuration = Time.time - interruptStartTime;
            
            isInterrupted = false;
            currentInterruptType = InterruptType.None;
            
            // 处理特定类型的中断恢复
            ResolveSpecificInterrupt(interruptType);
            
            OnInterruptResolved?.Invoke(interruptType);
            
            if (logInterruptEvents)
            {
                Debug.Log($"MobileInterruptHandler: 中断已解决 - {interruptType}, 持续时间: {interruptDuration:F1}秒");
            }
        }
        
        /// <summary>
        /// 解决特定类型的中断
        /// </summary>
        private void ResolveSpecificInterrupt(InterruptType interruptType)
        {
            switch (interruptType)
            {
                case InterruptType.PhoneCall:
                    ResolvePhoneCallInterrupt();
                    break;
                case InterruptType.ApplicationMinimized:
                    ResolveMinimizeInterrupt();
                    break;
            }
        }
        
        /// <summary>
        /// 解决来电中断
        /// </summary>
        private void ResolvePhoneCallInterrupt()
        {
            // 恢复音量
            if (AudioManager.Instance != null)
            {
                // 这里应该恢复到之前的音量设置
                AudioManager.Instance.SetMasterVolume(1f);
            }
        }
        
        /// <summary>
        /// 解决应用最小化中断
        /// </summary>
        private void ResolveMinimizeInterrupt()
        {
            // 检查是否需要恢复游戏状态
            if (enableAutoRestore && showRestorePrompt)
            {
                StartCoroutine(ShowRestorePrompt());
            }
        }
        
        /// <summary>
        /// 显示恢复提示
        /// </summary>
        private IEnumerator ShowRestorePrompt()
        {
            yield return new WaitForSeconds(restoreDelay);
            
            // 在实际项目中，这里应该显示一个UI提示
            // 询问用户是否要恢复游戏状态
            if (showDebugInfo)
            {
                Debug.Log("MobileInterruptHandler: 显示游戏状态恢复提示");
            }
            
            // 自动恢复（在实际项目中应该等待用户选择）
            if (savedGameState != null)
            {
                RestoreGameState(savedGameState);
            }
        }
        
        /// <summary>
        /// 保存游戏状态
        /// </summary>
        public void SaveGameState()
        {
            GameStateData gameState = new GameStateData();
            
            // 收集基本游戏信息
            gameState.gameTime = Time.time;
            gameState.isGameActive = gameManager != null && gameManager.isGameStarted;
            
            // 收集玩家数据
            CollectPlayerData(gameState);
            
            // 收集分数数据
            CollectScoreData(gameState);
            
            // 收集设置数据
            CollectSettingsData(gameState);
            
            // 保存到本地存储
            SaveGameStateToStorage(gameState);
            
            savedGameState = gameState;
            hasUnsavedChanges = false;
            lastAutoSaveTime = Time.time;
            
            OnGameStateSaved?.Invoke(gameState);
            
            if (showDebugInfo)
            {
                Debug.Log($"MobileInterruptHandler: 游戏状态已保存 - {gameState.saveTime}");
            }
        }
        
        /// <summary>
        /// 收集玩家数据
        /// </summary>
        private void CollectPlayerData(GameStateData gameState)
        {
            // 查找玩家对象
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                gameState.playerPosition = player.transform.position;
                
                // 获取玩家健康值
                var health = player.GetComponent<Health>();
                if (health != null)
                {
                    gameState.playerHealth = health.CurrentHealth;
                }
            }
        }
        
        /// <summary>
        /// 收集分数数据
        /// </summary>
        private void CollectScoreData(GameStateData gameState)
        {
            if (ScoreManager.Instance != null)
            {
                gameState.currentScore = ScoreManager.Instance.CurrentScore;
                gameState.highScore = ScoreManager.Instance.HighScore;
                gameState.collectiblesCollected = ScoreManager.Instance.GetTotalCollectedItems();
            }
        }
        
        /// <summary>
        /// 收集设置数据
        /// </summary>
        private void CollectSettingsData(GameStateData gameState)
        {
            if (AudioManager.Instance != null)
            {
                gameState.masterVolume = AudioManager.Instance.MasterVolume;
                gameState.musicVolume = AudioManager.Instance.MusicVolume;
                gameState.sfxVolume = AudioManager.Instance.SFXVolume;
            }
            
            gameState.qualityLevel = QualitySettings.GetQualityLevel();
        }
        
        /// <summary>
        /// 保存游戏状态到存储
        /// </summary>
        private void SaveGameStateToStorage(GameStateData gameState)
        {
            try
            {
                string json = JsonUtility.ToJson(gameState, true);
                
                if (compressGameState)
                {
                    // 在实际项目中可以添加压缩逻辑
                }
                
                string key = "GameState_" + System.DateTime.Now.Ticks;
                PlayerPrefs.SetString(key, json);
                PlayerPrefs.SetString("LastGameStateKey", key);
                PlayerPrefs.Save();
                
                // 管理保存槽数量
                ManageSaveSlots();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"MobileInterruptHandler: 保存游戏状态失败 - {e.Message}");
            }
        }
        
        /// <summary>
        /// 管理保存槽数量
        /// </summary>
        private void ManageSaveSlots()
        {
            // 获取所有游戏状态键
            List<string> gameStateKeys = new List<string>();
            
            // 这里应该实现获取所有保存的游戏状态键的逻辑
            // 由于PlayerPrefs没有直接的方法获取所有键，这里简化处理
            
            // 如果超过最大槽数，删除最旧的保存
            if (gameStateKeys.Count > maxSaveSlots)
            {
                for (int i = 0; i < gameStateKeys.Count - maxSaveSlots; i++)
                {
                    PlayerPrefs.DeleteKey(gameStateKeys[i]);
                }
            }
        }
        
        /// <summary>
        /// 恢复游戏状态
        /// </summary>
        public void RestoreGameState(GameStateData gameState)
        {
            if (gameState == null)
            {
                Debug.LogWarning("MobileInterruptHandler: 无法恢复空的游戏状态");
                return;
            }
            
            // 恢复玩家数据
            RestorePlayerData(gameState);
            
            // 恢复分数数据
            RestoreScoreData(gameState);
            
            // 恢复设置数据
            RestoreSettingsData(gameState);
            
            OnGameStateRestored?.Invoke(gameState);
            
            if (showDebugInfo)
            {
                Debug.Log($"MobileInterruptHandler: 游戏状态已恢复 - {gameState.saveTime}");
            }
        }
        
        /// <summary>
        /// 恢复玩家数据
        /// </summary>
        private void RestorePlayerData(GameStateData gameState)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                player.transform.position = gameState.playerPosition;
                
                var health = player.GetComponent<Health>();
                if (health != null)
                {
                    // 计算需要恢复的生命值
                    int currentHealth = health.CurrentHealth;
                    int targetHealth = gameState.playerHealth;

                    if (targetHealth > currentHealth)
                    {
                        // 需要恢复生命值
                        int healthToRestore = targetHealth - currentHealth;
                        health.RestoreHealth(healthToRestore);
                    }
                    else if (targetHealth < currentHealth)
                    {
                        // 需要减少生命值
                        int healthToLose = currentHealth - targetHealth;
                        health.TakeDamage(healthToLose);
                    }
                    // 如果相等则不需要做任何操作
                }
            }
        }
        
        /// <summary>
        /// 恢复分数数据
        /// </summary>
        private void RestoreScoreData(GameStateData gameState)
        {
            if (ScoreManager.Instance != null)
            {
                // 先重置当前分数，然后添加目标分数
                ScoreManager.Instance.ResetCurrentScore();
                if (gameState.currentScore > 0)
                {
                    ScoreManager.Instance.AddScore(gameState.currentScore);
                }
                // 注意：通常不恢复最高分，因为它应该是持久的
            }
        }
        
        /// <summary>
        /// 恢复设置数据
        /// </summary>
        private void RestoreSettingsData(GameStateData gameState)
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMasterVolume(gameState.masterVolume);
                AudioManager.Instance.SetMusicVolume(gameState.musicVolume);
                AudioManager.Instance.SetSFXVolume(gameState.sfxVolume);
            }
            
            QualitySettings.SetQualityLevel(gameState.qualityLevel, true);
        }
        
        /// <summary>
        /// 检查游戏状态恢复
        /// </summary>
        private IEnumerator CheckForGameStateRestore()
        {
            yield return new WaitForSeconds(1f); // 等待系统初始化
            
            string lastStateKey = PlayerPrefs.GetString("LastGameStateKey", "");
            if (!string.IsNullOrEmpty(lastStateKey) && PlayerPrefs.HasKey(lastStateKey))
            {
                try
                {
                    string json = PlayerPrefs.GetString(lastStateKey);
                    GameStateData gameState = JsonUtility.FromJson<GameStateData>(json);
                    
                    if (gameState != null)
                    {
                        savedGameState = gameState;
                        
                        if (showRestorePrompt)
                        {
                            // 在实际项目中，这里应该显示恢复提示UI
                            if (showDebugInfo)
                            {
                                Debug.Log("MobileInterruptHandler: 发现可恢复的游戏状态");
                            }
                        }
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"MobileInterruptHandler: 恢复游戏状态失败 - {e.Message}");
                }
            }
        }
        
        /// <summary>
        /// 自动保存
        /// </summary>
        private void AutoSave()
        {
            if (enableAutoSave && hasUnsavedChanges)
            {
                SaveGameState();
                OnAutoSaveTriggered?.Invoke();
            }
        }
        
        /// <summary>
        /// 标记有未保存的更改
        /// </summary>
        public void MarkUnsavedChanges()
        {
            hasUnsavedChanges = true;
        }
        
        /// <summary>
        /// 清除保存的游戏状态
        /// </summary>
        public void ClearSavedGameState()
        {
            string lastStateKey = PlayerPrefs.GetString("LastGameStateKey", "");
            if (!string.IsNullOrEmpty(lastStateKey))
            {
                PlayerPrefs.DeleteKey(lastStateKey);
                PlayerPrefs.DeleteKey("LastGameStateKey");
                PlayerPrefs.Save();
            }
            
            savedGameState = null;
            hasUnsavedChanges = false;
            
            if (showDebugInfo)
            {
                Debug.Log("MobileInterruptHandler: 已清除保存的游戏状态");
            }
        }
        
        #region 静态实例管理
        
        private static MobileInterruptHandler instance;
        public static MobileInterruptHandler Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<MobileInterruptHandler>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("MobileInterruptHandler");
                        instance = go.AddComponent<MobileInterruptHandler>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
}
