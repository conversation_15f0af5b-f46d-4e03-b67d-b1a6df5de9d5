using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using MobileScrollingGame.Core;
using UnityInput = UnityEngine.Input;

namespace MobileScrollingGame.Mobile
{
    /// <summary>
    /// 性能监控系统
    /// 监控帧率、内存使用、CPU使用等性能指标，并提供动态质量调整
    /// </summary>
    public class PerformanceMonitor : MonoBehaviour
    {
        [Header("监控设置")]
        [SerializeField] private bool enableMonitoring = true;
        [SerializeField] private float updateInterval = 1f;
        [SerializeField] private int sampleCount = 60;
        
        [Header("性能阈值")]
        [SerializeField] private float lowFPSThreshold = 30f;
        [SerializeField] private float highFPSThreshold = 55f;
        [SerializeField] private float memoryWarningThreshold = 0.8f; // 80%的系统内存
        [SerializeField] private float cpuWarningThreshold = 0.8f; // 80%的CPU使用率
        
        [Header("动态质量调整")]
        [SerializeField] private bool enableDynamicQuality = true;
        [SerializeField] private float qualityAdjustmentDelay = 5f;
        [SerializeField] private int minQualityLevel = 0;
        [SerializeField] private int maxQualityLevel = 5;
        
        [Header("UI显示")]
        [SerializeField] private bool showPerformanceUI = false;
        [SerializeField] private KeyCode toggleUIKey = KeyCode.F1;
        
        [Header("调试设置")]
        [SerializeField] private bool logPerformanceData = false;
        [SerializeField] private float logInterval = 10f;
        
        // 性能数据
        private Queue<float> fpsHistory = new Queue<float>();
        private Queue<float> memoryHistory = new Queue<float>();
        private Queue<float> cpuHistory = new Queue<float>();
        
        // 当前性能指标
        private float currentFPS = 0f;
        private float averageFPS = 0f;
        private float currentMemoryUsage = 0f;
        private float currentCPUUsage = 0f;
        
        // 质量调整
        private int currentQualityLevel;
        private float lastQualityAdjustment = 0f;
        private bool isAdjustingQuality = false;
        
        // 统计信息
        private int frameCount = 0;
        private float timeAccumulator = 0f;
        private float lastUpdateTime = 0f;
        private float lastLogTime = 0f;
        
        // 事件
        public System.Action<PerformanceData> OnPerformanceUpdate;
        public System.Action<int> OnQualityLevelChanged;
        public System.Action OnLowPerformanceDetected;
        public System.Action OnHighPerformanceDetected;
        
        /// <summary>
        /// 性能数据结构
        /// </summary>
        public struct PerformanceData
        {
            public float fps;
            public float averageFPS;
            public float memoryUsage;
            public float cpuUsage;
            public int qualityLevel;
            public PerformanceLevel performanceLevel;
        }
        
        /// <summary>
        /// 性能等级
        /// </summary>
        public enum PerformanceLevel
        {
            Low,
            Medium,
            High
        }
        
        // 属性
        public float CurrentFPS => currentFPS;
        public float AverageFPS => averageFPS;
        public float CurrentMemoryUsage => currentMemoryUsage;
        public float CurrentCPUUsage => currentCPUUsage;
        public int CurrentQualityLevel => currentQualityLevel;
        public PerformanceLevel CurrentPerformanceLevel => GetPerformanceLevel();
        
        private void Awake()
        {
            InitializePerformanceMonitor();
        }
        
        private void Start()
        {
            currentQualityLevel = QualitySettings.GetQualityLevel();
            
            if (enableMonitoring)
            {
                StartCoroutine(MonitorPerformance());
            }
            
            if (logPerformanceData)
            {
                InvokeRepeating(nameof(LogPerformanceData), logInterval, logInterval);
            }
        }
        
        private void Update()
        {
            if (enableMonitoring)
            {
                UpdateFrameRate();
            }
            
            // 切换UI显示
            if (UnityInput.GetKeyDown(toggleUIKey))
            {
                showPerformanceUI = !showPerformanceUI;
            }
        }
        
        /// <summary>
        /// 初始化性能监控器
        /// </summary>
        private void InitializePerformanceMonitor()
        {
            // 初始化历史数据队列
            fpsHistory.Clear();
            memoryHistory.Clear();
            cpuHistory.Clear();
            
            lastUpdateTime = Time.time;
            lastLogTime = Time.time;
        }
        
        /// <summary>
        /// 更新帧率
        /// </summary>
        private void UpdateFrameRate()
        {
            frameCount++;
            timeAccumulator += Time.unscaledDeltaTime;
            
            if (timeAccumulator >= updateInterval)
            {
                currentFPS = frameCount / timeAccumulator;
                frameCount = 0;
                timeAccumulator = 0f;
                
                // 添加到历史记录
                AddToHistory(fpsHistory, currentFPS);
                
                // 计算平均帧率
                CalculateAverageFPS();
            }
        }
        
        /// <summary>
        /// 监控性能协程
        /// </summary>
        private IEnumerator MonitorPerformance()
        {
            while (enableMonitoring)
            {
                yield return new WaitForSeconds(updateInterval);
                
                // 更新内存使用
                UpdateMemoryUsage();
                
                // 更新CPU使用（估算）
                UpdateCPUUsage();
                
                // 检查性能并调整质量
                if (enableDynamicQuality)
                {
                    CheckAndAdjustQuality();
                }
                
                // 触发性能更新事件
                OnPerformanceUpdate?.Invoke(GetCurrentPerformanceData());
                
                lastUpdateTime = Time.time;
            }
        }
        
        /// <summary>
        /// 更新内存使用
        /// </summary>
        private void UpdateMemoryUsage()
        {
            long totalMemory = SystemInfo.systemMemorySize * 1024L * 1024L; // 转换为字节
            long usedMemory = System.GC.GetTotalMemory(false);
            
            currentMemoryUsage = (float)usedMemory / totalMemory;
            
            // 添加到历史记录
            AddToHistory(memoryHistory, currentMemoryUsage);
        }
        
        /// <summary>
        /// 更新CPU使用（估算）
        /// </summary>
        private void UpdateCPUUsage()
        {
            // 基于帧时间估算CPU使用率
            float frameTime = Time.unscaledDeltaTime;
            float targetFrameTime = 1f / Application.targetFrameRate;
            
            currentCPUUsage = Mathf.Clamp01(frameTime / targetFrameTime);
            
            // 添加到历史记录
            AddToHistory(cpuHistory, currentCPUUsage);
        }
        
        /// <summary>
        /// 添加数据到历史记录
        /// </summary>
        private void AddToHistory(Queue<float> history, float value)
        {
            history.Enqueue(value);
            
            if (history.Count > sampleCount)
            {
                history.Dequeue();
            }
        }
        
        /// <summary>
        /// 计算平均帧率
        /// </summary>
        private void CalculateAverageFPS()
        {
            if (fpsHistory.Count == 0)
            {
                averageFPS = currentFPS;
                return;
            }
            
            float sum = 0f;
            foreach (float fps in fpsHistory)
            {
                sum += fps;
            }
            
            averageFPS = sum / fpsHistory.Count;
        }
        
        /// <summary>
        /// 检查并调整质量
        /// </summary>
        private void CheckAndAdjustQuality()
        {
            if (isAdjustingQuality || Time.time - lastQualityAdjustment < qualityAdjustmentDelay)
            {
                return;
            }
            
            bool shouldAdjust = false;
            int targetQualityLevel = currentQualityLevel;
            
            // 检查是否需要降低质量
            if (averageFPS < lowFPSThreshold || currentMemoryUsage > memoryWarningThreshold)
            {
                if (currentQualityLevel > minQualityLevel)
                {
                    targetQualityLevel = currentQualityLevel - 1;
                    shouldAdjust = true;
                    
                    OnLowPerformanceDetected?.Invoke();
                }
            }
            // 检查是否可以提高质量
            else if (averageFPS > highFPSThreshold && currentMemoryUsage < memoryWarningThreshold * 0.6f)
            {
                if (currentQualityLevel < maxQualityLevel)
                {
                    targetQualityLevel = currentQualityLevel + 1;
                    shouldAdjust = true;
                    
                    OnHighPerformanceDetected?.Invoke();
                }
            }
            
            if (shouldAdjust)
            {
                StartCoroutine(AdjustQualityLevel(targetQualityLevel));
            }
        }
        
        /// <summary>
        /// 调整质量等级
        /// </summary>
        private IEnumerator AdjustQualityLevel(int targetLevel)
        {
            isAdjustingQuality = true;
            
            // 逐步调整质量等级以避免突然的性能变化
            while (currentQualityLevel != targetLevel)
            {
                if (currentQualityLevel < targetLevel)
                {
                    currentQualityLevel++;
                }
                else
                {
                    currentQualityLevel--;
                }
                
                QualitySettings.SetQualityLevel(currentQualityLevel, true);
                OnQualityLevelChanged?.Invoke(currentQualityLevel);
                
                if (logPerformanceData)
                {
                    Debug.Log($"PerformanceMonitor: 质量等级调整为 {currentQualityLevel}");
                }
                
                // 等待一段时间观察效果
                yield return new WaitForSeconds(1f);
            }
            
            lastQualityAdjustment = Time.time;
            isAdjustingQuality = false;
        }
        
        /// <summary>
        /// 获取当前性能数据
        /// </summary>
        public PerformanceData GetCurrentPerformanceData()
        {
            return new PerformanceData
            {
                fps = currentFPS,
                averageFPS = averageFPS,
                memoryUsage = currentMemoryUsage,
                cpuUsage = currentCPUUsage,
                qualityLevel = currentQualityLevel,
                performanceLevel = GetPerformanceLevel()
            };
        }
        
        /// <summary>
        /// 获取性能等级
        /// </summary>
        private PerformanceLevel GetPerformanceLevel()
        {
            if (averageFPS < lowFPSThreshold || currentMemoryUsage > memoryWarningThreshold)
            {
                return PerformanceLevel.Low;
            }
            else if (averageFPS > highFPSThreshold && currentMemoryUsage < memoryWarningThreshold * 0.6f)
            {
                return PerformanceLevel.High;
            }
            else
            {
                return PerformanceLevel.Medium;
            }
        }
        
        /// <summary>
        /// 记录性能数据
        /// </summary>
        private void LogPerformanceData()
        {
            if (!logPerformanceData) return;
            
            PerformanceData data = GetCurrentPerformanceData();
            
            Debug.Log($"性能监控数据:");
            Debug.Log($"- FPS: {data.fps:F1} (平均: {data.averageFPS:F1})");
            Debug.Log($"- 内存使用: {data.memoryUsage:P1}");
            Debug.Log($"- CPU使用: {data.cpuUsage:P1}");
            Debug.Log($"- 质量等级: {data.qualityLevel}");
            Debug.Log($"- 性能等级: {data.performanceLevel}");
        }
        
        /// <summary>
        /// 强制调整质量等级
        /// </summary>
        public void SetQualityLevel(int level)
        {
            level = Mathf.Clamp(level, minQualityLevel, maxQualityLevel);
            
            if (level != currentQualityLevel)
            {
                currentQualityLevel = level;
                QualitySettings.SetQualityLevel(level, true);
                OnQualityLevelChanged?.Invoke(level);
                
                lastQualityAdjustment = Time.time;
                
                if (logPerformanceData)
                {
                    Debug.Log($"PerformanceMonitor: 手动设置质量等级为 {level}");
                }
            }
        }
        
        /// <summary>
        /// 重置性能历史数据
        /// </summary>
        public void ResetPerformanceHistory()
        {
            fpsHistory.Clear();
            memoryHistory.Clear();
            cpuHistory.Clear();
            
            frameCount = 0;
            timeAccumulator = 0f;
            
            if (logPerformanceData)
            {
                Debug.Log("PerformanceMonitor: 性能历史数据已重置");
            }
        }
        
        /// <summary>
        /// 获取性能历史数据
        /// </summary>
        public float[] GetFPSHistory()
        {
            return fpsHistory.ToArray();
        }
        
        public float[] GetMemoryHistory()
        {
            return memoryHistory.ToArray();
        }
        
        public float[] GetCPUHistory()
        {
            return cpuHistory.ToArray();
        }
        
        #region UI显示
        
        private void OnGUI()
        {
            if (!showPerformanceUI) return;
            
            GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 200));
            
            GUILayout.Label("性能监控", GUI.skin.box);
            
            PerformanceData data = GetCurrentPerformanceData();
            
            GUILayout.Label($"FPS: {data.fps:F1}");
            GUILayout.Label($"平均FPS: {data.averageFPS:F1}");
            GUILayout.Label($"内存: {data.memoryUsage:P1}");
            GUILayout.Label($"CPU: {data.cpuUsage:P1}");
            GUILayout.Label($"质量等级: {data.qualityLevel}");
            GUILayout.Label($"性能等级: {data.performanceLevel}");
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("重置历史"))
            {
                ResetPerformanceHistory();
            }
            
            if (GUILayout.Button("强制GC"))
            {
                System.GC.Collect();
                Resources.UnloadUnusedAssets();
            }
            
            GUILayout.EndArea();
        }
        
        #endregion
        
        #region 静态实例管理
        
        private static PerformanceMonitor instance;
        public static PerformanceMonitor Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<PerformanceMonitor>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("PerformanceMonitor");
                        instance = go.AddComponent<PerformanceMonitor>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
}
