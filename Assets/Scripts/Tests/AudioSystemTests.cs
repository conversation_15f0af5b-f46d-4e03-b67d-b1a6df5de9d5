using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 音频系统测试
    /// 测试音频管理器、音频事件系统和音频设置的功能
    /// </summary>
    public class AudioSystemTests
    {
        private GameObject testGameObject;
        private AudioManager audioManager;
        private AudioEventSystem audioEventSystem;
        private MobileScrollingGame.Core.AudioSettings audioSettings;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试游戏对象
            testGameObject = new GameObject("AudioSystemTest");

            // 添加音频管理器组件
            audioManager = testGameObject.AddComponent<AudioManager>();

            // 手动触发初始化（因为在测试环境中Awake可能不会被自动调用）
            // 使用反射调用私有方法，或者通过公共方法触发初始化
            // 这里我们通过调用一个会触发初始化的公共方法来确保初始化
            audioManager.SetMasterVolume(1.0f); // 这会触发InitializeAudioSystem

            // 添加音频事件系统组件
            audioEventSystem = testGameObject.AddComponent<AudioEventSystem>();

            // 创建音频设置
            audioSettings = new MobileScrollingGame.Core.AudioSettings();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
        }
        
        #region AudioManager Tests
        
        [Test]
        public void AudioManager_InitializesCorrectly()
        {
            // 验证音频管理器初始化
            Assert.IsNotNull(audioManager);
            Assert.AreEqual(1f, audioManager.MasterVolume);
            Assert.AreEqual(1f, audioManager.MusicVolume);
            Assert.AreEqual(1f, audioManager.SFXVolume);
        }
        
        [Test]
        public void AudioManager_SetMasterVolume_UpdatesCorrectly()
        {
            // 测试主音量设置
            float testVolume = 0.5f;
            audioManager.SetMasterVolume(testVolume);
            
            Assert.AreEqual(testVolume, audioManager.MasterVolume);
        }
        
        [Test]
        public void AudioManager_SetMusicVolume_UpdatesCorrectly()
        {
            // 测试音乐音量设置
            float testVolume = 0.7f;
            audioManager.SetMusicVolume(testVolume);
            
            Assert.AreEqual(testVolume, audioManager.MusicVolume);
        }
        
        [Test]
        public void AudioManager_SetSFXVolume_UpdatesCorrectly()
        {
            // 测试音效音量设置
            float testVolume = 0.8f;
            audioManager.SetSFXVolume(testVolume);
            
            Assert.AreEqual(testVolume, audioManager.SFXVolume);
        }
        
        [Test]
        public void AudioManager_VolumeClampedToValidRange()
        {
            // 测试音量范围限制
            audioManager.SetMasterVolume(-0.5f);
            Assert.AreEqual(0f, audioManager.MasterVolume);
            
            audioManager.SetMasterVolume(1.5f);
            Assert.AreEqual(1f, audioManager.MasterVolume);
        }
        
        [UnityTest]
        public IEnumerator AudioManager_PlaySoundEffect_DoesNotThrowException()
        {
            // 测试播放音效不会抛出异常
            Assert.DoesNotThrow(() => audioManager.PlaySoundEffect("TestSound"));
            yield return null;
        }
        
        [UnityTest]
        public IEnumerator AudioManager_PlayBackgroundMusic_DoesNotThrowException()
        {
            // 测试播放背景音乐不会抛出异常
            Assert.DoesNotThrow(() => audioManager.PlayBackgroundMusic("TestMusic"));
            yield return null;
        }
        
        [Test]
        public void AudioManager_StopBackgroundMusic_DoesNotThrowException()
        {
            // 测试停止背景音乐不会抛出异常
            Assert.DoesNotThrow(() => audioManager.StopBackgroundMusic());
        }
        
        #endregion
        
        #region AudioEventSystem Tests
        
        [Test]
        public void AudioEventSystem_InitializesCorrectly()
        {
            // 验证音频事件系统初始化
            Assert.IsNotNull(audioEventSystem);
        }
        
        [Test]
        public void AudioEventSystem_PlayAudioEvent_DoesNotThrowException()
        {
            // 测试播放音频事件不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.PlayAudioEvent("TestEvent"));
        }
        
        [Test]
        public void AudioEventSystem_PlayUISound_DoesNotThrowException()
        {
            // 测试播放UI音效不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.PlayUISound("TestUISound"));
        }
        
        [Test]
        public void AudioEventSystem_PlayCharacterSound_DoesNotThrowException()
        {
            // 测试播放角色音效不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.PlayCharacterSound("TestCharacterSound"));
        }
        
        [Test]
        public void AudioEventSystem_PlayEnemySound_DoesNotThrowException()
        {
            // 测试播放敌人音效不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.PlayEnemySound("TestEnemySound"));
        }
        
        [Test]
        public void AudioEventSystem_PlayEnvironmentSound_DoesNotThrowException()
        {
            // 测试播放环境音效不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.PlayEnvironmentSound("TestEnvironmentSound"));
        }
        
        [Test]
        public void AudioEventSystem_PlayCollectibleSound_DoesNotThrowException()
        {
            // 测试播放收集品音效不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.PlayCollectibleSound("TestCollectibleSound"));
        }
        
        [UnityTest]
        public IEnumerator AudioEventSystem_PlayDelayedAudioEvent_ProcessesCorrectly()
        {
            // 测试延迟音频事件处理
            float delay = 0.1f;
            audioEventSystem.PlayDelayedAudioEvent("TestDelayedSound", delay);
            
            // 等待延迟时间
            yield return new WaitForSeconds(delay + 0.05f);
            
            // 验证没有抛出异常
            Assert.IsTrue(true);
        }
        
        [Test]
        public void AudioEventSystem_StopAllSoundEffects_DoesNotThrowException()
        {
            // 测试停止所有音效不会抛出异常
            Assert.DoesNotThrow(() => audioEventSystem.StopAllSoundEffects());
        }
        
        #endregion
        
        #region AudioSettings Tests
        
        [Test]
        public void AudioSettings_InitializesWithDefaults()
        {
            // 验证音频设置默认值
            Assert.AreEqual(1f, audioSettings.masterVolume);
            Assert.AreEqual(0.8f, audioSettings.musicVolume);
            Assert.AreEqual(1f, audioSettings.sfxVolume);
            Assert.AreEqual(1f, audioSettings.uiVolume);
        }
        
        [Test]
        public void AudioSettings_SaveAndLoad_PreservesValues()
        {
            // 设置测试值
            audioSettings.masterVolume = 0.5f;
            audioSettings.musicVolume = 0.6f;
            audioSettings.sfxVolume = 0.7f;
            audioSettings.uiVolume = 0.8f;
            
            // 保存设置
            audioSettings.SaveSettings();
            
            // 创建新的设置对象并加载
            MobileScrollingGame.Core.AudioSettings loadedSettings = new MobileScrollingGame.Core.AudioSettings();
            loadedSettings.LoadSettings();
            
            // 验证值是否正确加载
            Assert.AreEqual(0.5f, loadedSettings.masterVolume);
            Assert.AreEqual(0.6f, loadedSettings.musicVolume);
            Assert.AreEqual(0.7f, loadedSettings.sfxVolume);
            Assert.AreEqual(0.8f, loadedSettings.uiVolume);
        }
        
        [Test]
        public void AudioSettings_ValidateSettings_ClampsInvalidValues()
        {
            // 设置无效值
            audioSettings.masterVolume = -0.5f;
            audioSettings.musicVolume = 1.5f;
            
            // 验证设置
            bool isValid = audioSettings.ValidateSettings();
            
            // 验证值被限制在有效范围内
            Assert.IsFalse(isValid);
            Assert.AreEqual(0f, audioSettings.masterVolume);
            Assert.AreEqual(1f, audioSettings.musicVolume);
        }
        
        [Test]
        public void AudioSettings_ResetToDefaults_RestoresDefaultValues()
        {
            // 修改设置
            audioSettings.masterVolume = 0.5f;
            audioSettings.musicVolume = 0.3f;
            
            // 重置为默认值
            audioSettings.ResetToDefaults();
            
            // 验证默认值
            Assert.AreEqual(1f, audioSettings.masterVolume);
            Assert.AreEqual(0.8f, audioSettings.musicVolume);
        }
        
        [Test]
        public void AudioSettings_Clone_CreatesIdenticalCopy()
        {
            // 设置测试值
            audioSettings.masterVolume = 0.5f;
            audioSettings.musicVolume = 0.6f;
            audioSettings.enableAudio3D = false;
            
            // 克隆设置
            MobileScrollingGame.Core.AudioSettings clonedSettings = audioSettings.Clone();
            
            // 验证克隆的设置
            Assert.AreEqual(audioSettings.masterVolume, clonedSettings.masterVolume);
            Assert.AreEqual(audioSettings.musicVolume, clonedSettings.musicVolume);
            Assert.AreEqual(audioSettings.enableAudio3D, clonedSettings.enableAudio3D);
        }
        
        [Test]
        public void AudioSettings_Equals_ComparesCorrectly()
        {
            // 创建相同的设置
            MobileScrollingGame.Core.AudioSettings settings1 = new MobileScrollingGame.Core.AudioSettings();
            MobileScrollingGame.Core.AudioSettings settings2 = new MobileScrollingGame.Core.AudioSettings();
            
            // 验证相等
            Assert.IsTrue(settings1.Equals(settings2));
            
            // 修改一个设置
            settings2.masterVolume = 0.5f;
            
            // 验证不相等
            Assert.IsFalse(settings1.Equals(settings2));
        }
        
        [Test]
        public void AudioSettings_GetSettingsSummary_ReturnsValidString()
        {
            // 获取设置摘要
            string summary = audioSettings.GetSettingsSummary();
            
            // 验证摘要不为空且包含关键信息
            Assert.IsNotNull(summary);
            Assert.IsTrue(summary.Contains("音频设置摘要"));
            Assert.IsTrue(summary.Contains("主音量"));
            Assert.IsTrue(summary.Contains("音乐音量"));
        }
        
        #endregion
        
        #region AudioClipLibrary Tests
        
        [Test]
        public void AudioClipLibrary_CreateInstance_DoesNotThrowException()
        {
            // 测试创建音频库实例
            Assert.DoesNotThrow(() => ScriptableObject.CreateInstance<AudioClipLibrary>());
        }
        
        [Test]
        public void AudioClipLibrary_GetSoundEffect_ReturnsNullForNonExistentSound()
        {
            // 创建音频库
            AudioClipLibrary library = ScriptableObject.CreateInstance<AudioClipLibrary>();
            
            // 尝试获取不存在的音效
            AudioClip clip = library.GetSoundEffect("NonExistentSound");
            
            // 验证返回null
            Assert.IsNull(clip);
        }
        
        [Test]
        public void AudioClipLibrary_GetBackgroundMusic_ReturnsNullForNonExistentMusic()
        {
            // 创建音频库
            AudioClipLibrary library = ScriptableObject.CreateInstance<AudioClipLibrary>();
            
            // 尝试获取不存在的背景音乐
            AudioClip clip = library.GetBackgroundMusic("NonExistentMusic");
            
            // 验证返回null
            Assert.IsNull(clip);
        }
        
        [Test]
        public void AudioClipLibrary_HasAudio_ReturnsFalseForNonExistentAudio()
        {
            // 创建音频库
            AudioClipLibrary library = ScriptableObject.CreateInstance<AudioClipLibrary>();
            
            // 检查不存在的音频
            bool hasAudio = library.HasAudio("NonExistentAudio");
            
            // 验证返回false
            Assert.IsFalse(hasAudio);
        }
        
        [Test]
        public void AudioClipLibrary_ValidateLibrary_ReturnsTrue()
        {
            // 创建音频库
            AudioClipLibrary library = ScriptableObject.CreateInstance<AudioClipLibrary>();
            
            // 验证空库
            bool isValid = library.ValidateLibrary();
            
            // 验证返回true（空库是有效的）
            Assert.IsTrue(isValid);
        }
        
        #endregion
        
        #region Integration Tests
        
        [UnityTest]
        public IEnumerator AudioSystem_Integration_WorksCorrectly()
        {
            // 集成测试：音频管理器和事件系统协同工作
            
            // 设置音量
            audioManager.SetMasterVolume(0.8f);
            audioManager.SetMusicVolume(0.6f);
            audioManager.SetSFXVolume(0.7f);
            
            // 播放音频事件
            audioEventSystem.PlayAudioEvent("TestEvent");
            audioEventSystem.PlayUISound("TestUISound");
            
            // 等待一帧
            yield return null;
            
            // 验证音量设置
            Assert.AreEqual(0.8f, audioManager.MasterVolume);
            Assert.AreEqual(0.6f, audioManager.MusicVolume);
            Assert.AreEqual(0.7f, audioManager.SFXVolume);
        }
        
        [Test]
        public void AudioSystem_SettingsIntegration_AppliesCorrectly()
        {
            // 集成测试：音频设置和音频管理器
            
            // 设置音频设置
            audioSettings.masterVolume = 0.5f;
            audioSettings.musicVolume = 0.4f;
            audioSettings.sfxVolume = 0.6f;
            
            // 应用设置（这里只测试不抛出异常）
            Assert.DoesNotThrow(() => audioSettings.ApplySettings());
        }
        
        #endregion
        
        #region Performance Tests
        
        [UnityTest]
        public IEnumerator AudioSystem_Performance_HandlesMultipleSounds()
        {
            // 性能测试：同时播放多个音效
            
            float startTime = Time.realtimeSinceStartup;
            
            // 播放多个音效
            for (int i = 0; i < 10; i++)
            {
                audioEventSystem.PlayUISound($"TestSound{i}");
            }
            
            yield return null;
            
            float endTime = Time.realtimeSinceStartup;
            float duration = endTime - startTime;
            
            // 验证性能（应该在合理时间内完成）
            Assert.Less(duration, 0.1f, "音频系统处理多个音效的时间过长");
        }
        
        #endregion
    }
}
