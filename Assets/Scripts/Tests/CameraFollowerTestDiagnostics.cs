using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraFollower测试诊断工具
    /// 用于调试和分析测试失败的原因
    /// </summary>
    public class CameraFollowerTestDiagnostics
    {
        private GameObject cameraObject;
        private GameObject targetObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        [SetUp]
        public void SetUp()
        {
            // 创建摄像机对象
            cameraObject = new GameObject("DiagnosticCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            // 在测试环境中设置固定的aspect比例，避免异常值
            cameraComponent.aspect = 16f / 9f; // 标准16:9比例
            cameraFollower = cameraObject.AddComponent<CameraFollower>();

            // 创建目标对象
            targetObject = new GameObject("DiagnosticTarget");
            targetObject.transform.position = Vector3.zero;

            Debug.Log("=== CameraFollower诊断测试开始 ===");
        }
        
        [TearDown]
        public void TearDown()
        {
            if (cameraObject != null)
                Object.DestroyImmediate(cameraObject);
            if (targetObject != null)
                Object.DestroyImmediate(targetObject);
                
            Debug.Log("=== CameraFollower诊断测试结束 ===");
        }
        
        [Test]
        public void DiagnoseSetFollowTarget()
        {
            Debug.Log("--- 诊断SetFollowTarget ---");

            // 首先禁用边界限制，确保测试不受边界影响
            cameraFollower.EnableBounds(false);

            // 记录初始状态
            Vector3 initialCameraPos = cameraObject.transform.position;
            Vector3 initialTargetPos = targetObject.transform.position;

            Debug.Log($"初始摄像机位置: {initialCameraPos}");
            Debug.Log($"初始目标位置: {initialTargetPos}");
            Debug.Log($"边界限制已禁用");

            // 设置目标位置到(0, 0, 0)
            targetObject.transform.position = Vector3.zero;

            // 执行设置目标
            cameraFollower.SetFollowTarget(targetObject.transform);

            // 记录结果状态
            Vector3 finalCameraPos = cameraObject.transform.position;
            // 期望位置应该是目标位置 + CameraFollower的默认偏移量(0, 1, -10)
            Vector3 expectedPosition = targetObject.transform.position + new Vector3(0, 1, -10);

            Debug.Log($"目标位置: {targetObject.transform.position}");
            Debug.Log($"设置目标后摄像机位置: {finalCameraPos}");
            Debug.Log($"期望摄像机位置: {expectedPosition}");
            Debug.Log($"位置差异: {Vector3.Distance(finalCameraPos, expectedPosition)}");

            // 检查各个组件
            Debug.Log($"摄像机组件存在: {cameraComponent != null}");
            Debug.Log($"摄像机正交: {cameraComponent.orthographic}");
            Debug.Log($"摄像机尺寸: {cameraComponent.orthographicSize}");
            Debug.Log($"摄像机宽高比: {cameraComponent.aspect}");

            // 验证结果 - 使用更宽松的容差
            bool xMatches = Mathf.Abs(finalCameraPos.x - expectedPosition.x) <= 0.1f;
            bool yMatches = Mathf.Abs(finalCameraPos.y - expectedPosition.y) <= 0.1f;
            bool zMatches = Mathf.Abs(finalCameraPos.z - expectedPosition.z) <= 0.1f;

            Debug.Log($"X轴匹配: {xMatches} (差异: {Mathf.Abs(finalCameraPos.x - expectedPosition.x)})");
            Debug.Log($"Y轴匹配: {yMatches} (差异: {Mathf.Abs(finalCameraPos.y - expectedPosition.y)})");
            Debug.Log($"Z轴匹配: {zMatches} (差异: {Mathf.Abs(finalCameraPos.z - expectedPosition.z)})");

            Assert.IsTrue(xMatches && yMatches && zMatches,
                $"位置设置失败。期望: {expectedPosition}, 实际: {finalCameraPos}");
        }
        
        [Test]
        public void DiagnoseUpdateCameraPosition()
        {
            Debug.Log("--- 诊断UpdateCameraPosition ---");
            
            // 设置初始状态
            cameraFollower.SetFollowTarget(targetObject.transform);
            Vector3 initialCameraPos = cameraObject.transform.position;
            
            Debug.Log($"设置目标后初始摄像机位置: {initialCameraPos}");
            
            // 移动目标
            Vector3 newTargetPos = new Vector3(5, 0, 0);
            targetObject.transform.position = newTargetPos;
            
            Debug.Log($"目标移动到: {newTargetPos}");
            
            // 更新摄像机位置
            cameraFollower.UpdateCameraPosition();
            
            Vector3 finalCameraPos = cameraObject.transform.position;
            Debug.Log($"更新后摄像机位置: {finalCameraPos}");
            Debug.Log($"摄像机X轴移动: {finalCameraPos.x - initialCameraPos.x}");
            
            // 检查是否向右移动
            bool movedRight = finalCameraPos.x > initialCameraPos.x;
            Debug.Log($"摄像机向右移动: {movedRight}");
            
            Assert.IsTrue(movedRight, "摄像机应该跟随目标向右移动");
        }
        
        [Test]
        public void DiagnoseCameraBounds()
        {
            Debug.Log("--- 诊断摄像机边界 ---");

            // 使用更大的边界，确保摄像机视野能完全在边界内
            Rect bounds = new Rect(-25, -15, 50, 30);
            cameraFollower.SetCameraBounds(bounds);
            cameraFollower.SetFollowTarget(targetObject.transform);

            Debug.Log($"设置边界: {bounds}");
            Debug.Log($"摄像机尺寸: {cameraComponent.orthographicSize}");
            Debug.Log($"摄像机宽高比: {cameraComponent.aspect}");

            // 计算摄像机视野
            float cameraHeight = cameraComponent.orthographicSize * 2f;
            // 在测试环境中使用固定的aspect比例
            float aspect = 16f / 9f; // 使用标准的16:9比例
            float cameraWidth = cameraHeight * aspect;

            Debug.Log($"摄像机视野高度: {cameraHeight}");
            Debug.Log($"摄像机视野宽度: {cameraWidth}");

            // 计算边界限制
            float minX = bounds.xMin + cameraWidth * 0.5f;
            float maxX = bounds.xMax - cameraWidth * 0.5f;
            float minY = bounds.yMin + cameraHeight * 0.5f;
            float maxY = bounds.yMax - cameraHeight * 0.5f;

            Debug.Log($"X轴限制范围: [{minX}, {maxX}]");
            Debug.Log($"Y轴限制范围: [{minY}, {maxY}]");

            // 验证边界计算是否合理
            bool boundsValid = minX <= maxX && minY <= maxY;
            Debug.Log($"边界计算有效: {boundsValid}");

            if (!boundsValid)
            {
                Debug.LogWarning("边界太小，摄像机将被居中");
                // 如果边界无效，测试摄像机是否被正确居中
                Vector3 centerPosition = new Vector3(bounds.center.x, bounds.center.y, -10);

                // 移动目标到边界外
                Vector3 farTarget = new Vector3(50, 0, 0);
                targetObject.transform.position = farTarget;

                Debug.Log($"目标移动到边界外: {farTarget}");

                // 更新摄像机位置
                cameraFollower.UpdateCameraPosition();

                Vector3 clampedCameraPos = cameraObject.transform.position;
                Debug.Log($"限制后摄像机位置: {clampedCameraPos}");
                Debug.Log($"期望居中位置: {centerPosition}");

                // 验证摄像机是否被正确居中
                bool centeredCorrectly = Vector3.Distance(clampedCameraPos, centerPosition) <= 0.1f;
                Debug.Log($"摄像机正确居中: {centeredCorrectly}");

                Assert.IsTrue(centeredCorrectly, "当边界太小时，摄像机应该被居中");
            }
            else
            {
                // 正常的边界限制测试
                // 移动目标到边界外
                Vector3 farTarget = new Vector3(50, 0, 0);
                targetObject.transform.position = farTarget;

                Debug.Log($"目标移动到边界外: {farTarget}");

                // 更新摄像机位置
                cameraFollower.UpdateCameraPosition();

                Vector3 clampedCameraPos = cameraObject.transform.position;
                Debug.Log($"限制后摄像机位置: {clampedCameraPos}");

                // 验证边界限制
                bool withinBoundsX = clampedCameraPos.x >= minX - 0.1f && clampedCameraPos.x <= maxX + 0.1f;
                bool withinBoundsY = clampedCameraPos.y >= minY - 0.1f && clampedCameraPos.y <= maxY + 0.1f;

                Debug.Log($"X轴在边界内: {withinBoundsX} (位置: {clampedCameraPos.x}, 范围: [{minX}, {maxX}])");
                Debug.Log($"Y轴在边界内: {withinBoundsY} (位置: {clampedCameraPos.y}, 范围: [{minY}, {maxY}])");

                Assert.IsTrue(withinBoundsX && withinBoundsY, "摄像机应该被边界限制");
            }
        }
        
        [UnityTest]
        public IEnumerator DiagnoseSmoothMovement()
        {
            Debug.Log("--- 诊断平滑移动 ---");
            
            // 设置平滑移动参数
            cameraFollower.SetSmoothTime(0.1f);
            cameraFollower.SetFollowTarget(targetObject.transform);
            
            Vector3 initialPos = cameraObject.transform.position;
            Debug.Log($"初始摄像机位置: {initialPos}");
            
            // 移动目标
            targetObject.transform.position = new Vector3(5, 0, 0);
            Debug.Log($"目标移动到: {targetObject.transform.position}");
            
            // 记录多帧的移动过程
            for (int frame = 0; frame < 10; frame++)
            {
                cameraFollower.UpdateCameraPosition();
                Vector3 currentPos = cameraObject.transform.position;
                float distance = Vector3.Distance(currentPos, initialPos);
                
                Debug.Log($"帧 {frame}: 摄像机位置 {currentPos}, 移动距离 {distance}");
                
                yield return null;
            }
            
            Vector3 finalPos = cameraObject.transform.position;
            float totalMovement = Vector3.Distance(finalPos, initialPos);
            
            Debug.Log($"最终摄像机位置: {finalPos}");
            Debug.Log($"总移动距离: {totalMovement}");
            
            Assert.Greater(totalMovement, 0.1f, "摄像机应该有明显的移动");
        }
        
        [Test]
        public void DiagnoseShakeEffect()
        {
            Debug.Log("--- 诊断震动效果 ---");
            
            cameraFollower.SetFollowTarget(targetObject.transform);
            Vector3 originalPos = cameraObject.transform.position;
            
            Debug.Log($"震动前位置: {originalPos}");
            
            // 触发震动
            cameraFollower.ShakeCamera(1f, 0.5f);
            
            Debug.Log("震动已触发");
            
            // 这里只能测试方法调用不会出错，实际震动效果需要在运行时观察
            Assert.DoesNotThrow(() => cameraFollower.ShakeCamera(1f, 0.5f));
        }
        
        [Test]
        public void DiagnoseComponentIntegrity()
        {
            Debug.Log("--- 诊断组件完整性 ---");
            
            // 检查所有必要的组件
            Debug.Log($"摄像机对象存在: {cameraObject != null}");
            Debug.Log($"目标对象存在: {targetObject != null}");
            Debug.Log($"CameraFollower组件存在: {cameraFollower != null}");
            Debug.Log($"Camera组件存在: {cameraComponent != null}");
            
            // 检查摄像机设置
            if (cameraComponent != null)
            {
                Debug.Log($"摄像机类型: {(cameraComponent.orthographic ? "正交" : "透视")}");
                Debug.Log($"正交尺寸: {cameraComponent.orthographicSize}");
                Debug.Log($"宽高比: {cameraComponent.aspect}");
                Debug.Log($"近裁剪面: {cameraComponent.nearClipPlane}");
                Debug.Log($"远裁剪面: {cameraComponent.farClipPlane}");
            }
            
            // 检查CameraFollower的公共方法
            Assert.DoesNotThrow(() => cameraFollower.GetCameraPosition());
            Assert.DoesNotThrow(() => cameraFollower.EnableFollowing(true));
            Assert.DoesNotThrow(() => cameraFollower.SetFollowSpeed(2f));
            Assert.DoesNotThrow(() => cameraFollower.SetOffset(Vector3.zero));
            Assert.DoesNotThrow(() => cameraFollower.SetSmoothTime(0.3f));
            Assert.DoesNotThrow(() => cameraFollower.EnableBounds(true));
            Assert.DoesNotThrow(() => cameraFollower.SnapToTarget());
            
            Debug.Log("所有组件和方法检查完成");
        }
    }
}