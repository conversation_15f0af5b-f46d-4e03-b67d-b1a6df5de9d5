using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraFollower组件的单元测试
    /// 测试摄像机跟随、边界限制和平滑移动功能
    /// </summary>
    public class CameraFollowerTests
    {
        private GameObject cameraObject;
        private GameObject targetObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        [SetUp]
        public void SetUp()
        {
            // 创建摄像机对象
            cameraObject = new GameObject("TestCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            // 在测试环境中设置固定的aspect比例，避免异常值
            cameraComponent.aspect = 16f / 9f; // 标准16:9比例
            cameraFollower = cameraObject.AddComponent<CameraFollower>();

            // 手动触发初始化，确保cameraComponent被正确设置
            cameraFollower.enabled = true;

            // 手动调用Awake方法来初始化组件
            var awakeMethod = typeof(CameraFollower).GetMethod("Awake",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            awakeMethod?.Invoke(cameraFollower, null);

            // 创建目标对象
            targetObject = new GameObject("TestTarget");
            targetObject.transform.position = Vector3.zero;
        }
        
        [TearDown]
        public void TearDown()
        {
            if (cameraObject != null)
                Object.DestroyImmediate(cameraObject);
            if (targetObject != null)
                Object.DestroyImmediate(targetObject);
        }
        
        [Test]
        public void SetFollowTarget_SetsTargetCorrectly()
        {
            // Arrange - 禁用边界限制，确保测试不受边界影响
            cameraFollower.EnableBounds(false);

            // Act
            cameraFollower.SetFollowTarget(targetObject.transform);

            // Assert
            Assert.IsNotNull(cameraFollower);
            // 验证摄像机位置已更新到目标附近
            Vector3 expectedPosition = targetObject.transform.position + new Vector3(0, 1, -10);
            Vector3 actualPosition = cameraObject.transform.position;

            Assert.AreEqual(expectedPosition.x, actualPosition.x, 0.1f,
                $"X位置不匹配。期望: {expectedPosition.x}, 实际: {actualPosition.x}");
            Assert.AreEqual(expectedPosition.y, actualPosition.y, 0.1f,
                $"Y位置不匹配。期望: {expectedPosition.y}, 实际: {actualPosition.y}");
            Assert.AreEqual(expectedPosition.z, actualPosition.z, 0.1f,
                $"Z位置不匹配。期望: {expectedPosition.z}, 实际: {actualPosition.z}");
        }
        
        [Test]
        public void UpdateCameraPosition_FollowsTargetMovement()
        {
            // Arrange
            cameraFollower.EnableBounds(false); // 禁用边界限制
            cameraFollower.SetFollowTarget(targetObject.transform);
            Vector3 initialCameraPos = cameraObject.transform.position;

            // Act
            targetObject.transform.position = new Vector3(5, 0, 0);
            cameraFollower.UpdateCameraPosition();

            // Assert
            Vector3 newCameraPos = cameraObject.transform.position;
            Assert.Greater(newCameraPos.x, initialCameraPos.x, "摄像机应该跟随目标向右移动");
        }
        
        [Test]
        public void SetCameraBounds_LimitsCameraMovement()
        {
            // Arrange
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraFollower.SetCameraBounds(bounds);

            // 先设置目标在原点
            targetObject.transform.position = Vector3.zero;
            cameraFollower.SetFollowTarget(targetObject.transform);

            // Act - 移动目标到边界外
            targetObject.transform.position = new Vector3(20, 0, 0);

            // 确保边界限制已启用
            cameraFollower.EnableBounds(true);

            // 多次调用UpdateCameraPosition确保移动生效
            for (int i = 0; i < 5; i++)
            {
                cameraFollower.UpdateCameraPosition();
            }

            // Assert - 摄像机应该被限制在边界内
            Vector3 cameraPos = cameraObject.transform.position;
            float cameraHeight = cameraComponent.orthographicSize * 2f;
            float cameraWidth = cameraHeight * cameraComponent.aspect;

            // 计算有效的摄像机移动范围
            float halfWidth = cameraWidth * 0.5f;
            float minX = bounds.xMin + halfWidth;
            float maxX = bounds.xMax - halfWidth;

            // 如果边界太小，摄像机会被居中到边界中心
            if (minX > maxX)
            {
                float expectedX = bounds.center.x;
                Assert.AreEqual(expectedX, cameraPos.x, 0.1f,
                    $"边界太小时摄像机应居中。位置: {cameraPos.x}, 预期: {expectedX}, 边界: {bounds}, 摄像机宽度: {cameraWidth}, minX: {minX}, maxX: {maxX}");
            }
            else
            {
                Assert.LessOrEqual(cameraPos.x, maxX + 0.1f,
                    $"摄像机不应超出右边界。位置: {cameraPos.x}, 最大允许: {maxX}, 边界: {bounds}, 摄像机宽度: {cameraWidth}");
            }
        }
        
        [Test]
        public void EnableFollowing_DisablesFollowingWhenFalse()
        {
            // Arrange
            cameraFollower.EnableBounds(false); // 禁用边界限制
            cameraFollower.SetFollowTarget(targetObject.transform);
            Vector3 initialCameraPos = cameraObject.transform.position;

            // Act
            cameraFollower.EnableFollowing(false);
            targetObject.transform.position = new Vector3(10, 0, 0);
            cameraFollower.UpdateCameraPosition();

            // Assert
            Vector3 currentCameraPos = cameraObject.transform.position;
            Assert.AreEqual(initialCameraPos, currentCameraPos, "禁用跟随时摄像机不应移动");
        }
        
        [Test]
        public void ShakeCamera_TriggersShakeEffect()
        {
            // Arrange
            Vector3 originalPosition = cameraObject.transform.position;
            
            // Act
            cameraFollower.ShakeCamera(1f, 0.5f);
            
            // Assert
            // 震动效果会在下一帧生效，这里主要测试方法调用不会出错
            Assert.DoesNotThrow(() => cameraFollower.ShakeCamera(1f, 0.5f));
        }
        
        [Test]
        public void GetCameraPosition_ReturnsCorrectPosition()
        {
            // Arrange
            Vector3 testPosition = new Vector3(3, 2, -10);
            cameraObject.transform.position = testPosition;
            
            // Act
            Vector3 returnedPosition = cameraFollower.GetCameraPosition();
            
            // Assert
            Assert.AreEqual(testPosition, returnedPosition);
        }
        
        [UnityTest]
        public IEnumerator UpdateCameraPosition_SmoothlyFollowsTarget()
        {
            // Arrange
            cameraFollower.EnableBounds(false); // 禁用边界限制
            cameraFollower.SetFollowTarget(targetObject.transform);
            cameraFollower.SetSmoothTime(0.1f);
            Vector3 initialPos = cameraObject.transform.position;

            // Act
            targetObject.transform.position = new Vector3(5, 0, 0);

            // 等待几帧让平滑移动生效
            for (int i = 0; i < 10; i++)
            {
                cameraFollower.UpdateCameraPosition();
                yield return null;
            }

            // Assert
            Vector3 finalPos = cameraObject.transform.position;
            Assert.Greater(finalPos.x, initialPos.x, "摄像机应该平滑跟随目标移动");
            Assert.Less(Mathf.Abs(finalPos.x - 5f), 1f, "摄像机应该接近目标位置");
        }
        
        [Test]
        public void SetOffset_ChangesFollowOffset()
        {
            // Arrange
            Vector3 newOffset = new Vector3(2, 3, -15);
            cameraFollower.EnableBounds(false); // 禁用边界限制
            cameraFollower.SetFollowTarget(targetObject.transform);

            // Act
            cameraFollower.SetOffset(newOffset);

            // Assert
            Vector3 expectedPosition = targetObject.transform.position + newOffset;
            Vector3 actualPosition = cameraObject.transform.position;
            Assert.AreEqual(expectedPosition.x, actualPosition.x, 0.1f);
            Assert.AreEqual(expectedPosition.y, actualPosition.y, 0.1f);
        }
        
        [Test]
        public void SnapToTarget_MovesImmediatelyToTarget()
        {
            // Arrange
            cameraFollower.EnableBounds(false); // 禁用边界限制
            cameraFollower.SetFollowTarget(targetObject.transform);
            targetObject.transform.position = new Vector3(10, 5, 0);

            // Act
            cameraFollower.SnapToTarget();

            // Assert
            Vector3 expectedPosition = targetObject.transform.position + new Vector3(0, 1, -10);
            Vector3 actualPosition = cameraObject.transform.position;
            Assert.AreEqual(expectedPosition.x, actualPosition.x, 0.01f);
            Assert.AreEqual(expectedPosition.y, actualPosition.y, 0.01f);
        }
        
        [Test]
        public void SetFollowSpeed_ChangesFollowSpeed()
        {
            // Arrange & Act
            cameraFollower.SetFollowSpeed(5f);
            
            // Assert
            Assert.DoesNotThrow(() => cameraFollower.SetFollowSpeed(5f));
            
            // 测试边界情况
            cameraFollower.SetFollowSpeed(-1f);
            // 应该被限制为最小值0.1f
        }
        
        [Test]
        public void EnableBounds_TogglesBoundaryRestriction()
        {
            // Arrange
            Rect bounds = new Rect(-2, -2, 4, 4);
            cameraFollower.SetCameraBounds(bounds);
            cameraFollower.SetFollowTarget(targetObject.transform);
            // 确保初始设置生效
            cameraFollower.UpdateCameraPosition();

            // Act - 禁用边界
            cameraFollower.EnableBounds(false);
            targetObject.transform.position = new Vector3(20, 0, 0);
            cameraFollower.UpdateCameraPosition();

            // Assert
            Vector3 cameraPos = cameraObject.transform.position;
            // 禁用边界时，摄像机应该能够跟随到边界外
            Assert.Greater(cameraPos.x, bounds.xMax, "禁用边界时摄像机应该能超出边界");
        }
    }
}