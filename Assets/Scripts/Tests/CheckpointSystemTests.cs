using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Level;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 检查点系统的集成测试
    /// </summary>
    public class CheckpointSystemTests
    {
        private GameObject _checkpointObject;
        private Checkpoint _checkpoint;
        private GameObject _playerObject;
        private GameObject _checkpointManagerObject;
        private CheckpointManager _checkpointManager;
        
        [SetUp]
        public void SetUp()
        {
            // 创建玩家对象
            _playerObject = new GameObject("Player");
            _playerObject.tag = "Player";
            _playerObject.AddComponent<Health>();
            _playerObject.AddComponent<Rigidbody2D>();
            _playerObject.AddComponent<BoxCollider2D>();
            
            // 创建检查点对象
            _checkpointObject = new GameObject("Checkpoint");
            _checkpoint = _checkpointObject.AddComponent<Checkpoint>();
            _checkpointObject.AddComponent<CircleCollider2D>().isTrigger = true;
            
            // 创建检查点管理器
            _checkpointManagerObject = new GameObject("CheckpointManager");
            _checkpointManager = _checkpointManagerObject.AddComponent<CheckpointManager>();
            
            // 清除PlayerPrefs
            PlayerPrefs.DeleteKey("GameProgress");
        }
        
        [TearDown]
        public void TearDown()
        {
            if (_checkpointObject != null)
                Object.DestroyImmediate(_checkpointObject);
            
            if (_playerObject != null)
                Object.DestroyImmediate(_playerObject);
            
            if (_checkpointManagerObject != null)
                Object.DestroyImmediate(_checkpointManagerObject);
            
            PlayerPrefs.DeleteKey("GameProgress");
        }
        
        [Test]
        public void Checkpoint_InitialState_IsNotActivated()
        {
            // Assert
            Assert.IsFalse(_checkpoint.IsActivated(), "检查点初始状态应该是未激活");
        }
        
        [Test]
        public void Checkpoint_HasValidId_AfterInitialization()
        {
            // Act
            string checkpointId = _checkpoint.GetCheckpointId();
            
            // Assert
            Assert.IsNotEmpty(checkpointId, "检查点应该有有效的ID");
        }
        
        [Test]
        public void Checkpoint_SetCustomId_UpdatesCorrectly()
        {
            // Arrange
            string customId = "test_checkpoint_001";
            
            // Act
            _checkpoint.SetCheckpointId(customId);
            
            // Assert
            Assert.AreEqual(customId, _checkpoint.GetCheckpointId(), "检查点ID应该被正确设置");
        }
        
        [UnityTest]
        public IEnumerator Checkpoint_PlayerEntersTrigger_ActivatesAutomatically()
        {
            // Arrange
            _playerObject.transform.position = new Vector3(10, 0, 0);
            _checkpointObject.transform.position = Vector3.zero;
            bool activated = false;
            _checkpoint.OnCheckpointActivated += (cp) => activated = true;
            
            // Act - 移动玩家到检查点位置
            _playerObject.transform.position = Vector3.zero;
            
            // 等待物理更新
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(activated, "玩家进入触发器时检查点应该自动激活");
            Assert.IsTrue(_checkpoint.IsActivated(), "检查点状态应该是已激活");
        }
        
        [Test]
        public void Checkpoint_ManualActivation_WorksCorrectly()
        {
            // Arrange
            _checkpoint.SetAutoActivate(false);
            bool activated = false;
            _checkpoint.OnCheckpointActivated += (cp) => activated = true;
            
            // Act
            _checkpoint.ActivateCheckpoint();
            
            // Assert
            Assert.IsTrue(activated, "手动激活应该触发激活事件");
            Assert.IsTrue(_checkpoint.IsActivated(), "检查点应该被激活");
        }
        
        [Test]
        public void Checkpoint_Reset_ClearsActivationState()
        {
            // Arrange
            _checkpoint.ActivateCheckpoint();
            Assert.IsTrue(_checkpoint.IsActivated(), "前置条件：检查点应该已激活");
            
            // Act
            _checkpoint.ResetCheckpoint();
            
            // Assert
            Assert.IsFalse(_checkpoint.IsActivated(), "重置后检查点应该是未激活状态");
        }
        
        [Test]
        public void Checkpoint_RespawnPlayer_MovesPlayerToCheckpointPosition()
        {
            // Arrange
            Vector3 checkpointPosition = new Vector3(5, 3, 0);
            _checkpointObject.transform.position = checkpointPosition;
            _playerObject.transform.position = new Vector3(10, 10, 0);
            _checkpoint.ActivateCheckpoint();
            
            // Act
            _checkpoint.RespawnPlayer();
            
            // Assert
            Assert.AreEqual(checkpointPosition, _playerObject.transform.position, "玩家应该被移动到检查点位置");
        }
        
        [Test]
        public void Checkpoint_RespawnPlayer_ResetsPlayerHealth()
        {
            // Arrange
            var healthComponent = _playerObject.GetComponent<Health>();
            healthComponent.TakeDamage(30);
            _checkpoint.ActivateCheckpoint();
            
            // Act
            _checkpoint.RespawnPlayer();
            
            // Assert
            Assert.AreEqual(healthComponent.MaxHealth, healthComponent.CurrentHealth, "重生时玩家生命值应该被重置");
        }
        
        [Test]
        public void Checkpoint_SetActivationRadius_UpdatesCollider()
        {
            // Arrange
            float newRadius = 3.5f;
            var collider = _checkpointObject.GetComponent<CircleCollider2D>();
            
            // Act
            _checkpoint.SetActivationRadius(newRadius);
            
            // Assert
            Assert.AreEqual(newRadius, collider.radius, "碰撞器半径应该被更新");
        }
        
        [Test]
        public void CheckpointManager_InitialState_HasNoCheckpoints()
        {
            // Assert
            Assert.AreEqual(0, _checkpointManager.GetTotalCheckpointCount(), "初始状态下应该没有检查点");
        }
        
        [Test]
        public void CheckpointManager_AddCheckpoint_IncreasesCount()
        {
            // Act
            _checkpointManager.AddCheckpoint(_checkpoint);
            
            // Assert
            Assert.AreEqual(1, _checkpointManager.GetTotalCheckpointCount(), "添加检查点后数量应该增加");
        }
        
        [Test]
        public void CheckpointManager_GetCheckpointById_ReturnsCorrectCheckpoint()
        {
            // Arrange
            string testId = "test_checkpoint";
            _checkpoint.SetCheckpointId(testId);
            _checkpointManager.AddCheckpoint(_checkpoint);
            
            // Act
            var foundCheckpoint = _checkpointManager.GetCheckpointById(testId);
            
            // Assert
            Assert.AreEqual(_checkpoint, foundCheckpoint, "应该返回正确的检查点");
        }
        
        [Test]
        public void CheckpointManager_GetCheckpointByIndex_ReturnsCorrectCheckpoint()
        {
            // Arrange
            _checkpointManager.AddCheckpoint(_checkpoint);
            
            // Act
            var foundCheckpoint = _checkpointManager.GetCheckpointByIndex(0);
            
            // Assert
            Assert.AreEqual(_checkpoint, foundCheckpoint, "应该返回正确索引的检查点");
        }
        
        [Test]
        public void CheckpointManager_RemoveCheckpoint_DecreasesCount()
        {
            // Arrange
            _checkpointManager.AddCheckpoint(_checkpoint);
            Assert.AreEqual(1, _checkpointManager.GetTotalCheckpointCount(), "前置条件：应该有1个检查点");
            
            // Act
            _checkpointManager.RemoveCheckpoint(_checkpoint);
            
            // Assert
            Assert.AreEqual(0, _checkpointManager.GetTotalCheckpointCount(), "移除检查点后数量应该减少");
        }
        
        [Test]
        public void CheckpointManager_ResetAllCheckpoints_ClearsAllActivations()
        {
            // Arrange
            var checkpoint2 = new GameObject("Checkpoint2").AddComponent<Checkpoint>();
            _checkpointManager.AddCheckpoint(_checkpoint);
            _checkpointManager.AddCheckpoint(checkpoint2);
            
            _checkpoint.ActivateCheckpoint();
            checkpoint2.ActivateCheckpoint();
            
            // Act
            _checkpointManager.ResetAllCheckpoints();
            
            // Assert
            Assert.IsFalse(_checkpoint.IsActivated(), "第一个检查点应该被重置");
            Assert.IsFalse(checkpoint2.IsActivated(), "第二个检查点应该被重置");
            Assert.IsNull(_checkpointManager.GetLastActivatedCheckpoint(), "最后激活的检查点应该被清除");
            
            // Cleanup
            Object.DestroyImmediate(checkpoint2.gameObject);
        }
        
        [Test]
        public void CheckpointManager_GetActivatedCheckpointCount_ReturnsCorrectCount()
        {
            // Arrange
            var checkpoint2 = new GameObject("Checkpoint2").AddComponent<Checkpoint>();
            _checkpointManager.AddCheckpoint(_checkpoint);
            _checkpointManager.AddCheckpoint(checkpoint2);
            
            // Act & Assert - 初始状态
            Assert.AreEqual(0, _checkpointManager.GetActivatedCheckpointCount(), "初始状态下应该没有激活的检查点");
            
            // Act & Assert - 激活一个
            _checkpoint.ActivateCheckpoint();
            Assert.AreEqual(1, _checkpointManager.GetActivatedCheckpointCount(), "激活一个检查点后计数应该是1");
            
            // Act & Assert - 激活两个
            checkpoint2.ActivateCheckpoint();
            Assert.AreEqual(2, _checkpointManager.GetActivatedCheckpointCount(), "激活两个检查点后计数应该是2");
            
            // Cleanup
            Object.DestroyImmediate(checkpoint2.gameObject);
        }
        
        [Test]
        public void CheckpointManager_AreAllCheckpointsActivated_WorksCorrectly()
        {
            // Arrange
            var checkpoint2 = new GameObject("Checkpoint2").AddComponent<Checkpoint>();
            _checkpointManager.AddCheckpoint(_checkpoint);
            _checkpointManager.AddCheckpoint(checkpoint2);
            
            // Act & Assert - 初始状态
            Assert.IsFalse(_checkpointManager.AreAllCheckpointsActivated(), "初始状态下不是所有检查点都激活");
            
            // Act & Assert - 激活一个
            _checkpoint.ActivateCheckpoint();
            Assert.IsFalse(_checkpointManager.AreAllCheckpointsActivated(), "只激活一个检查点时不是所有都激活");
            
            // Act & Assert - 激活所有
            checkpoint2.ActivateCheckpoint();
            Assert.IsTrue(_checkpointManager.AreAllCheckpointsActivated(), "激活所有检查点后应该返回true");
            
            // Cleanup
            Object.DestroyImmediate(checkpoint2.gameObject);
        }
        
        [UnityTest]
        public IEnumerator CheckpointManager_RespawnAtLastCheckpoint_WorksCorrectly()
        {
            // Arrange
            Vector3 checkpointPosition = new Vector3(8, 4, 0);
            _checkpointObject.transform.position = checkpointPosition;
            _playerObject.transform.position = new Vector3(0, 0, 0);
            _checkpointManager.AddCheckpoint(_checkpoint);
            _checkpoint.ActivateCheckpoint();
            
            // Act
            _checkpointManager.RespawnAtLastCheckpoint();
            
            // 等待重生协程完成
            yield return new WaitForSeconds(0.1f);
            
            // Assert
            Assert.AreEqual(checkpointPosition, _playerObject.transform.position, "玩家应该在最后激活的检查点重生");
        }
        
        [Test]
        public void CheckpointManager_GetLastActivatedCheckpoint_ReturnsCorrectCheckpoint()
        {
            // Arrange
            var checkpoint2 = new GameObject("Checkpoint2").AddComponent<Checkpoint>();
            _checkpointManager.AddCheckpoint(_checkpoint);
            _checkpointManager.AddCheckpoint(checkpoint2);
            
            // Act
            _checkpoint.ActivateCheckpoint();
            checkpoint2.ActivateCheckpoint();
            
            // Assert
            Assert.AreEqual(checkpoint2, _checkpointManager.GetLastActivatedCheckpoint(), "应该返回最后激活的检查点");
            
            // Cleanup
            Object.DestroyImmediate(checkpoint2.gameObject);
        }
    }
}