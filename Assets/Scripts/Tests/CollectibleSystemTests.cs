using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 收集品系统测试
    /// </summary>
    public class CollectibleSystemTests
    {
        private GameObject collectibleObject;
        private Collectible collectible;
        private GameObject playerObject;
        private CollectibleData testData;
        private ScoreManager scoreManager;
        private InventoryManager inventoryManager;

        [SetUp]
        public void SetUp()
        {
            // 创建收集品对象
            collectibleObject = new GameObject("TestCollectible");
            collectibleObject.AddComponent<SpriteRenderer>();
            collectibleObject.AddComponent<CircleCollider2D>();
            collectible = collectibleObject.AddComponent<Collectible>();

            // 创建玩家对象
            playerObject = new GameObject("Player");
            playerObject.tag = "Player";
            playerObject.AddComponent<BoxCollider2D>();

            // 创建测试数据
            testData = new CollectibleData
            {
                type = CollectibleType.Coin,
                itemName = "Test Coin",
                description = "A test coin",
                value = 10,
                quantity = 1,
                isConsumable = true,
                glowColor = Color.yellow,
                animationSpeed = 1f,
                soundVolume = 1f
            };

            // 设置收集品数据
            collectible.SetData(testData);

            // 创建管理器
            GameObject managerObject = new GameObject("Managers");
            scoreManager = managerObject.AddComponent<ScoreManager>();
            inventoryManager = managerObject.AddComponent<InventoryManager>();
        }

        [TearDown]
        public void TearDown()
        {
            if (collectibleObject != null)
                Object.DestroyImmediate(collectibleObject);
            if (playerObject != null)
                Object.DestroyImmediate(playerObject);
            if (scoreManager != null)
                Object.DestroyImmediate(scoreManager.gameObject);
        }

        #region CollectibleData Tests

        [Test]
        public void CollectibleData_Validation_ReturnsCorrectResult()
        {
            // Arrange
            var validData = new CollectibleData
            {
                itemName = "Valid Item",
                value = 10,
                quantity = 1,
                soundVolume = 0.5f
            };

            var invalidData = new CollectibleData
            {
                itemName = "", // 无效名称
                value = -5,    // 无效值
                quantity = 0,  // 无效数量
                soundVolume = 2f // 无效音量
            };

            // Act & Assert
            Assert.IsTrue(validData.IsValid());
            Assert.IsFalse(invalidData.IsValid());
        }

        [Test]
        public void CollectibleData_GetDisplayText_ReturnsCorrectFormat()
        {
            // Arrange
            var singleItem = new CollectibleData { itemName = "Coin", quantity = 1 };
            var multipleItems = new CollectibleData { itemName = "Gem", quantity = 5 };

            // Act & Assert
            Assert.AreEqual("Coin", singleItem.GetDisplayText());
            Assert.AreEqual("Gem x5", multipleItems.GetDisplayText());
        }

        [Test]
        public void CollectibleData_GetTotalValue_CalculatesCorrectly()
        {
            // Arrange
            var data = new CollectibleData { value = 10, quantity = 3 };

            // Act
            int totalValue = data.GetTotalValue();

            // Assert
            Assert.AreEqual(30, totalValue);
        }

        #endregion

        #region Collectible Tests

        [Test]
        public void Collectible_Initialization_SetsCorrectData()
        {
            // Assert
            Assert.IsNotNull(collectible.Data);
            Assert.AreEqual(testData.itemName, collectible.Data.itemName);
            Assert.IsFalse(collectible.IsCollected);
        }

        [Test]
        public void Collectible_SetData_UpdatesCorrectly()
        {
            // Arrange
            var newData = new CollectibleData
            {
                itemName = "New Item",
                value = 20
            };

            // Act
            collectible.SetData(newData);

            // Assert
            Assert.AreEqual("New Item", collectible.Data.itemName);
            Assert.AreEqual(20, collectible.Data.value);
        }

        [UnityTest]
        public IEnumerator Collectible_TriggerCollection_WorksCorrectly()
        {
            // Arrange
            bool collectionTriggered = false;
            collectible.OnCollected += (data) => collectionTriggered = true;

            // Act
            collectible.Collect(playerObject);
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.IsTrue(collectionTriggered);
            Assert.IsTrue(collectible.IsCollected);
        }

        [UnityTest]
        public IEnumerator Collectible_CollectionAnimation_CompletesCorrectly()
        {
            // Arrange
            bool animationCompleted = false;
            collectible.OnCollectionComplete += (c) => animationCompleted = true;

            // Act
            collectible.Collect(playerObject);
            yield return new WaitForSeconds(1f); // 等待动画完成

            // Assert
            Assert.IsTrue(animationCompleted);
        }

        [Test]
        public void Collectible_PreventDoubleCollection_WorksCorrectly()
        {
            // Arrange
            int collectionCount = 0;
            collectible.OnCollected += (data) => collectionCount++;

            // Act
            collectible.Collect(playerObject);
            collectible.Collect(playerObject); // 尝试重复收集

            // Assert
            Assert.AreEqual(1, collectionCount);
        }

        #endregion

        #region ScoreManager Tests

        [Test]
        public void ScoreManager_Initialization_SetsCorrectDefaults()
        {
            // Assert
            Assert.AreEqual(0, scoreManager.CurrentScore);
            Assert.AreEqual(1f, scoreManager.ScoreMultiplier);
            Assert.AreEqual(0, scoreManager.ComboCount);
        }

        [Test]
        public void ScoreManager_AddScore_UpdatesCorrectly()
        {
            // Arrange
            int initialScore = scoreManager.CurrentScore;
            bool scoreChanged = false;
            scoreManager.OnScoreChanged += (score) => scoreChanged = true;

            // Act
            scoreManager.AddScore(100);

            // Assert
            Assert.AreEqual(initialScore + 100, scoreManager.CurrentScore);
            Assert.IsTrue(scoreChanged);
        }

        [Test]
        public void ScoreManager_HighScore_UpdatesWhenExceeded()
        {
            // Arrange
            bool highScoreChanged = false;
            scoreManager.OnHighScoreChanged += (score) => highScoreChanged = true;

            // Act
            scoreManager.AddScore(1000);

            // Assert
            Assert.AreEqual(1000, scoreManager.HighScore);
            Assert.IsTrue(highScoreChanged);
        }

        [Test]
        public void ScoreManager_SetScoreMultiplier_UpdatesCorrectly()
        {
            // Act
            scoreManager.SetScoreMultiplier(2f);

            // Assert
            Assert.AreEqual(2f, scoreManager.ScoreMultiplier);
        }

        [Test]
        public void ScoreManager_ResetCurrentScore_ClearsScore()
        {
            // Arrange
            scoreManager.AddScore(500);

            // Act
            scoreManager.ResetCurrentScore();

            // Assert
            Assert.AreEqual(0, scoreManager.CurrentScore);
            Assert.AreEqual(0, scoreManager.ComboCount);
        }

        [Test]
        public void ScoreManager_GetCollectedItemCount_ReturnsCorrectCount()
        {
            // Arrange
            var coinData = new CollectibleData { type = CollectibleType.Coin, quantity = 3 };
            
            // 模拟收集物品
            var handleItemCollectedMethod = typeof(ScoreManager).GetMethod("HandleItemCollected",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            handleItemCollectedMethod?.Invoke(scoreManager, new object[] { coinData });

            // Act
            int coinCount = scoreManager.GetCollectedItemCount(CollectibleType.Coin);

            // Assert
            Assert.AreEqual(3, coinCount);
        }

        #endregion

        #region InventoryManager Tests

        [Test]
        public void InventoryManager_Initialization_SetsCorrectDefaults()
        {
            // Assert
            Assert.AreEqual(0, inventoryManager.CurrentSize);
            Assert.IsFalse(inventoryManager.IsFull);
        }

        [Test]
        public void InventoryManager_AddItem_WorksCorrectly()
        {
            // Arrange
            bool itemAdded = false;
            inventoryManager.OnItemAdded += (item) => itemAdded = true;

            // Act
            bool result = inventoryManager.AddItem(testData, 1);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(itemAdded);
            Assert.AreEqual(1, inventoryManager.CurrentSize);
        }

        [Test]
        public void InventoryManager_AddDuplicateItem_IncreasesQuantity()
        {
            // Arrange
            inventoryManager.AddItem(testData, 2);

            // Act
            inventoryManager.AddItem(testData, 3);

            // Assert
            Assert.AreEqual(1, inventoryManager.CurrentSize); // 仍然只有一个物品条目
            Assert.AreEqual(5, inventoryManager.GetItemCount(CollectibleType.Coin)); // 但数量增加了
        }

        [Test]
        public void InventoryManager_RemoveItem_WorksCorrectly()
        {
            // Arrange
            inventoryManager.AddItem(testData, 3);
            bool itemRemoved = false;
            inventoryManager.OnItemRemoved += (item) => itemRemoved = true;

            // Act
            bool result = inventoryManager.RemoveItem(CollectibleType.Coin, 1);

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(2, inventoryManager.GetItemCount(CollectibleType.Coin));
        }

        [Test]
        public void InventoryManager_UseItem_WorksCorrectly()
        {
            // Arrange
            var consumableData = new CollectibleData
            {
                type = CollectibleType.HealthPotion,
                itemName = "Health Potion",
                isConsumable = true,
                hasSpecialEffect = true,
                healthRestore = 20
            };
            
            inventoryManager.AddItem(consumableData, 2);
            bool itemUsed = false;
            inventoryManager.OnItemUsed += (item) => itemUsed = true;

            // Act
            bool result = inventoryManager.UseItem(CollectibleType.HealthPotion, 1);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(itemUsed);
            Assert.AreEqual(1, inventoryManager.GetItemCount(CollectibleType.HealthPotion)); // 消耗品使用后数量减少
        }

        [Test]
        public void InventoryManager_ClearInventory_RemovesAllItems()
        {
            // Arrange
            inventoryManager.AddItem(testData, 5);
            var gemData = new CollectibleData { type = CollectibleType.Gem, itemName = "Gem" };
            inventoryManager.AddItem(gemData, 3);

            // Act
            inventoryManager.ClearInventory();

            // Assert
            Assert.AreEqual(0, inventoryManager.CurrentSize);
            Assert.AreEqual(0, inventoryManager.GetItemCount(CollectibleType.Coin));
            Assert.AreEqual(0, inventoryManager.GetItemCount(CollectibleType.Gem));
        }

        [Test]
        public void InventoryManager_InventoryFull_PreventsFurtherAdditions()
        {
            // Arrange
            var smallInventory = new GameObject("SmallInventory").AddComponent<InventoryManager>();
            
            // 使用反射设置最大库存大小
            var maxSizeField = typeof(InventoryManager).GetField("maxInventorySize",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            maxSizeField?.SetValue(smallInventory, 1);

            bool inventoryFullTriggered = false;
            smallInventory.OnInventoryFull += () => inventoryFullTriggered = true;

            // Act
            smallInventory.AddItem(testData, 1); // 填满库存
            bool secondAddResult = smallInventory.AddItem(testData, 1); // 尝试再次添加

            // Assert
            Assert.IsFalse(secondAddResult);
            Assert.IsTrue(inventoryFullTriggered);

            // 清理
            Object.DestroyImmediate(smallInventory.gameObject);
        }

        #endregion

        #region Integration Tests

        [UnityTest]
        public IEnumerator CollectibleSystem_FullWorkflow_WorksCorrectly()
        {
            // Arrange
            int initialScore = scoreManager.CurrentScore;
            int initialInventorySize = inventoryManager.CurrentSize;

            // Act - 模拟玩家触碰收集品
            var playerCollider = playerObject.GetComponent<BoxCollider2D>();
            var collectibleCollider = collectibleObject.GetComponent<CircleCollider2D>();
            
            // 移动玩家到收集品位置
            playerObject.transform.position = collectibleObject.transform.position;
            
            // 手动触发碰撞
            collectible.Collect(playerObject);
            
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.Greater(scoreManager.CurrentScore, initialScore, "分数应该增加");
            Assert.IsTrue(collectible.IsCollected, "收集品应该被标记为已收集");
        }

        [Test]
        public void CollectibleSystem_ScoreCalculation_IncludesMultipliers()
        {
            // Arrange
            scoreManager.SetScoreMultiplier(2f);
            var highValueData = new CollectibleData { value = 50, quantity = 2 };

            // Act
            var handleItemCollectedMethod = typeof(ScoreManager).GetMethod("HandleItemCollected",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            handleItemCollectedMethod?.Invoke(scoreManager, new object[] { highValueData });

            // Assert
            // 基础分数: 50 * 2 = 100
            // 应用倍数: 100 * 2 = 200
            Assert.GreaterOrEqual(scoreManager.CurrentScore, 200);
        }

        [UnityTest]
        public IEnumerator CollectibleSystem_ComboSystem_WorksCorrectly()
        {
            // Arrange
            var coinData1 = new CollectibleData { type = CollectibleType.Coin, value = 10 };
            var coinData2 = new CollectibleData { type = CollectibleType.Coin, value = 10 };

            // Act - 快速连续收集
            var handleItemCollectedMethod = typeof(ScoreManager).GetMethod("HandleItemCollected",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            handleItemCollectedMethod?.Invoke(scoreManager, new object[] { coinData1 });
            yield return new WaitForSeconds(0.1f);
            handleItemCollectedMethod?.Invoke(scoreManager, new object[] { coinData2 });

            // Assert
            Assert.Greater(scoreManager.ComboCount, 0, "连击计数应该增加");
        }

        #endregion

        #region Performance Tests

        [UnityTest]
        public IEnumerator CollectibleSystem_ManyItems_PerformsWell()
        {
            // Arrange
            var collectibles = new System.Collections.Generic.List<GameObject>();
            
            // 创建多个收集品
            for (int i = 0; i < 20; i++)
            {
                var obj = new GameObject($"Collectible_{i}");
                obj.AddComponent<SpriteRenderer>();
                obj.AddComponent<CircleCollider2D>();
                var coll = obj.AddComponent<Collectible>();
                coll.SetData(testData);
                collectibles.Add(obj);
            }

            // Act - 快速收集所有物品
            float startTime = Time.realtimeSinceStartup;
            
            foreach (var obj in collectibles)
            {
                var coll = obj.GetComponent<Collectible>();
                coll.Collect(playerObject);
            }
            
            yield return new WaitForSeconds(0.5f);
            
            float endTime = Time.realtimeSinceStartup;

            // Assert
            float deltaTime = endTime - startTime;
            Assert.Less(deltaTime, 2f, "大量收集品处理应该在合理时间内完成");

            // 清理
            foreach (var obj in collectibles)
            {
                if (obj != null)
                    Object.DestroyImmediate(obj);
            }
        }

        #endregion
    }
}