using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Level;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 难度系统的测试用例
    /// </summary>
    public class DifficultySystemTests
    {
        private GameObject _difficultySystemObject;
        private DifficultySystem _difficultySystem;
        private GameObject _hintSystemObject;
        private HintSystem _hintSystem;
        
        [SetUp]
        public void SetUp()
        {
            // 创建难度系统对象
            _difficultySystemObject = new GameObject("DifficultySystem");
            _difficultySystem = _difficultySystemObject.AddComponent<DifficultySystem>();
            
            // 创建提示系统对象
            _hintSystemObject = new GameObject("HintSystem");
            _hintSystem = _hintSystemObject.AddComponent<HintSystem>();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (_difficultySystemObject != null)
                Object.DestroyImmediate(_difficultySystemObject);
            
            if (_hintSystemObject != null)
                Object.DestroyImmediate(_hintSystemObject);
        }
        
        [Test]
        public void DifficultySystem_InitialState_HasDefaultDifficulty()
        {
            // Act
            var currentDifficulty = _difficultySystem.GetCurrentDifficulty();
            
            // Assert
            Assert.IsNotNull(currentDifficulty, "应该有默认难度");
            Assert.AreEqual("简单", currentDifficulty.name, "默认难度应该是简单");
        }
        
        [Test]
        public void DifficultySystem_GetAvailableDifficultyLevels_ReturnsMultipleLevels()
        {
            // Act
            var availableLevels = _difficultySystem.GetAvailableDifficultyLevels();
            
            // Assert
            Assert.IsNotNull(availableLevels, "可用难度列表不应该为空");
            Assert.Greater(availableLevels.Count, 0, "应该有至少一个难度等级");
            Assert.IsTrue(availableLevels.Exists(d => d.name == "简单"), "应该包含简单难度");
            Assert.IsTrue(availableLevels.Exists(d => d.name == "普通"), "应该包含普通难度");
            Assert.IsTrue(availableLevels.Exists(d => d.name == "困难"), "应该包含困难难度");
        }
        
        [Test]
        public void DifficultySystem_SetDifficultyMode_UpdatesMode()
        {
            // Act
            _difficultySystem.SetDifficultyMode(DifficultyMode.Adaptive);
            
            // Assert - 通过更新难度来验证模式是否生效
            _difficultySystem.UpdateDifficultyForLevel(0);
            var currentDifficulty = _difficultySystem.GetCurrentDifficulty();
            Assert.IsNotNull(currentDifficulty, "设置适应性模式后应该有当前难度");
        }
        
        [Test]
        public void DifficultySystem_RecordLevelFailure_UpdatesStats()
        {
            // Act
            _difficultySystem.RecordLevelFailure();
            _difficultySystem.RecordLevelFailure();
            
            // Assert
            var stats = _difficultySystem.GetDifficultyStats();
            Assert.AreEqual(2, stats.currentLevelFailures, "当前关卡失败次数应该是2");
            Assert.AreEqual(2, stats.totalFailures, "总失败次数应该是2");
            Assert.AreEqual(0, stats.consecutiveSuccesses, "连续成功次数应该被重置为0");
        }
        
        [Test]
        public void DifficultySystem_RecordLevelSuccess_UpdatesStats()
        {
            // Arrange
            _difficultySystem.RecordLevelFailure(); // 先记录一次失败
            
            // Act
            _difficultySystem.RecordLevelSuccess(10.5f);
            _difficultySystem.RecordLevelSuccess(12.0f);
            
            // Assert
            var stats = _difficultySystem.GetDifficultyStats();
            Assert.AreEqual(0, stats.currentLevelFailures, "当前关卡失败次数应该被重置");
            Assert.AreEqual(2, stats.consecutiveSuccesses, "连续成功次数应该是2");
            Assert.Greater(stats.averageCompletionTime, 0, "平均完成时间应该大于0");
        }
        
        [Test]
        public void DifficultySystem_UpdateDifficultyForLevel_ProgressiveMode()
        {
            // Arrange
            _difficultySystem.SetDifficultyMode(DifficultyMode.Progressive);
            
            // Act & Assert - 关卡0-2应该是简单难度
            _difficultySystem.UpdateDifficultyForLevel(0);
            Assert.AreEqual("简单", _difficultySystem.GetCurrentDifficulty().name);
            
            _difficultySystem.UpdateDifficultyForLevel(2);
            Assert.AreEqual("简单", _difficultySystem.GetCurrentDifficulty().name);
            
            // Act & Assert - 关卡3-5应该是普通难度
            _difficultySystem.UpdateDifficultyForLevel(3);
            Assert.AreEqual("普通", _difficultySystem.GetCurrentDifficulty().name);
            
            // Act & Assert - 关卡6-8应该是困难难度
            _difficultySystem.UpdateDifficultyForLevel(6);
            Assert.AreEqual("困难", _difficultySystem.GetCurrentDifficulty().name);
        }
        
        [Test]
        public void DifficultySystem_AdaptiveDifficulty_ReducesOnFailure()
        {
            // Arrange
            _difficultySystem.SetDifficultyMode(DifficultyMode.Adaptive);
            var initialStats = _difficultySystem.GetDifficultyStats();
            float initialMultiplier = initialStats.adaptiveDifficultyMultiplier;
            
            // Act - 记录多次失败
            for (int i = 0; i < 4; i++)
            {
                _difficultySystem.RecordLevelFailure();
            }
            
            // Assert
            var newStats = _difficultySystem.GetDifficultyStats();
            Assert.Less(newStats.adaptiveDifficultyMultiplier, initialMultiplier, 
                "多次失败后适应性难度倍数应该降低");
        }
        
        [Test]
        public void DifficultySystem_AdaptiveDifficulty_IncreasesOnSuccess()
        {
            // Arrange
            _difficultySystem.SetDifficultyMode(DifficultyMode.Adaptive);
            var initialStats = _difficultySystem.GetDifficultyStats();
            float initialMultiplier = initialStats.adaptiveDifficultyMultiplier;
            
            // Act - 记录多次成功
            _difficultySystem.RecordLevelSuccess(10.0f);
            _difficultySystem.RecordLevelSuccess(9.5f);
            _difficultySystem.RecordLevelSuccess(8.0f);
            
            // Assert
            var newStats = _difficultySystem.GetDifficultyStats();
            Assert.GreaterOrEqual(newStats.adaptiveDifficultyMultiplier, initialMultiplier, 
                "多次成功后适应性难度倍数应该保持或增加");
        }
        
        [Test]
        public void DifficultySystem_ResetDifficultyStats_ClearsAllStats()
        {
            // Arrange
            _difficultySystem.RecordLevelFailure();
            _difficultySystem.RecordLevelSuccess(15.0f);
            
            // Act
            _difficultySystem.ResetDifficultyStats();
            
            // Assert
            var stats = _difficultySystem.GetDifficultyStats();
            Assert.AreEqual(0, stats.currentLevelFailures, "当前关卡失败次数应该被重置");
            Assert.AreEqual(0, stats.consecutiveSuccesses, "连续成功次数应该被重置");
            Assert.AreEqual(1.0f, stats.adaptiveDifficultyMultiplier, "适应性难度倍数应该被重置为1.0");
            Assert.AreEqual(0f, stats.averageCompletionTime, "平均完成时间应该被重置");
        }
        
        [Test]
        public void DifficultySystem_SetAdaptiveDifficultyMultiplier_ClampsValue()
        {
            // Act - 测试超出范围的值
            _difficultySystem.SetAdaptiveDifficultyMultiplier(5.0f); // 超出最大值
            var stats1 = _difficultySystem.GetDifficultyStats();
            
            _difficultySystem.SetAdaptiveDifficultyMultiplier(0.1f); // 超出最小值
            var stats2 = _difficultySystem.GetDifficultyStats();
            
            // Assert
            Assert.LessOrEqual(stats1.adaptiveDifficultyMultiplier, 2.0f, "倍数应该被限制在最大值以内");
            Assert.GreaterOrEqual(stats2.adaptiveDifficultyMultiplier, 0.5f, "倍数应该被限制在最小值以上");
        }
        
        [Test]
        public void DifficultySystem_AddDifficultyLevel_IncreasesAvailableLevels()
        {
            // Arrange
            var initialCount = _difficultySystem.GetAvailableDifficultyLevels().Count;
            var customDifficulty = new DifficultyLevel
            {
                name = "自定义难度",
                levelIndex = 99,
                enemySpeedMultiplier = 1.5f
            };
            
            // Act
            _difficultySystem.AddDifficultyLevel(customDifficulty);
            
            // Assert
            var newCount = _difficultySystem.GetAvailableDifficultyLevels().Count;
            Assert.AreEqual(initialCount + 1, newCount, "添加难度等级后数量应该增加");
            
            var availableLevels = _difficultySystem.GetAvailableDifficultyLevels();
            Assert.IsTrue(availableLevels.Exists(d => d.name == "自定义难度"), "应该包含新添加的难度等级");
        }
        
        [Test]
        public void DifficultySystem_SetHintSystemEnabled_UpdatesSetting()
        {
            // Act
            _difficultySystem.SetHintSystemEnabled(false);
            
            // 记录失败来触发提示检查
            bool hintTriggered = false;
            _difficultySystem.OnHintTriggered += (hint) => hintTriggered = true;
            
            for (int i = 0; i < 5; i++)
            {
                _difficultySystem.RecordLevelFailure();
            }
            
            // Assert
            Assert.IsFalse(hintTriggered, "禁用提示系统后不应该触发提示");
        }
        
        [Test]
        public void HintSystem_InitialState_NotShowingHint()
        {
            // Assert
            Assert.IsFalse(_hintSystem.IsShowingHint(), "初始状态下不应该显示提示");
            Assert.AreEqual(0, _hintSystem.GetQueuedHintCount(), "初始状态下提示队列应该为空");
        }
        
        [Test]
        public void HintSystem_SetDefaultDisplayDuration_UpdatesDuration()
        {
            // Arrange
            float newDuration = 8.0f;
            
            // Act
            _hintSystem.SetDefaultDisplayDuration(newDuration);
            
            // Assert - 通过显示提示来验证（需要UI组件才能完全测试）
            // 这里只验证方法调用不会出错
            Assert.DoesNotThrow(() => _hintSystem.SetDefaultDisplayDuration(newDuration));
        }
        
        [Test]
        public void HintSystem_AddCustomHint_IncreasesAvailableHints()
        {
            // Arrange
            var customHint = new HintData
            {
                id = "custom_test_hint",
                text = "这是一个自定义测试提示",
                category = HintCategory.General,
                priority = HintPriority.Medium,
                displayDuration = 3.0f
            };
            
            // Act
            _hintSystem.AddCustomHint(customHint);
            
            // Assert - 验证提示被添加（通过尝试显示来验证）
            Assert.DoesNotThrow(() => _hintSystem.ShowHint("custom_test_hint"));
        }
        
        [Test]
        public void HintSystem_ClearHintQueue_EmptiesQueue()
        {
            // Act
            _hintSystem.ClearHintQueue();
            
            // Assert
            Assert.AreEqual(0, _hintSystem.GetQueuedHintCount(), "清空后提示队列应该为空");
        }
        
        [Test]
        public void DifficultyLevel_DefaultValues_AreValid()
        {
            // Arrange & Act
            var difficulty = new DifficultyLevel();
            
            // Assert
            Assert.AreEqual(1.0f, difficulty.enemySpeedMultiplier, "默认敌人速度倍数应该是1.0");
            Assert.AreEqual(1.0f, difficulty.enemyDamageMultiplier, "默认敌人伤害倍数应该是1.0");
            Assert.AreEqual(1.0f, difficulty.enemyHealthMultiplier, "默认敌人生命值倍数应该是1.0");
            Assert.AreEqual(1.0f, difficulty.collectibleSpawnRate, "默认收集品生成率应该是1.0");
            Assert.AreEqual(1.0f, difficulty.playerHealthMultiplier, "默认玩家生命值倍数应该是1.0");
            Assert.IsTrue(difficulty.enableHints, "默认应该启用提示");
            Assert.AreEqual(HintFrequency.Medium, difficulty.hintFrequency, "默认提示频率应该是中等");
        }
        
        [Test]
        public void HintData_DefaultValues_AreValid()
        {
            // Arrange & Act
            var hint = new HintData();
            
            // Assert
            Assert.AreEqual(HintCategory.General, hint.category, "默认提示类别应该是General");
            Assert.AreEqual(HintPriority.Medium, hint.priority, "默认提示优先级应该是Medium");
            Assert.AreEqual(5.0f, hint.displayDuration, "默认显示时间应该是5秒");
        }
    }
}