using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using MobileScrollingGame.Core;
using MobileScrollingGame.Mobile;
using MobileScrollingGame.UI;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 端到端游戏流程测试
    /// 测试完整的游戏流程，从启动到结束的所有环节
    /// </summary>
    public class EndToEndGameTests
    {
        private GameObject testGameObject;
        private GameManager gameManager;
        private ScoreManager scoreManager;
        private AudioManager audioManager;
        private UIManager uiManager;
        private MobileDeviceManager mobileDeviceManager;
        
        // 测试配置
        private const float TEST_TIMEOUT = 30f;
        private const int TEST_ITERATIONS = 3;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试环境
            SetupTestEnvironment();
        }
        
        [TearDown]
        public void TearDown()
        {
            CleanupTestEnvironment();
        }
        
        /// <summary>
        /// 设置测试环境
        /// </summary>
        private void SetupTestEnvironment()
        {
            testGameObject = new GameObject("EndToEndTestEnvironment");
            
            // 添加核心管理器
            gameManager = testGameObject.AddComponent<GameManager>();
            scoreManager = testGameObject.AddComponent<ScoreManager>();
            audioManager = testGameObject.AddComponent<AudioManager>();
            uiManager = testGameObject.AddComponent<UIManager>();
            mobileDeviceManager = testGameObject.AddComponent<MobileDeviceManager>();
            
            // 创建测试玩家
            CreateTestPlayer();
            
            // 创建测试UI
            CreateTestUI();
        }
        
        /// <summary>
        /// 创建测试玩家
        /// </summary>
        private void CreateTestPlayer()
        {
            GameObject player = new GameObject("TestPlayer");
            player.tag = "Player";
            player.transform.position = Vector3.zero;
            
            // 添加必要的组件
            var health = player.AddComponent<Health>();
            // 使用反射设置最大生命值
            var maxHealthField = typeof(Health).GetField("maxHealth", BindingFlags.NonPublic | BindingFlags.Instance);
            maxHealthField?.SetValue(health, 100);
            
            var rigidbody = player.AddComponent<Rigidbody2D>();
            var collider = player.AddComponent<BoxCollider2D>();
        }
        
        /// <summary>
        /// 创建测试UI
        /// </summary>
        private void CreateTestUI()
        {
            // 创建Canvas
            GameObject canvas = new GameObject("TestCanvas");
            var canvasComponent = canvas.AddComponent<Canvas>();
            canvasComponent.renderMode = RenderMode.ScreenSpaceOverlay;
            
            // 添加UI组件
            var gameHUD = canvas.AddComponent<GameHUD>();
            var pauseMenu = canvas.AddComponent<PauseMenu>();
            var gameOverScreen = canvas.AddComponent<GameOverScreen>();
        }
        
        /// <summary>
        /// 清理测试环境
        /// </summary>
        private void CleanupTestEnvironment()
        {
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
            
            // 清理所有测试对象
            GameObject[] testObjects = GameObject.FindGameObjectsWithTag("TestObject");
            foreach (GameObject obj in testObjects)
            {
                Object.DestroyImmediate(obj);
            }
        }
        
        #region 完整游戏流程测试
        
        [UnityTest]
        public IEnumerator EndToEnd_CompleteGameFlow_WorksCorrectly()
        {
            // 测试完整的游戏流程
            // 注：测试环境下直接 yield return 协程即可，无需调用 StartCoroutine（此类不是 MonoBehaviour）
            yield return TestGameStartup();
            yield return TestGameplay();
            yield return TestGamePause();
            yield return TestGameResume();
            yield return TestGameOver();
            yield return TestGameRestart();
        }
        
        /// <summary>
        /// 测试游戏启动
        /// </summary>
        private IEnumerator TestGameStartup()
        {
            Debug.Log("测试游戏启动流程...");
            
            // 验证初始状态
            Assert.IsFalse(gameManager.isGameStarted);
            Assert.AreEqual(0, scoreManager.CurrentScore);
            
            // 启动游戏
            gameManager.StartGame();
            
            yield return new WaitForSeconds(0.5f);
            
            // 验证游戏已启动
            Assert.IsTrue(gameManager.isGameStarted);
            Assert.IsFalse(gameManager.isGamePaused);
            
            Debug.Log("✓ 游戏启动测试通过");
        }
        
        /// <summary>
        /// 测试游戏玩法
        /// </summary>
        private IEnumerator TestGameplay()
        {
            Debug.Log("测试游戏玩法流程...");
            
            // 模拟游戏玩法
            yield return SimulateGameplay();
            
            // 验证分数系统
            Assert.Greater(scoreManager.CurrentScore, 0);
            
            Debug.Log("✓ 游戏玩法测试通过");
        }
        
        /// <summary>
        /// 模拟游戏玩法
        /// </summary>
        private IEnumerator SimulateGameplay()
        {
            // 模拟收集物品
            for (int i = 0; i < 5; i++)
            {
                scoreManager.AddScore(100);
                yield return new WaitForSeconds(0.1f);
            }
            
            // 模拟连击
            for (int i = 0; i < 3; i++)
            {
                scoreManager.AddCombo();
                yield return new WaitForSeconds(0.1f);
            }
            
            // 模拟收集品收集
            scoreManager.AddCollectible();
            
            yield return new WaitForSeconds(0.5f);
        }
        
        /// <summary>
        /// 测试游戏暂停
        /// </summary>
        private IEnumerator TestGamePause()
        {
            Debug.Log("测试游戏暂停流程...");
            
            // 暂停游戏
            gameManager.PauseGame();
            
            yield return new WaitForSeconds(0.1f);
            
            // 验证暂停状态
            Assert.IsTrue(gameManager.isGamePaused);
            Assert.AreEqual(0f, Time.timeScale);
            
            Debug.Log("✓ 游戏暂停测试通过");
        }
        
        /// <summary>
        /// 测试游戏恢复
        /// </summary>
        private IEnumerator TestGameResume()
        {
            Debug.Log("测试游戏恢复流程...");
            
            // 恢复游戏
            gameManager.ResumeGame();
            
            yield return new WaitForSeconds(0.1f);
            
            // 验证恢复状态
            Assert.IsFalse(gameManager.isGamePaused);
            Assert.AreEqual(1f, Time.timeScale);
            
            Debug.Log("✓ 游戏恢复测试通过");
        }
        
        /// <summary>
        /// 测试游戏结束
        /// </summary>
        private IEnumerator TestGameOver()
        {
            Debug.Log("测试游戏结束流程...");
            
            int finalScore = scoreManager.CurrentScore;
            
            // 触发游戏结束
            gameManager.GameOver();
            
            yield return new WaitForSeconds(0.5f);
            
            // 验证游戏结束状态
            Assert.IsFalse(gameManager.isGameStarted);
            Assert.IsFalse(gameManager.isGamePaused);
            
            // 验证最高分更新
            if (finalScore > scoreManager.HighScore)
            {
                Assert.AreEqual(finalScore, scoreManager.HighScore);
            }
            
            Debug.Log("✓ 游戏结束测试通过");
        }
        
        /// <summary>
        /// 测试游戏重启
        /// </summary>
        private IEnumerator TestGameRestart()
        {
            Debug.Log("测试游戏重启流程...");
            
            // 重启游戏
            gameManager.RestartGame();
            
            yield return new WaitForSeconds(0.5f);
            
            // 验证重启状态
            Assert.IsTrue(gameManager.isGameStarted);
            Assert.IsFalse(gameManager.isGamePaused);
            Assert.AreEqual(0, scoreManager.CurrentScore);
            
            Debug.Log("✓ 游戏重启测试通过");
        }
        
        #endregion
        
        #region 系统集成测试
        
        [UnityTest]
        public IEnumerator EndToEnd_SystemIntegration_WorksCorrectly()
        {
            // 测试各系统间的集成
            // 注：直接 yield return 子协程
            yield return TestScoreAudioIntegration();
            yield return TestUIGameManagerIntegration();
            yield return TestMobileDeviceIntegration();
        }
        
        /// <summary>
        /// 测试分数和音频系统集成
        /// </summary>
        private IEnumerator TestScoreAudioIntegration()
        {
            Debug.Log("测试分数-音频系统集成...");
            
            bool audioEventTriggered = false;
            
            // 监听音频事件
            if (AudioEventSystem.Instance != null)
            {
                AudioEventSystem.Instance.OnSoundEffectPlayed += (soundName) => {
                    audioEventTriggered = true;
                };
            }
            
            // 触发分数变化
            scoreManager.AddScore(100);
            
            yield return new WaitForSeconds(0.5f);
            
            // 验证音频事件被触发（如果音频系统可用）
            if (AudioEventSystem.Instance != null)
            {
                // 在实际环境中应该触发音频事件
                Debug.Log("分数-音频集成测试完成");
            }
            
            Debug.Log("✓ 分数-音频系统集成测试通过");
        }
        
        /// <summary>
        /// 测试UI和游戏管理器集成
        /// </summary>
        private IEnumerator TestUIGameManagerIntegration()
        {
            Debug.Log("测试UI-游戏管理器集成...");
            
            // 启动游戏
            gameManager.StartGame();
            yield return new WaitForSeconds(0.1f);
            
            // 验证UI状态更新
            if (uiManager != null)
            {
                // UI应该显示游戏HUD
                Assert.IsNotNull(uiManager);
            }
            
            // 暂停游戏
            gameManager.PauseGame();
            yield return new WaitForSeconds(0.1f);
            
            // 验证UI状态更新
            if (uiManager != null)
            {
                // UI应该显示暂停菜单
                Assert.IsNotNull(uiManager);
            }
            
            Debug.Log("✓ UI-游戏管理器集成测试通过");
        }
        
        /// <summary>
        /// 测试移动设备系统集成
        /// </summary>
        private IEnumerator TestMobileDeviceIntegration()
        {
            Debug.Log("测试移动设备系统集成...");
            
            // 测试设备性能评估
            DevicePerformanceLevel performanceLevel = mobileDeviceManager.GetDevicePerformanceLevel();
            Assert.IsTrue(performanceLevel == DevicePerformanceLevel.Low || 
                         performanceLevel == DevicePerformanceLevel.Medium || 
                         performanceLevel == DevicePerformanceLevel.High);
            
            // 测试内存信息获取
            MemoryInfo memoryInfo = mobileDeviceManager.GetMemoryInfo();
            Assert.Greater(memoryInfo.totalSystemMemory, 0);
            
            yield return new WaitForSeconds(0.1f);
            
            Debug.Log("✓ 移动设备系统集成测试通过");
        }
        
        #endregion
        
        #region 性能基准测试
        
        [UnityTest]
        public IEnumerator EndToEnd_PerformanceBenchmark_MeetsRequirements()
        {
            // 性能基准测试
            // 注：直接 yield return 子协程
            yield return TestFrameRatePerformance();
            yield return TestMemoryUsagePerformance();
            yield return TestLoadTimePerformance();
        }
        
        /// <summary>
        /// 测试帧率性能
        /// </summary>
        private IEnumerator TestFrameRatePerformance()
        {
            Debug.Log("测试帧率性能基准...");
            
            float startTime = Time.realtimeSinceStartup;
            int frameCount = 0;
            
            // 运行游戏一段时间并计算平均帧率
            while (Time.realtimeSinceStartup - startTime < 2f)
            {
                frameCount++;
                yield return null;
            }
            
            float duration = Time.realtimeSinceStartup - startTime;
            float averageFPS = frameCount / duration;
            
            // 验证帧率满足最低要求（至少20FPS）
            Assert.Greater(averageFPS, 20f, $"平均帧率过低: {averageFPS:F1} FPS");
            
            Debug.Log($"✓ 帧率性能测试通过: {averageFPS:F1} FPS");
        }
        
        /// <summary>
        /// 测试内存使用性能
        /// </summary>
        private IEnumerator TestMemoryUsagePerformance()
        {
            Debug.Log("测试内存使用性能基准...");
            
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 模拟游戏运行
            yield return SimulateIntensiveGameplay();
            
            long finalMemory = System.GC.GetTotalMemory(false);
            long memoryIncrease = finalMemory - initialMemory;
            
            // 验证内存增长在合理范围内（小于50MB）
            long maxMemoryIncrease = 50 * 1024 * 1024; // 50MB
            Assert.Less(memoryIncrease, maxMemoryIncrease, 
                $"内存使用增长过多: {memoryIncrease / (1024 * 1024)}MB");
            
            Debug.Log($"✓ 内存使用性能测试通过: +{memoryIncrease / (1024 * 1024)}MB");
        }
        
        /// <summary>
        /// 模拟密集游戏玩法
        /// </summary>
        private IEnumerator SimulateIntensiveGameplay()
        {
            // 快速添加分数和触发事件
            for (int i = 0; i < 100; i++)
            {
                scoreManager.AddScore(10);
                if (i % 10 == 0)
                {
                    scoreManager.AddCombo();
                }
                if (i % 20 == 0)
                {
                    scoreManager.AddCollectible();
                }
                
                if (i % 5 == 0)
                {
                    yield return null; // 每5次操作等待一帧
                }
            }
        }
        
        /// <summary>
        /// 测试加载时间性能
        /// </summary>
        private IEnumerator TestLoadTimePerformance()
        {
            Debug.Log("测试加载时间性能基准...");
            
            float startTime = Time.realtimeSinceStartup;
            
            // 模拟游戏初始化
            gameManager.StartGame();
            
            yield return new WaitForSeconds(0.1f);
            
            float loadTime = Time.realtimeSinceStartup - startTime;
            
            // 验证加载时间在合理范围内（小于5秒）
            Assert.Less(loadTime, 5f, $"游戏加载时间过长: {loadTime:F2}秒");
            
            Debug.Log($"✓ 加载时间性能测试通过: {loadTime:F2}秒");
        }
        
        #endregion
        
        #region 用户体验测试
        
        [UnityTest]
        public IEnumerator EndToEnd_UserExperience_MeetsStandards()
        {
            // 用户体验测试
            // 注：直接 yield return 子协程
            yield return TestResponseiveness();
            yield return TestVisualFeedback();
            yield return TestAudioFeedback();
        }
        
        /// <summary>
        /// 测试响应性
        /// </summary>
        private IEnumerator TestResponseiveness()
        {
            Debug.Log("测试用户界面响应性...");
            
            float startTime = Time.realtimeSinceStartup;
            
            // 模拟用户输入
            gameManager.PauseGame();
            
            float responseTime = Time.realtimeSinceStartup - startTime;
            
            // 验证响应时间在合理范围内（小于100ms）
            Assert.Less(responseTime, 0.1f, $"UI响应时间过长: {responseTime * 1000:F1}ms");
            
            yield return new WaitForSeconds(0.1f);
            
            Debug.Log($"✓ 响应性测试通过: {responseTime * 1000:F1}ms");
        }
        
        /// <summary>
        /// 测试视觉反馈
        /// </summary>
        private IEnumerator TestVisualFeedback()
        {
            Debug.Log("测试视觉反馈系统...");
            
            // 测试分数变化的视觉反馈
            int initialScore = scoreManager.CurrentScore;
            scoreManager.AddScore(100);
            
            yield return new WaitForSeconds(0.1f);
            
            // 验证分数已更新
            Assert.Greater(scoreManager.CurrentScore, initialScore);
            
            Debug.Log("✓ 视觉反馈测试通过");
        }
        
        /// <summary>
        /// 测试音频反馈
        /// </summary>
        private IEnumerator TestAudioFeedback()
        {
            Debug.Log("测试音频反馈系统...");
            
            // 测试音频系统是否可用
            if (audioManager != null)
            {
                Assert.IsNotNull(audioManager);
                
                // 测试音量设置
                audioManager.SetMasterVolume(0.5f);
                Assert.AreEqual(0.5f, audioManager.MasterVolume);
            }
            
            yield return new WaitForSeconds(0.1f);
            
            Debug.Log("✓ 音频反馈测试通过");
        }
        
        #endregion
        
        #region 多设备兼容性测试
        
        [Test]
        public void EndToEnd_DeviceCompatibility_SupportsMultipleDevices()
        {
            Debug.Log("测试多设备兼容性...");
            
            // 测试不同屏幕分辨率
            TestScreenResolutionCompatibility();
            
            // 测试不同性能等级
            TestPerformanceLevelCompatibility();
            
            // 测试移动平台特性
            TestMobilePlatformCompatibility();
            
            Debug.Log("✓ 多设备兼容性测试通过");
        }
        
        /// <summary>
        /// 测试屏幕分辨率兼容性
        /// </summary>
        private void TestScreenResolutionCompatibility()
        {
            // 模拟不同的屏幕分辨率
            int[] testWidths = { 1920, 1280, 1024, 800 };
            int[] testHeights = { 1080, 720, 768, 600 };
            
            for (int i = 0; i < testWidths.Length; i++)
            {
                int width = testWidths[i];
                int height = testHeights[i];
                
                // 在实际测试中，这里会设置屏幕分辨率
                // Screen.SetResolution(width, height, false);
                
                // 验证UI布局适应性
                Assert.Greater(width, 0);
                Assert.Greater(height, 0);
                
                Debug.Log($"分辨率 {width}x{height} 兼容性验证通过");
            }
        }
        
        /// <summary>
        /// 测试性能等级兼容性
        /// </summary>
        private void TestPerformanceLevelCompatibility()
        {
            // 测试不同性能等级的设置
            DevicePerformanceLevel[] levels = { 
                DevicePerformanceLevel.Low, 
                DevicePerformanceLevel.Medium, 
                DevicePerformanceLevel.High 
            };
            
            foreach (DevicePerformanceLevel level in levels)
            {
                // 根据性能等级调整设置
                int qualityLevel = level == DevicePerformanceLevel.Low ? 0 : 
                                  level == DevicePerformanceLevel.Medium ? 2 : 4;
                
                QualitySettings.SetQualityLevel(qualityLevel, true);
                
                Assert.AreEqual(qualityLevel, QualitySettings.GetQualityLevel());
                
                Debug.Log($"性能等级 {level} 兼容性验证通过");
            }
        }
        
        /// <summary>
        /// 测试移动平台兼容性
        /// </summary>
        private void TestMobilePlatformCompatibility()
        {
            // 测试移动平台特定功能
            if (Application.isMobilePlatform)
            {
                // 测试触摸输入（显式使用 UnityEngine.Input，避免与 MobileScrollingGame.Input 命名空间冲突）
                Assert.IsTrue(UnityEngine.Input.multiTouchEnabled);
                
                // 测试设备信息获取
                Assert.IsNotNull(SystemInfo.deviceModel);
                Assert.Greater(SystemInfo.systemMemorySize, 0);
                
                Debug.Log("移动平台特性兼容性验证通过");
            }
            else
            {
                Debug.Log("非移动平台，跳过移动特性测试");
            }
        }
        
        #endregion
        
        #region 压力测试
        
        [UnityTest]
        public IEnumerator EndToEnd_StressTest_HandlesHighLoad()
        {
            Debug.Log("开始压力测试...");

            // 注：直接 yield return 子协程
            yield return TestHighFrequencyOperations();
            yield return TestMemoryPressure();
            yield return TestConcurrentOperations();

            Debug.Log("✓ 压力测试完成");
        }
        
        /// <summary>
        /// 测试高频操作
        /// </summary>
        private IEnumerator TestHighFrequencyOperations()
        {
            Debug.Log("测试高频操作处理...");
            
            float startTime = Time.realtimeSinceStartup;
            
            // 执行大量高频操作
            for (int i = 0; i < 1000; i++)
            {
                scoreManager.AddScore(1);
                
                if (i % 100 == 0)
                {
                    yield return null;
                }
            }
            
            float duration = Time.realtimeSinceStartup - startTime;
            
            // 验证操作在合理时间内完成
            Assert.Less(duration, 5f, $"高频操作处理时间过长: {duration:F2}秒");
            
            Debug.Log($"✓ 高频操作测试通过: {duration:F2}秒");
        }
        
        /// <summary>
        /// 测试内存压力
        /// </summary>
        private IEnumerator TestMemoryPressure()
        {
            Debug.Log("测试内存压力处理...");
            
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 创建内存压力
            List<byte[]> memoryBlocks = new List<byte[]>();
            for (int i = 0; i < 10; i++)
            {
                memoryBlocks.Add(new byte[1024 * 1024]); // 1MB blocks
                yield return null;
            }
            
            // 清理内存
            memoryBlocks.Clear();
            System.GC.Collect();
            
            yield return new WaitForSeconds(0.5f);
            
            long finalMemory = System.GC.GetTotalMemory(false);
            long memoryDifference = finalMemory - initialMemory;
            
            // 验证内存得到适当清理
            Assert.Less(memoryDifference, 5 * 1024 * 1024, "内存清理不充分");
            
            Debug.Log($"✓ 内存压力测试通过: {memoryDifference / (1024 * 1024)}MB差异");
        }
        
        /// <summary>
        /// 测试并发操作
        /// </summary>
        private IEnumerator TestConcurrentOperations()
        {
            Debug.Log("测试并发操作处理...");
            
            // 同时执行多种操作
            // 注：使用 MonoBehaviour 宿主启动协程（本类不是 MonoBehaviour）
            if (gameManager != null)
            {
                gameManager.StartCoroutine(ConcurrentScoreOperations());
                gameManager.StartCoroutine(ConcurrentAudioOperations());
                gameManager.StartCoroutine(ConcurrentUIOperations());
            }

            yield return new WaitForSeconds(2f);
            
            // 验证系统仍然稳定
            Assert.IsNotNull(gameManager);
            Assert.IsNotNull(scoreManager);
            
            Debug.Log("✓ 并发操作测试通过");
        }
        
        /// <summary>
        /// 并发分数操作
        /// </summary>
        private IEnumerator ConcurrentScoreOperations()
        {
            for (int i = 0; i < 50; i++)
            {
                scoreManager.AddScore(10);
                yield return new WaitForSeconds(0.02f);
            }
        }
        
        /// <summary>
        /// 并发音频操作
        /// </summary>
        private IEnumerator ConcurrentAudioOperations()
        {
            for (int i = 0; i < 20; i++)
            {
                if (audioManager != null)
                {
                    audioManager.SetMasterVolume(Random.Range(0.5f, 1f));
                }
                yield return new WaitForSeconds(0.05f);
            }
        }
        
        /// <summary>
        /// 并发UI操作
        /// </summary>
        private IEnumerator ConcurrentUIOperations()
        {
            for (int i = 0; i < 10; i++)
            {
                gameManager.PauseGame();
                yield return new WaitForSeconds(0.05f);
                gameManager.ResumeGame();
                yield return new WaitForSeconds(0.05f);
            }
        }
        
        #endregion
    }
}
