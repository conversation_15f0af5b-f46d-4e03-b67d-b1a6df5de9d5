using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Enemy;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 敌人AI集成测试
    /// 测试敌人AI在不同场景下的完整行为
    /// </summary>
    public class EnemyAIIntegrationTests
    {
        private GameObject enemyObject;
        private EnemyController enemyController;
        private GameObject playerObject;
        private MockCharacterController mockPlayerController;

        [SetUp]
        public void SetUp()
        {
            // 创建敌人对象
            enemyObject = new GameObject("TestEnemy");
            enemyObject.AddComponent<Rigidbody2D>();
            enemyObject.AddComponent<BoxCollider2D>();
            enemyObject.AddComponent<SpriteRenderer>();
            enemyController = enemyObject.AddComponent<EnemyController>();

            // 创建玩家对象和模拟控制器
            playerObject = new GameObject("Player");
            playerObject.tag = "Player";
            mockPlayerController = playerObject.AddComponent<MockCharacterController>();

            // 设置测试数据
            var testData = new EnemyData
            {
                maxHealth = 50,
                currentHealth = 50,
                damage = 10,
                moveSpeed = 2f,
                chaseSpeed = 4f,
                patrolDistance = 5f,
                patrolWaitTime = 0.5f,
                detectionRange = 8f,
                attackRange = 1.5f,
                loseTargetDistance = 12f,
                attackCooldown = 1f,
                attackDuration = 0.3f
            };

            var dataField = typeof(EnemyController).GetField("enemyData",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            dataField?.SetValue(enemyController, testData);

            // 手动调用初始化方法而不是直接调用 Start()
            InitializeEnemyController(enemyController, enemyObject);
        }

        [TearDown]
        public void TearDown()
        {
            if (enemyObject != null)
                Object.DestroyImmediate(enemyObject);
            if (playerObject != null)
                Object.DestroyImmediate(playerObject);
        }

        /// <summary>
        /// 手动初始化敌人控制器（避免直接调用 Start() 方法）
        /// </summary>
        private void InitializeEnemyController(EnemyController controller, GameObject controllerObject)
        {
            // 使用反射调用受保护的初始化方法
            var initializeStateMachineMethod = typeof(EnemyController).GetMethod("InitializeStateMachine",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var findPlayerMethod = typeof(EnemyController).GetMethod("FindPlayer",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            initializeStateMachineMethod?.Invoke(controller, null);
            findPlayerMethod?.Invoke(controller, null);

            // 设置初始位置
            var initialPositionField = typeof(EnemyController).GetField("initialPosition",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            initialPositionField?.SetValue(controller, controllerObject.transform.position);
        }

        #region 完整AI行为测试

        [UnityTest]
        public IEnumerator EnemyAI_CompletePatrolCycle_WorksCorrectly()
        {
            // Arrange
            playerObject.transform.position = Vector3.right * 20f; // 远离玩家
            enemyObject.transform.position = Vector3.zero;
            Vector3 initialPosition = enemyObject.transform.position;

            // Act - 等待完整的巡逻周期
            yield return new WaitForSeconds(3f);

            // Assert
            // 敌人应该移动了一定距离然后返回
            Assert.AreEqual(EnemyStateType.Patrol, enemyController.StateMachine.CurrentStateType);
            // 验证敌人确实进行了移动
            Assert.IsTrue(Mathf.Abs(enemyObject.transform.position.x - initialPosition.x) >= 0.5f ||
                         enemyController.StateMachine.CurrentStateType == EnemyStateType.Patrol);
        }

        [UnityTest]
        public IEnumerator EnemyAI_DetectAndChasePlayer_WorksCorrectly()
        {
            // Arrange
            enemyObject.transform.position = Vector3.zero;
            playerObject.transform.position = Vector3.right * 6f; // 在检测范围内

            // Act
            yield return new WaitForSeconds(1f);

            // Assert
            Assert.AreEqual(EnemyStateType.Chase, enemyController.StateMachine.CurrentStateType);
            
            // 验证敌人向玩家方向移动
            Vector3 directionToPlayer = (playerObject.transform.position - enemyObject.transform.position).normalized;
            Assert.Greater(directionToPlayer.x, 0, "敌人应该向右追逐玩家");
        }

        [UnityTest]
        public IEnumerator EnemyAI_AttackPlayerInRange_WorksCorrectly()
        {
            // Arrange
            enemyObject.transform.position = Vector3.zero;
            playerObject.transform.position = Vector3.right * 1f; // 在攻击范围内
            bool attackReceived = false;
            mockPlayerController.OnDamageReceived += (damage) => attackReceived = true;

            // Act
            yield return new WaitForSeconds(1f);

            // Assert
            Assert.AreEqual(EnemyStateType.Attack, enemyController.StateMachine.CurrentStateType);
            Assert.IsTrue(attackReceived, "玩家应该受到攻击");
        }

        [UnityTest]
        public IEnumerator EnemyAI_LoseTargetAndReturnToPatrol_WorksCorrectly()
        {
            // Arrange
            enemyObject.transform.position = Vector3.zero;
            playerObject.transform.position = Vector3.right * 6f;
            
            // 等待敌人开始追逐
            yield return new WaitForSeconds(0.5f);
            Assert.AreEqual(EnemyStateType.Chase, enemyController.StateMachine.CurrentStateType);

            // Act - 将玩家移动到失去目标距离之外
            playerObject.transform.position = Vector3.right * 15f;
            yield return new WaitForSeconds(4f); // 等待失去目标时间

            // Assert
            Assert.AreEqual(EnemyStateType.Patrol, enemyController.StateMachine.CurrentStateType);
        }

        #endregion

        #region 战斗系统集成测试

        [UnityTest]
        public IEnumerator EnemyAI_TakeDamageAndRecover_WorksCorrectly()
        {
            // Arrange
            enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
            int initialHealth = enemyController.Data.currentHealth;

            // Act
            enemyController.TakeDamage(20);
            yield return new WaitForSeconds(0.1f);

            // Assert - 应该进入受伤状态
            Assert.AreEqual(EnemyStateType.Hurt, enemyController.StateMachine.CurrentStateType);
            Assert.AreEqual(initialHealth - 20, enemyController.Data.currentHealth);

            // Act - 等待受伤状态结束
            yield return new WaitForSeconds(0.6f);

            // Assert - 应该恢复到正常状态
            Assert.AreNotEqual(EnemyStateType.Hurt, enemyController.StateMachine.CurrentStateType);
        }

        [UnityTest]
        public IEnumerator EnemyAI_DeathSequence_WorksCorrectly()
        {
            // Arrange
            bool deathEventTriggered = false;
            enemyController.OnDeath += () => deathEventTriggered = true;

            // Act
            enemyController.TakeDamage(100); // 致命伤害
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.AreEqual(EnemyStateType.Death, enemyController.StateMachine.CurrentStateType);
            Assert.IsTrue(deathEventTriggered);
            Assert.IsFalse(enemyController.IsAlive);
            
            // 验证碰撞器被禁用
            Assert.IsFalse(enemyController.GetComponent<Collider2D>().enabled);
        }

        [UnityTest]
        public IEnumerator EnemyAI_AttackCooldownSystem_WorksCorrectly()
        {
            // Arrange
            enemyObject.transform.position = Vector3.zero;
            playerObject.transform.position = Vector3.right * 1f;
            int attackCount = 0;
            enemyController.OnAttackPerformed += () => attackCount++;

            // Act - 等待多次攻击
            yield return new WaitForSeconds(3f);

            // Assert - 应该有多次攻击，但受到冷却时间限制
            Assert.Greater(attackCount, 1, "应该执行多次攻击");
            Assert.Less(attackCount, 5, "攻击次数应该受到冷却时间限制");
        }

        #endregion

        #region 检测系统集成测试

        [Test]
        public void EnemyAI_PlayerDetection_WorksAtDifferentDistances()
        {
            // Test detection range
            playerObject.transform.position = Vector3.right * 7f;
            Assert.IsTrue(enemyController.IsPlayerInRange(enemyController.Data.detectionRange));
            Assert.IsFalse(enemyController.IsPlayerInRange(enemyController.Data.attackRange));

            // Test attack range
            playerObject.transform.position = Vector3.right * 1f;
            Assert.IsTrue(enemyController.IsPlayerInRange(enemyController.Data.attackRange));
        }

        [Test]
        public void EnemyAI_DirectionCalculation_IsAccurate()
        {
            // Arrange
            enemyObject.transform.position = Vector3.zero;
            
            // Test right direction
            playerObject.transform.position = Vector3.right * 5f;
            Vector2 rightDirection = enemyController.GetDirectionToPlayer();
            Assert.AreEqual(Vector2.right, rightDirection.normalized);

            // Test left direction
            playerObject.transform.position = Vector3.left * 5f;
            Vector2 leftDirection = enemyController.GetDirectionToPlayer();
            Assert.AreEqual(Vector2.left, leftDirection.normalized);
        }

        #endregion

        #region 状态持久性测试

        [UnityTest]
        public IEnumerator EnemyAI_StateTransitions_MaintainConsistency()
        {
            // Arrange
            var stateHistory = new System.Collections.Generic.List<EnemyStateType>();
            enemyController.StateMachine.OnStateChanged += (from, to) => stateHistory.Add(to);

            // Act - 模拟复杂的状态变化场景
            playerObject.transform.position = Vector3.right * 6f; // 触发追逐
            yield return new WaitForSeconds(0.5f);

            playerObject.transform.position = Vector3.right * 1f; // 触发攻击
            yield return new WaitForSeconds(0.5f);

            enemyController.TakeDamage(10); // 触发受伤
            yield return new WaitForSeconds(0.6f);

            playerObject.transform.position = Vector3.right * 20f; // 失去目标
            yield return new WaitForSeconds(1f);

            // Assert
            Assert.Contains(EnemyStateType.Chase, stateHistory);
            Assert.Contains(EnemyStateType.Attack, stateHistory);
            Assert.Contains(EnemyStateType.Hurt, stateHistory);
            
            // 最终应该回到巡逻状态
            Assert.AreEqual(EnemyStateType.Patrol, enemyController.StateMachine.CurrentStateType);
        }

        #endregion

        #region 性能测试

        [UnityTest]
        public IEnumerator EnemyAI_PerformanceUnderLoad_RemainsStable()
        {
            // Arrange
            var enemies = new System.Collections.Generic.List<EnemyController>();
            
            // 创建多个敌人
            for (int i = 0; i < 5; i++)
            {
                var enemyObj = new GameObject($"Enemy_{i}");
                enemyObj.AddComponent<Rigidbody2D>();
                enemyObj.AddComponent<BoxCollider2D>();
                enemyObj.AddComponent<SpriteRenderer>();
                var enemy = enemyObj.AddComponent<EnemyController>();
                
                var data = new EnemyData
                {
                    maxHealth = 50,
                    currentHealth = 50,
                    detectionRange = 8f,
                    attackRange = 1.5f
                };
                
                var dataField = typeof(EnemyController).GetField("enemyData",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                dataField?.SetValue(enemy, data);

                // 手动调用初始化方法而不是直接调用 Start()
                InitializeEnemyController(enemy, enemyObj);

                enemies.Add(enemy);
            }

            // Act - 运行一段时间
            float startTime = Time.realtimeSinceStartup;
            yield return new WaitForSeconds(2f);
            float endTime = Time.realtimeSinceStartup;

            // Assert
            float deltaTime = endTime - startTime;
            Assert.Less(deltaTime, 3f, "多个敌人AI应该能够高效运行");

            // 清理
            foreach (var enemy in enemies)
            {
                if (enemy != null)
                    Object.DestroyImmediate(enemy.gameObject);
            }
        }

        #endregion
    }

    /// <summary>
    /// 模拟角色控制器用于测试
    /// </summary>
    public class MockCharacterController : MonoBehaviour, ICharacterController
    {
        public int Health { get; private set; } = 100;
        public System.Action<int> OnDamageReceived;

        public void Move(Vector2 direction) { }
        public void Jump() { }
        public void PerformAction() { }
        
        public void TakeDamage(int damage)
        {
            Health = Mathf.Max(0, Health - damage);
            OnDamageReceived?.Invoke(damage);
        }
        
        public void SetAnimation(string animationName) { }
        public void SetIdleState() { }
        public Vector3 GetPosition() => transform.position;
        public bool IsGrounded() => true;
        public int GetHealth() => Health;
    }
}