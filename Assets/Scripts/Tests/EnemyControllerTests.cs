using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Enemy;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 敌人控制器测试
    /// </summary>
    public class EnemyControllerTests
    {
        private GameObject enemyObject;
        private EnemyController enemyController;
        private GameObject playerObject;
        private EnemyData testEnemyData;

        [SetUp]
        public void SetUp()
        {
            // 创建敌人对象
            enemyObject = new GameObject("TestEnemy");
            enemyObject.AddComponent<Rigidbody2D>();
            enemyObject.AddComponent<BoxCollider2D>();
            enemyObject.AddComponent<SpriteRenderer>();
            enemyController = enemyObject.AddComponent<EnemyController>();

            // 创建玩家对象
            playerObject = new GameObject("Player");
            playerObject.tag = "Player";
            playerObject.transform.position = Vector3.zero;

            // 设置测试数据
            testEnemyData = new EnemyData
            {
                maxHealth = 50,
                currentHealth = 50,
                damage = 10,
                moveSpeed = 2f,
                chaseSpeed = 4f,
                patrolDistance = 5f,
                detectionRange = 8f,
                attackRange = 1.5f,
                attackCooldown = 1.5f,
                attackDuration = 0.5f
            };

            // 通过反射设置私有字段
            var dataField = typeof(EnemyController).GetField("enemyData", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            dataField?.SetValue(enemyController, testEnemyData);
        }

        [TearDown]
        public void TearDown()
        {
            if (enemyObject != null)
                Object.DestroyImmediate(enemyObject);
            if (playerObject != null)
                Object.DestroyImmediate(playerObject);
        }

        /// <summary>
        /// 手动初始化敌人控制器（避免直接调用 Start() 方法）
        /// </summary>
        private void InitializeEnemyController()
        {
            // 使用反射调用受保护的初始化方法
            var initializeStateMachineMethod = typeof(EnemyController).GetMethod("InitializeStateMachine",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var findPlayerMethod = typeof(EnemyController).GetMethod("FindPlayer",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            initializeStateMachineMethod?.Invoke(enemyController, null);
            findPlayerMethod?.Invoke(enemyController, null);

            // 设置初始位置
            var initialPositionField = typeof(EnemyController).GetField("initialPosition",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            initialPositionField?.SetValue(enemyController, enemyObject.transform.position);
        }

        [Test]
        public void EnemyController_Initialization_SetsCorrectInitialState()
        {
            // Act
            InitializeEnemyController();

            // Assert
            Assert.IsNotNull(enemyController.StateMachine);
            Assert.AreEqual(EnemyStateType.Patrol, enemyController.StateMachine.CurrentStateType);
            Assert.IsTrue(enemyController.IsAlive);
        }

        [Test]
        public void EnemyData_Validation_ReturnsCorrectResult()
        {
            // Arrange
            var validData = new EnemyData
            {
                maxHealth = 50,
                damage = 10,
                moveSpeed = 2f,
                chaseSpeed = 4f,
                patrolDistance = 5f,
                detectionRange = 8f,
                attackRange = 1.5f,
                attackCooldown = 1.5f
            };

            var invalidData = new EnemyData
            {
                maxHealth = -10, // 无效值
                damage = 10,
                moveSpeed = 2f,
                chaseSpeed = 4f,
                patrolDistance = 5f,
                detectionRange = 8f,
                attackRange = 1.5f,
                attackCooldown = 1.5f
            };

            // Act & Assert
            Assert.IsTrue(validData.IsValid());
            Assert.IsFalse(invalidData.IsValid());
        }

        [Test]
        public void EnemyController_TakeDamage_ReducesHealth()
        {
            // Arrange
            InitializeEnemyController();
            int initialHealth = enemyController.Data.currentHealth;
            int damage = 20;

            // Act
            enemyController.TakeDamage(damage);

            // Assert
            Assert.AreEqual(initialHealth - damage, enemyController.Data.currentHealth);
        }

        [Test]
        public void EnemyController_TakeDamage_TriggersHurtState()
        {
            // Arrange
            InitializeEnemyController();

            // Act
            enemyController.TakeDamage(10);

            // Assert
            Assert.AreEqual(EnemyStateType.Hurt, enemyController.StateMachine.CurrentStateType);
        }

        [Test]
        public void EnemyController_TakeFatalDamage_TriggersDeathState()
        {
            // Arrange
            InitializeEnemyController();

            // Act
            enemyController.TakeDamage(100); // 超过最大生命值

            // Assert
            Assert.AreEqual(EnemyStateType.Death, enemyController.StateMachine.CurrentStateType);
            Assert.IsFalse(enemyController.IsAlive);
        }

        [Test]
        public void EnemyController_SetFacingDirection_UpdatesCorrectly()
        {
            // Arrange
            InitializeEnemyController();

            // Act
            enemyController.SetFacingDirection(false);

            // Assert
            Assert.IsFalse(enemyController.Data.facingRight);
        }

        [Test]
        public void EnemyController_CanAttack_RespectsAttackCooldown()
        {
            // Arrange
            InitializeEnemyController();

            // Act
            bool canAttackBefore = enemyController.CanAttack();
            enemyController.PerformAttack();
            bool canAttackAfter = enemyController.CanAttack();

            // Assert
            Assert.IsTrue(canAttackBefore);
            Assert.IsFalse(canAttackAfter);
        }

        [UnityTest]
        public IEnumerator EnemyController_AttackCooldown_ResetsAfterTime()
        {
            // Arrange
            InitializeEnemyController();
            enemyController.PerformAttack();

            // Act
            yield return new WaitForSeconds(testEnemyData.attackCooldown + 0.1f);

            // Assert
            Assert.IsTrue(enemyController.CanAttack());
        }

        [Test]
        public void EnemyController_IsPlayerInRange_DetectsCorrectly()
        {
            // Arrange
            InitializeEnemyController();
            playerObject.transform.position = enemyObject.transform.position + Vector3.right * 5f;

            // Act
            bool inDetectionRange = enemyController.IsPlayerInRange(testEnemyData.detectionRange);
            bool inAttackRange = enemyController.IsPlayerInRange(testEnemyData.attackRange);

            // Assert
            Assert.IsTrue(inDetectionRange);
            Assert.IsFalse(inAttackRange);
        }

        [Test]
        public void EnemyController_GetDirectionToPlayer_ReturnsCorrectDirection()
        {
            // Arrange
            InitializeEnemyController();
            enemyObject.transform.position = Vector3.zero;
            playerObject.transform.position = Vector3.right * 5f;

            // Act
            Vector2 direction = enemyController.GetDirectionToPlayer();

            // Assert
            Assert.AreEqual(Vector2.right, direction.normalized);
        }

        [Test]
        public void EnemyStateMachine_StateTransition_WorksCorrectly()
        {
            // Arrange
            InitializeEnemyController();
            var stateMachine = enemyController.StateMachine;

            // Act
            bool transitionResult = stateMachine.ChangeState(EnemyStateType.Chase);

            // Assert
            Assert.IsTrue(transitionResult);
            Assert.AreEqual(EnemyStateType.Chase, stateMachine.CurrentStateType);
        }

        [Test]
        public void EnemyStateMachine_InvalidTransition_ReturnsFalse()
        {
            // Arrange
            InitializeEnemyController();
            var stateMachine = enemyController.StateMachine;
            stateMachine.ChangeState(EnemyStateType.Death);

            // Act
            bool transitionResult = stateMachine.ChangeState(EnemyStateType.Patrol);

            // Assert
            Assert.IsFalse(transitionResult);
            Assert.AreEqual(EnemyStateType.Death, stateMachine.CurrentStateType);
        }

        [UnityTest]
        public IEnumerator EnemyPatrolState_MovementBehavior_WorksCorrectly()
        {
            // Arrange
            InitializeEnemyController();
            enemyController.StateMachine.ChangeState(EnemyStateType.Patrol);
            Vector3 initialPosition = enemyObject.transform.position;

            // Act
            yield return new WaitForSeconds(1f);

            // Assert
            // 敌人应该开始移动（位置发生变化）
            Assert.AreNotEqual(initialPosition, enemyObject.transform.position);
        }

        [UnityTest]
        public IEnumerator EnemyChaseState_ChasesPlayer_WhenInRange()
        {
            // Arrange
            InitializeEnemyController();
            playerObject.transform.position = enemyObject.transform.position + Vector3.right * 3f;
            enemyController.StateMachine.ChangeState(EnemyStateType.Chase);

            Vector3 initialPosition = enemyObject.transform.position;

            // Act
            yield return new WaitForSeconds(0.5f);

            // Assert
            // 敌人应该向玩家方向移动
            Vector3 currentPosition = enemyObject.transform.position;
            Assert.Greater(currentPosition.x, initialPosition.x);
        }

        [Test]
        public void EnemyHealthSystem_HealthPercentage_CalculatesCorrectly()
        {
            // Arrange
            var data = new EnemyData { maxHealth = 100, currentHealth = 75 };

            // Act
            float percentage = data.GetHealthPercentage();

            // Assert
            Assert.AreEqual(0.75f, percentage, 0.01f);
        }

        [Test]
        public void EnemyHealthSystem_IsAlive_ReturnsCorrectStatus()
        {
            // Arrange
            var aliveData = new EnemyData { currentHealth = 50 };
            var deadData = new EnemyData { currentHealth = 0 };

            // Act & Assert
            Assert.IsTrue(aliveData.IsAlive());
            Assert.IsFalse(deadData.IsAlive());
        }

        [Test]
        public void EnemyData_Reset_RestoresDefaultValues()
        {
            // Arrange
            var data = new EnemyData { maxHealth = 100, currentHealth = 50, facingRight = false };

            // Act
            data.Reset();

            // Assert
            Assert.AreEqual(data.maxHealth, data.currentHealth);
            Assert.IsTrue(data.facingRight);
            Assert.IsFalse(data.isGrounded);
        }
    }
}