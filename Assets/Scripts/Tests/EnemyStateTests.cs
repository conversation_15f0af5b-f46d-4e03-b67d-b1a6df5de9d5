using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Enemy;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 敌人状态测试
    /// </summary>
    public class EnemyStateTests
    {
        private GameObject enemyObject;
        private EnemyController enemyController;
        private GameObject playerObject;
        private EnemyStateMachine stateMachine;

        [SetUp]
        public void SetUp()
        {
            // 创建敌人对象
            enemyObject = new GameObject("TestEnemy");
            enemyObject.AddComponent<Rigidbody2D>();
            enemyObject.AddComponent<BoxCollider2D>();
            enemyObject.AddComponent<SpriteRenderer>();
            enemyController = enemyObject.AddComponent<EnemyController>();

            // 创建玩家对象
            playerObject = new GameObject("Player");
            playerObject.tag = "Player";
            playerObject.transform.position = Vector3.zero;

            // 设置测试数据
            var testData = new EnemyData
            {
                maxHealth = 50,
                currentHealth = 50,
                detectionRange = 8f,
                attackRange = 1.5f,
                patrolDistance = 5f,
                patrolWaitTime = 1f,
                moveSpeed = 2f,
                chaseSpeed = 4f
            };

            var dataField = typeof(EnemyController).GetField("enemyData",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            dataField?.SetValue(enemyController, testData);

            // 手动调用初始化方法而不是直接调用 Start()
            InitializeEnemyController();
            stateMachine = enemyController.StateMachine;
        }

        [TearDown]
        public void TearDown()
        {
            if (enemyObject != null)
                Object.DestroyImmediate(enemyObject);
            if (playerObject != null)
                Object.DestroyImmediate(playerObject);
        }

        /// <summary>
        /// 手动初始化敌人控制器（避免直接调用 Start() 方法）
        /// </summary>
        private void InitializeEnemyController()
        {
            // 使用反射调用受保护的初始化方法
            var initializeStateMachineMethod = typeof(EnemyController).GetMethod("InitializeStateMachine",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var findPlayerMethod = typeof(EnemyController).GetMethod("FindPlayer",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            initializeStateMachineMethod?.Invoke(enemyController, null);
            findPlayerMethod?.Invoke(enemyController, null);

            // 设置初始位置
            var initialPositionField = typeof(EnemyController).GetField("initialPosition",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            initialPositionField?.SetValue(enemyController, enemyObject.transform.position);
        }

        #region Idle State Tests

        [Test]
        public void EnemyIdleState_OnEnter_StopsMovement()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Idle);
            var idleState = stateMachine.GetState<EnemyIdleState>(EnemyStateType.Idle);

            // Act
            idleState.OnEnter();

            // Assert
            var rb = enemyController.GetComponent<Rigidbody2D>();
            Assert.That(rb.linearVelocity.x, Is.EqualTo(0f).Within(0.1f));
        }

        [UnityTest]
        public IEnumerator EnemyIdleState_TransitionsToPatrol_AfterIdleTime()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Idle);
            playerObject.transform.position = Vector3.right * 20f; // 远离玩家

            // Act
            yield return new WaitForSeconds(2.5f); // 等待超过最大待机时间

            // Assert
            Assert.AreEqual(EnemyStateType.Patrol, stateMachine.CurrentStateType);
        }

        [Test]
        public void EnemyIdleState_CanTransitionTo_ReturnsCorrectValues()
        {
            // Arrange
            var idleState = stateMachine.GetState<EnemyIdleState>(EnemyStateType.Idle);

            // Act & Assert
            Assert.IsTrue(idleState.CanTransitionTo(EnemyStateType.Death));
            Assert.IsTrue(idleState.CanTransitionTo(EnemyStateType.Hurt));
            Assert.IsTrue(idleState.CanTransitionTo(EnemyStateType.Patrol));
            Assert.IsTrue(idleState.CanTransitionTo(EnemyStateType.Chase));
            Assert.IsFalse(idleState.CanTransitionTo(EnemyStateType.Attack));
        }

        #endregion

        #region Patrol State Tests

        [Test]
        public void EnemyPatrolState_OnEnter_SetsPatrolTarget()
        {
            // Arrange & Act
            stateMachine.ChangeState(EnemyStateType.Patrol);

            // Assert
            Assert.AreEqual(EnemyStateType.Patrol, stateMachine.CurrentStateType);
        }

        [UnityTest]
        public IEnumerator EnemyPatrolState_MovesToTarget_AndWaits()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Patrol);
            Vector3 initialPosition = enemyObject.transform.position;
            playerObject.transform.position = Vector3.right * 20f; // 远离玩家

            // Act
            yield return new WaitForSeconds(1f);

            // Assert
            // 敌人应该开始移动
            Assert.That(enemyObject.transform.position.x, Is.Not.EqualTo(initialPosition.x).Within(0.1f));
        }

        [Test]
        public void EnemyPatrolState_CanTransitionTo_ReturnsCorrectValues()
        {
            // Arrange
            var patrolState = stateMachine.GetState<EnemyPatrolState>(EnemyStateType.Patrol);

            // Act & Assert
            Assert.IsTrue(patrolState.CanTransitionTo(EnemyStateType.Death));
            Assert.IsTrue(patrolState.CanTransitionTo(EnemyStateType.Hurt));
            Assert.IsTrue(patrolState.CanTransitionTo(EnemyStateType.Idle));
            Assert.IsTrue(patrolState.CanTransitionTo(EnemyStateType.Chase));
            Assert.IsFalse(patrolState.CanTransitionTo(EnemyStateType.Attack));
        }

        #endregion

        #region Chase State Tests

        [Test]
        public void EnemyChaseState_OnEnter_ResetsLoseTargetTimer()
        {
            // Arrange & Act
            stateMachine.ChangeState(EnemyStateType.Chase);

            // Assert
            Assert.AreEqual(EnemyStateType.Chase, stateMachine.CurrentStateType);
        }

        [UnityTest]
        public IEnumerator EnemyChaseState_ChasesPlayer_WhenInRange()
        {
            // Arrange
            playerObject.transform.position = enemyObject.transform.position + Vector3.right * 5f;
            stateMachine.ChangeState(EnemyStateType.Chase);
            Vector3 initialPosition = enemyObject.transform.position;

            // Act
            yield return new WaitForSeconds(0.5f);

            // Assert
            Vector3 currentPosition = enemyObject.transform.position;
            Assert.That(currentPosition.x, Is.GreaterThan(initialPosition.x), "敌人应该向右移动追逐玩家");
        }

        [UnityTest]
        public IEnumerator EnemyChaseState_TransitionsToAttack_WhenInAttackRange()
        {
            // Arrange
            playerObject.transform.position = enemyObject.transform.position + Vector3.right * 1f; // 在攻击范围内
            stateMachine.ChangeState(EnemyStateType.Chase);

            // Act
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.AreEqual(EnemyStateType.Attack, stateMachine.CurrentStateType);
        }

        [Test]
        public void EnemyChaseState_CanTransitionTo_ReturnsCorrectValues()
        {
            // Arrange
            var chaseState = stateMachine.GetState<EnemyChaseState>(EnemyStateType.Chase);

            // Act & Assert
            Assert.IsTrue(chaseState.CanTransitionTo(EnemyStateType.Death));
            Assert.IsTrue(chaseState.CanTransitionTo(EnemyStateType.Hurt));
            Assert.IsTrue(chaseState.CanTransitionTo(EnemyStateType.Attack));
            Assert.IsTrue(chaseState.CanTransitionTo(EnemyStateType.Patrol));
            Assert.IsTrue(chaseState.CanTransitionTo(EnemyStateType.Idle));
        }

        #endregion

        #region Attack State Tests

        [Test]
        public void EnemyAttackState_OnEnter_StopsMovement()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Attack);
            var attackState = stateMachine.GetState<EnemyAttackState>(EnemyStateType.Attack);

            // Act
            attackState.OnEnter();

            // Assert
            var rb = enemyController.GetComponent<Rigidbody2D>();
            Assert.That(rb.linearVelocity.x, Is.EqualTo(0f).Within(0.1f));
        }

        [UnityTest]
        public IEnumerator EnemyAttackState_PerformsAttack_AfterDelay()
        {
            // Arrange
            playerObject.transform.position = enemyObject.transform.position + Vector3.right * 1f;
            stateMachine.ChangeState(EnemyStateType.Attack);
            bool attackPerformed = false;
            enemyController.OnAttackPerformed += () => attackPerformed = true;

            // Act
            yield return new WaitForSeconds(0.3f); // 等待攻击延迟

            // Assert
            Assert.IsTrue(attackPerformed, "攻击应该被执行");
        }

        [Test]
        public void EnemyAttackState_CanTransitionTo_ReturnsCorrectValues()
        {
            // Arrange
            var attackState = stateMachine.GetState<EnemyAttackState>(EnemyStateType.Attack);

            // Act & Assert
            Assert.IsTrue(attackState.CanTransitionTo(EnemyStateType.Death));
            Assert.IsTrue(attackState.CanTransitionTo(EnemyStateType.Hurt));
            Assert.IsTrue(attackState.CanTransitionTo(EnemyStateType.Chase));
            Assert.IsTrue(attackState.CanTransitionTo(EnemyStateType.Patrol));
            Assert.IsTrue(attackState.CanTransitionTo(EnemyStateType.Attack));
        }

        #endregion

        #region Hurt State Tests

        [Test]
        public void EnemyHurtState_OnEnter_StopsMovementAndAppliesEffects()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Hurt);
            var hurtState = stateMachine.GetState<EnemyHurtState>(EnemyStateType.Hurt);

            // Act
            hurtState.OnEnter();

            // Assert
            var rb = enemyController.GetComponent<Rigidbody2D>();
            Assert.That(rb.linearVelocity.x, Is.EqualTo(0f).Within(0.1f));
            
            var spriteRenderer = enemyController.GetComponent<SpriteRenderer>();
            Assert.AreEqual(Color.red, spriteRenderer.color);
        }

        [UnityTest]
        public IEnumerator EnemyHurtState_TransitionsAfterDuration()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Hurt);
            playerObject.transform.position = Vector3.right * 20f; // 远离玩家

            // Act
            yield return new WaitForSeconds(0.6f); // 等待受伤持续时间

            // Assert
            Assert.AreNotEqual(EnemyStateType.Hurt, stateMachine.CurrentStateType);
        }

        [Test]
        public void EnemyHurtState_CanTransitionTo_ReturnsCorrectValues()
        {
            // Arrange
            var hurtState = stateMachine.GetState<EnemyHurtState>(EnemyStateType.Hurt);

            // Act & Assert
            Assert.IsTrue(hurtState.CanTransitionTo(EnemyStateType.Death));
            Assert.IsFalse(hurtState.CanTransitionTo(EnemyStateType.Hurt)); // 不能被其他受伤状态打断
        }

        #endregion

        #region Death State Tests

        [Test]
        public void EnemyDeathState_OnEnter_DisablesColliderAndStopsMovement()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Death);
            var deathState = stateMachine.GetState<EnemyDeathState>(EnemyStateType.Death);

            // Act
            deathState.OnEnter();

            // Assert
            var collider = enemyController.GetComponent<Collider2D>();
            var rb = enemyController.GetComponent<Rigidbody2D>();
            
            Assert.IsFalse(collider.enabled);
            Assert.AreEqual(RigidbodyType2D.Static, rb.bodyType);
        }

        [Test]
        public void EnemyDeathState_CanTransitionTo_ReturnsFalse()
        {
            // Arrange
            var deathState = stateMachine.GetState<EnemyDeathState>(EnemyStateType.Death);

            // Act & Assert
            Assert.IsFalse(deathState.CanTransitionTo(EnemyStateType.Idle));
            Assert.IsFalse(deathState.CanTransitionTo(EnemyStateType.Patrol));
            Assert.IsFalse(deathState.CanTransitionTo(EnemyStateType.Chase));
            Assert.IsFalse(deathState.CanTransitionTo(EnemyStateType.Attack));
            Assert.IsFalse(deathState.CanTransitionTo(EnemyStateType.Hurt));
        }

        #endregion

        #region State Machine Integration Tests

        [Test]
        public void EnemyStateMachine_StateTransitions_FollowCorrectFlow()
        {
            // Test: Idle -> Patrol -> Chase -> Attack -> Death
            
            // Idle to Patrol
            stateMachine.ChangeState(EnemyStateType.Idle);
            Assert.IsTrue(stateMachine.ChangeState(EnemyStateType.Patrol));
            
            // Patrol to Chase
            Assert.IsTrue(stateMachine.ChangeState(EnemyStateType.Chase));
            
            // Chase to Attack
            Assert.IsTrue(stateMachine.ChangeState(EnemyStateType.Attack));
            
            // Attack to Death
            Assert.IsTrue(stateMachine.ChangeState(EnemyStateType.Death));
            
            // Death cannot transition to anything
            Assert.IsFalse(stateMachine.ChangeState(EnemyStateType.Idle));
        }

        [Test]
        public void EnemyStateMachine_ForceChangeState_IgnoresTransitionRules()
        {
            // Arrange
            stateMachine.ChangeState(EnemyStateType.Death);

            // Act
            stateMachine.ForceChangeState(EnemyStateType.Idle);

            // Assert
            Assert.AreEqual(EnemyStateType.Idle, stateMachine.CurrentStateType);
        }

        [UnityTest]
        public IEnumerator EnemyStateMachine_StateChangeEvents_AreTriggered()
        {
            // Arrange
            bool eventTriggered = false;
            EnemyStateType fromState = EnemyStateType.Idle;
            EnemyStateType toState = EnemyStateType.Idle;
            
            stateMachine.OnStateChanged += (from, to) => 
            {
                eventTriggered = true;
                fromState = from;
                toState = to;
            };

            // Act
            stateMachine.ChangeState(EnemyStateType.Chase);
            yield return null;

            // Assert
            Assert.IsTrue(eventTriggered);
            Assert.AreEqual(EnemyStateType.Patrol, fromState); // 初始状态是Patrol
            Assert.AreEqual(EnemyStateType.Chase, toState);
        }

        #endregion
    }
}