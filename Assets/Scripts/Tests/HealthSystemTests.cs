using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 生命值系统测试
    /// </summary>
    public class HealthSystemTests
    {
        private GameObject healthObject;
        private Health healthComponent;
        private GameObject damageObject;
        private DamageTrigger damageTrigger;

        [SetUp]
        public void SetUp()
        {
            // 创建生命值对象
            healthObject = new GameObject("TestHealth");
            healthObject.AddComponent<SpriteRenderer>();
            healthObject.AddComponent<Rigidbody2D>();
            healthObject.AddComponent<BoxCollider2D>();
            healthComponent = healthObject.AddComponent<Health>();

            // 创建伤害触发器对象
            damageObject = new GameObject("TestDamageTrigger");
            damageObject.AddComponent<BoxCollider2D>();
            damageTrigger = damageObject.AddComponent<DamageTrigger>();
        }

        [TearDown]
        public void TearDown()
        {
            if (healthObject != null)
                Object.DestroyImmediate(healthObject);
            if (damageObject != null)
                Object.DestroyImmediate(damageObject);
        }

        #region Health Component Tests

        [Test]
        public void Health_Initialization_SetsCorrectDefaults()
        {
            // Assert
            Assert.AreEqual(100, healthComponent.MaxHealth);
            Assert.AreEqual(100, healthComponent.CurrentHealth);
            Assert.AreEqual(1f, healthComponent.HealthPercentage, 0.01f);
            Assert.IsTrue(healthComponent.IsAlive);
            Assert.IsFalse(healthComponent.IsDead);
            Assert.IsFalse(healthComponent.IsInvincible);
        }

        [Test]
        public void Health_TakeDamage_ReducesHealth()
        {
            // Arrange
            int initialHealth = healthComponent.CurrentHealth;
            int damage = 25;

            // Act
            bool damageDealt = healthComponent.TakeDamage(damage);

            // Assert
            Assert.IsTrue(damageDealt);
            Assert.AreEqual(initialHealth - damage, healthComponent.CurrentHealth);
            Assert.AreEqual(0.75f, healthComponent.HealthPercentage, 0.01f);
        }

        [Test]
        public void Health_TakeDamageWithDamageInfo_WorksCorrectly()
        {
            // Arrange
            var damageInfo = new DamageInfo(30, DamageType.Fire, Vector2.right * 5f, 0.5f, damageObject);

            // Act
            bool damageDealt = healthComponent.TakeDamage(damageInfo);

            // Assert
            Assert.IsTrue(damageDealt);
            Assert.AreEqual(70, healthComponent.CurrentHealth);
        }

        [Test]
        public void Health_TakeFatalDamage_TriggersDeath()
        {
            // Arrange
            bool deathTriggered = false;
            healthComponent.OnDeath += () => deathTriggered = true;

            // Act
            healthComponent.TakeDamage(150); // 超过最大生命值

            // Assert
            Assert.IsTrue(deathTriggered);
            Assert.AreEqual(0, healthComponent.CurrentHealth);
            Assert.IsTrue(healthComponent.IsDead);
            Assert.IsFalse(healthComponent.IsAlive);
        }

        [Test]
        public void Health_RestoreHealth_IncreasesHealth()
        {
            // Arrange
            healthComponent.TakeDamage(40); // 减少到60
            int healthBeforeRestore = healthComponent.CurrentHealth;

            // Act
            healthComponent.RestoreHealth(20);

            // Assert
            Assert.AreEqual(healthBeforeRestore + 20, healthComponent.CurrentHealth);
            Assert.AreEqual(0.8f, healthComponent.HealthPercentage, 0.01f);
        }

        [Test]
        public void Health_RestoreHealth_CannotExceedMaxHealth()
        {
            // Arrange
            healthComponent.TakeDamage(10); // 减少到90

            // Act
            healthComponent.RestoreHealth(50); // 尝试恢复50点

            // Assert
            Assert.AreEqual(100, healthComponent.CurrentHealth); // 应该被限制在最大值
            Assert.AreEqual(1f, healthComponent.HealthPercentage, 0.01f);
        }

        [Test]
        public void Health_ForceDeath_KillsImmediately()
        {
            // Arrange
            bool deathTriggered = false;
            healthComponent.OnDeath += () => deathTriggered = true;

            // Act
            healthComponent.ForceDeath();

            // Assert
            Assert.IsTrue(deathTriggered);
            Assert.AreEqual(0, healthComponent.CurrentHealth);
            Assert.IsTrue(healthComponent.IsDead);
        }

        [Test]
        public void Health_HealthChangedEvent_TriggersCorrectly()
        {
            // Arrange
            int newHealth = 0;
            int maxHealth = 0;
            healthComponent.OnHealthChanged += (current, max) => 
            {
                newHealth = current;
                maxHealth = max;
            };

            // Act
            healthComponent.TakeDamage(25);

            // Assert
            Assert.AreEqual(75, newHealth);
            Assert.AreEqual(100, maxHealth);
        }

        [UnityTest]
        public IEnumerator Health_InvincibilityFrames_PreventDamage()
        {
            // Arrange
            healthComponent.TakeDamage(10); // 触发无敌帧

            // Act
            yield return new WaitForSeconds(0.1f);
            bool secondDamageDealt = healthComponent.TakeDamage(10);

            // Assert
            Assert.IsFalse(secondDamageDealt);
            Assert.IsTrue(healthComponent.IsInvincible);
            Assert.AreEqual(90, healthComponent.CurrentHealth); // 只受到第一次伤害
        }

        [UnityTest]
        public IEnumerator Health_Respawn_RestoresHealthAndPosition()
        {
            // Arrange
            Vector3 respawnPos = new Vector3(5, 5, 0);
            healthComponent.SetRespawnPosition(respawnPos);
            
            bool respawnTriggered = false;
            healthComponent.OnRespawn += () => respawnTriggered = true;

            // Act
            healthComponent.TakeDamage(150); // 致命伤害
            yield return new WaitForSeconds(2.5f); // 等待重生

            // Assert
            Assert.IsTrue(respawnTriggered);
            Assert.IsFalse(healthComponent.IsDead);
            Assert.IsTrue(healthComponent.IsAlive);
            Assert.AreEqual(100, healthComponent.CurrentHealth);
            Assert.AreEqual(respawnPos, healthObject.transform.position);
        }

        #endregion

        #region DamageInfo Tests

        [Test]
        public void DamageInfo_Constructor_SetsCorrectValues()
        {
            // Arrange & Act
            var damageInfo = new DamageInfo(50, DamageType.Fire, Vector2.left * 3f, 2f, damageObject);

            // Assert
            Assert.AreEqual(50, damageInfo.amount);
            Assert.AreEqual(DamageType.Fire, damageInfo.type);
            Assert.AreEqual(Vector2.left * 3f, damageInfo.knockbackForce);
            Assert.AreEqual(2f, damageInfo.stunDuration);
            Assert.AreEqual(damageObject, damageInfo.source);
        }

        [Test]
        public void DamageInfo_DefaultConstructor_SetsDefaults()
        {
            // Arrange & Act
            var damageInfo = new DamageInfo(25);

            // Assert
            Assert.AreEqual(25, damageInfo.amount);
            Assert.AreEqual(DamageType.Physical, damageInfo.type);
            Assert.AreEqual(Vector2.zero, damageInfo.knockbackForce);
            Assert.AreEqual(0f, damageInfo.stunDuration);
            Assert.AreEqual(null, damageInfo.source);
        }

        #endregion

        #region DamageTrigger Tests

        [Test]
        public void DamageTrigger_Initialization_SetsCorrectDefaults()
        {
            // Assert
            Assert.IsNotNull(damageTrigger);
            
            var collider = damageObject.GetComponent<Collider2D>();
            Assert.IsTrue(collider.isTrigger);
        }

        [Test]
        public void DamageTrigger_SetActive_UpdatesState()
        {
            // Act
            damageTrigger.SetActive(false);

            // Assert
            var collider = damageObject.GetComponent<Collider2D>();
            Assert.IsFalse(collider.enabled);

            // Act
            damageTrigger.SetActive(true);

            // Assert
            Assert.IsTrue(collider.enabled);
        }

        #endregion

        #region Integration Tests

        [UnityTest]
        public IEnumerator HealthSystem_CompleteWorkflow_WorksCorrectly()
        {
            // Arrange
            bool healthChangedTriggered = false;
            bool damageTakenTriggered = false;
            bool deathTriggered = false;
            bool respawnTriggered = false;

            healthComponent.OnHealthChanged += (current, max) => healthChangedTriggered = true;
            healthComponent.OnDamageTaken += (damageInfo) => damageTakenTriggered = true;
            healthComponent.OnDeath += () => deathTriggered = true;
            healthComponent.OnRespawn += () => respawnTriggered = true;

            // Act - 完整的伤害、死亡、重生流程
            healthComponent.TakeDamage(50); // 受伤
            yield return new WaitForSeconds(0.1f);

            healthComponent.RestoreHealth(20); // 恢复
            yield return new WaitForSeconds(0.1f);

            healthComponent.TakeDamage(100); // 致命伤害
            yield return new WaitForSeconds(2.5f); // 等待重生

            // Assert
            Assert.IsTrue(healthChangedTriggered);
            Assert.IsTrue(damageTakenTriggered);
            Assert.IsTrue(deathTriggered);
            Assert.IsTrue(respawnTriggered);
            Assert.IsTrue(healthComponent.IsAlive);
            Assert.AreEqual(100, healthComponent.CurrentHealth);
        }

        [UnityTest]
        public IEnumerator HealthSystem_KnockbackEffect_WorksCorrectly()
        {
            // Arrange
            var rb = healthObject.GetComponent<Rigidbody2D>();
            Vector3 initialPosition = healthObject.transform.position;
            
            var damageInfo = new DamageInfo(10, DamageType.Physical, Vector2.right * 10f);

            // Act
            healthComponent.TakeDamage(damageInfo);
            yield return new WaitForSeconds(0.5f); // 等待物理效果

            // Assert
            // 由于击退效果，对象应该向右移动
            Assert.Greater(healthObject.transform.position.x, initialPosition.x);
        }

        #endregion
    }
}