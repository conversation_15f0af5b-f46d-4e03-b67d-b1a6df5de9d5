using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using MobileScrollingGame.Core;
using MobileScrollingGame.Mobile;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 集成测试运行器
    /// 统一运行所有集成测试并生成测试报告
    /// </summary>
    public class IntegrationTestRunner : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool runTestsOnStart = false;
        [SerializeField] private bool generateDetailedReport = true;
        [SerializeField] private bool enablePerformanceMetrics = true;
        [SerializeField] private float testTimeout = 60f;
        
        [Header("测试套件")]
        [SerializeField] private bool runEndToEndTests = true;
        [SerializeField] private bool runMultiDeviceTests = true;
        [SerializeField] private bool runPerformanceTests = true;
        [SerializeField] private bool runStressTests = true;
        
        /// <summary>
        /// 测试结果
        /// </summary>
        public class TestResult
        {
            public string testName;
            public bool passed;
            public float executionTime;
            public string errorMessage;
            public Dictionary<string, object> metrics;
            
            public TestResult(string name)
            {
                testName = name;
                passed = false;
                executionTime = 0f;
                errorMessage = "";
                metrics = new Dictionary<string, object>();
            }
        }
        
        /// <summary>
        /// 测试套件结果
        /// </summary>
        public class TestSuiteResult
        {
            public string suiteName;
            public List<TestResult> testResults;
            public int totalTests;
            public int passedTests;
            public int failedTests;
            public float totalExecutionTime;
            
            public TestSuiteResult(string name)
            {
                suiteName = name;
                testResults = new List<TestResult>();
                totalTests = 0;
                passedTests = 0;
                failedTests = 0;
                totalExecutionTime = 0f;
            }
            
            public float PassRate => totalTests > 0 ? (float)passedTests / totalTests : 0f;
        }
        
        // 测试结果存储
        private List<TestSuiteResult> testSuiteResults = new List<TestSuiteResult>();
        private TestResult currentTestResult;
        private float testStartTime;
        
        // 性能指标
        private Dictionary<string, float> performanceBaselines = new Dictionary<string, float>();
        
        // 事件
        public System.Action<TestResult> OnTestCompleted;
        public System.Action<TestSuiteResult> OnTestSuiteCompleted;
        public System.Action<List<TestSuiteResult>> OnAllTestsCompleted;
        
        private void Start()
        {
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllIntegrationTests());
            }
        }
        
        /// <summary>
        /// 运行所有集成测试
        /// </summary>
        [UnityTest]
        public IEnumerator RunAllIntegrationTests()
        {
            Debug.Log("开始运行集成测试套件...");
            
            testSuiteResults.Clear();
            InitializePerformanceBaselines();
            
            float totalStartTime = Time.realtimeSinceStartup;
            
            // 运行端到端测试
            if (runEndToEndTests)
            {
                yield return StartCoroutine(RunEndToEndTestSuite());
            }
            
            // 运行多设备兼容性测试
            if (runMultiDeviceTests)
            {
                yield return StartCoroutine(RunMultiDeviceTestSuite());
            }
            
            // 运行性能测试
            if (runPerformanceTests)
            {
                yield return StartCoroutine(RunPerformanceTestSuite());
            }
            
            // 运行压力测试
            if (runStressTests)
            {
                yield return StartCoroutine(RunStressTestSuite());
            }
            
            float totalExecutionTime = Time.realtimeSinceStartup - totalStartTime;
            
            // 生成最终报告
            GenerateFinalReport(totalExecutionTime);
            
            // 触发完成事件
            OnAllTestsCompleted?.Invoke(testSuiteResults);
            
            Debug.Log($"所有集成测试完成，总耗时: {totalExecutionTime:F2}秒");
        }
        
        /// <summary>
        /// 初始化性能基准
        /// </summary>
        private void InitializePerformanceBaselines()
        {
            performanceBaselines["MinFPS"] = 20f;
            performanceBaselines["MaxMemoryMB"] = 200f;
            performanceBaselines["MaxLoadTimeSeconds"] = 5f;
            performanceBaselines["MaxResponseTimeMS"] = 100f;
        }
        
        /// <summary>
        /// 运行端到端测试套件
        /// </summary>
        private IEnumerator RunEndToEndTestSuite()
        {
            TestSuiteResult suiteResult = new TestSuiteResult("端到端测试");
            
            Debug.Log("运行端到端测试套件...");
            
            // 完整游戏流程测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "完整游戏流程", TestCompleteGameFlow));
            
            // 系统集成测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "系统集成", TestSystemIntegration));
            
            // 用户体验测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "用户体验", TestUserExperience));
            
            testSuiteResults.Add(suiteResult);
            OnTestSuiteCompleted?.Invoke(suiteResult);
            
            Debug.Log($"端到端测试套件完成: {suiteResult.passedTests}/{suiteResult.totalTests} 通过");
        }
        
        /// <summary>
        /// 运行多设备测试套件
        /// </summary>
        private IEnumerator RunMultiDeviceTestSuite()
        {
            TestSuiteResult suiteResult = new TestSuiteResult("多设备兼容性测试");
            
            Debug.Log("运行多设备兼容性测试套件...");
            
            // 屏幕分辨率兼容性
            yield return StartCoroutine(RunSingleTest(suiteResult, "屏幕分辨率兼容性", TestScreenResolutionCompatibility));
            
            // 性能等级兼容性
            yield return StartCoroutine(RunSingleTest(suiteResult, "性能等级兼容性", TestPerformanceLevelCompatibility));
            
            // 输入系统兼容性
            yield return StartCoroutine(RunSingleTest(suiteResult, "输入系统兼容性", TestInputSystemCompatibility));
            
            testSuiteResults.Add(suiteResult);
            OnTestSuiteCompleted?.Invoke(suiteResult);
            
            Debug.Log($"多设备兼容性测试套件完成: {suiteResult.passedTests}/{suiteResult.totalTests} 通过");
        }
        
        /// <summary>
        /// 运行性能测试套件
        /// </summary>
        private IEnumerator RunPerformanceTestSuite()
        {
            TestSuiteResult suiteResult = new TestSuiteResult("性能测试");
            
            Debug.Log("运行性能测试套件...");
            
            // 帧率性能测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "帧率性能", TestFrameRatePerformance));
            
            // 内存使用测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "内存使用", TestMemoryUsage));
            
            // 加载时间测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "加载时间", TestLoadTime));
            
            testSuiteResults.Add(suiteResult);
            OnTestSuiteCompleted?.Invoke(suiteResult);
            
            Debug.Log($"性能测试套件完成: {suiteResult.passedTests}/{suiteResult.totalTests} 通过");
        }
        
        /// <summary>
        /// 运行压力测试套件
        /// </summary>
        private IEnumerator RunStressTestSuite()
        {
            TestSuiteResult suiteResult = new TestSuiteResult("压力测试");
            
            Debug.Log("运行压力测试套件...");
            
            // 高频操作测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "高频操作", TestHighFrequencyOperations));
            
            // 内存压力测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "内存压力", TestMemoryPressure));
            
            // 并发操作测试
            yield return StartCoroutine(RunSingleTest(suiteResult, "并发操作", TestConcurrentOperations));
            
            testSuiteResults.Add(suiteResult);
            OnTestSuiteCompleted?.Invoke(suiteResult);
            
            Debug.Log($"压力测试套件完成: {suiteResult.passedTests}/{suiteResult.totalTests} 通过");
        }
        
        /// <summary>
        /// 运行单个测试
        /// </summary>
        private IEnumerator RunSingleTest(TestSuiteResult suiteResult, string testName, System.Func<IEnumerator> testMethod)
        {
            currentTestResult = new TestResult(testName);
            testStartTime = Time.realtimeSinceStartup;
            
            Debug.Log($"运行测试: {testName}");
            
            // 运行测试方法（避免在带catch的try中yield：手动驱动枚举器）
            System.Exception caught = null;
            System.Collections.IEnumerator enumerator = null;

            // 调用测试方法本身可能抛出异常
            try
            {
                enumerator = testMethod?.Invoke();
            }
            catch (System.Exception e)
            {
                caught = e;
            }

            if (caught == null && enumerator != null)
            {
                while (true)
                {
                    object current = null;
                    try
                    {
                        if (!enumerator.MoveNext()) break;
                        current = enumerator.Current;
                    }
                    catch (System.Exception e)
                    {
                        caught = e;
                        break;
                    }

                    // 在try/catch之外进行yield，符合C#协程限制
                    yield return current;
                }

                // 清理枚举器
                (enumerator as System.IDisposable)?.Dispose();
            }

            if (caught == null)
            {
                // 测试成功
                currentTestResult.passed = true;
                currentTestResult.executionTime = Time.realtimeSinceStartup - testStartTime;
                suiteResult.passedTests++;
                Debug.Log($"✓ 测试通过: {testName} ({currentTestResult.executionTime:F2}秒)");
            }
            else
            {
                // 测试失败
                currentTestResult.passed = false;
                currentTestResult.executionTime = Time.realtimeSinceStartup - testStartTime;
                currentTestResult.errorMessage = caught.Message;
                suiteResult.failedTests++;
                Debug.LogError($"✗ 测试失败: {testName} - {caught.Message}");
            }
            
            // 添加到套件结果
            suiteResult.testResults.Add(currentTestResult);
            suiteResult.totalTests++;
            suiteResult.totalExecutionTime += currentTestResult.executionTime;
            
            // 触发测试完成事件
            OnTestCompleted?.Invoke(currentTestResult);
        }
        
        #region 具体测试方法
        
        /// <summary>
        /// 测试完整游戏流程
        /// </summary>
        private IEnumerator TestCompleteGameFlow()
        {
            // 创建测试环境
            GameObject testManager = new GameObject("TestGameManager");
            GameManager gameManager = testManager.AddComponent<GameManager>();
            
            // 测试游戏启动
            gameManager.StartGame();
            yield return new WaitForSeconds(0.5f);
            Assert.IsTrue(gameManager.isGameStarted, "游戏应该已启动");
            
            // 测试游戏暂停
            gameManager.PauseGame();
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(gameManager.isGamePaused, "游戏应该已暂停");
            
            // 测试游戏恢复
            gameManager.ResumeGame();
            yield return new WaitForSeconds(0.1f);
            Assert.IsFalse(gameManager.isGamePaused, "游戏应该已恢复");
            
            // 测试游戏结束
            gameManager.GameOver();
            yield return new WaitForSeconds(0.5f);
            Assert.IsFalse(gameManager.isGameStarted, "游戏应该已结束");
            
            // 清理
            Object.DestroyImmediate(testManager);
            
            // 记录性能指标
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["GameFlowSteps"] = 4;
                currentTestResult.metrics["AverageStepTime"] = currentTestResult.executionTime / 4f;
            }
        }
        
        /// <summary>
        /// 测试系统集成
        /// </summary>
        private IEnumerator TestSystemIntegration()
        {
            // 测试各系统是否正确初始化
            Assert.IsNotNull(GameManager.Instance, "GameManager应该存在");
            
            if (ScoreManager.Instance != null)
            {
                Assert.IsNotNull(ScoreManager.Instance, "ScoreManager应该存在");
            }
            
            if (AudioManager.Instance != null)
            {
                Assert.IsNotNull(AudioManager.Instance, "AudioManager应该存在");
            }
            
            yield return new WaitForSeconds(0.1f);
            
            // 记录集成系统数量
            if (enablePerformanceMetrics)
            {
                int systemCount = 0;
                if (GameManager.Instance != null) systemCount++;
                if (ScoreManager.Instance != null) systemCount++;
                if (AudioManager.Instance != null) systemCount++;
                
                currentTestResult.metrics["IntegratedSystems"] = systemCount;
            }
        }
        
        /// <summary>
        /// 测试用户体验
        /// </summary>
        private IEnumerator TestUserExperience()
        {
            float startTime = Time.realtimeSinceStartup;
            
            // 模拟用户操作
            yield return new WaitForSeconds(0.1f);
            
            float responseTime = Time.realtimeSinceStartup - startTime;
            
            // 验证响应时间
            Assert.Less(responseTime, performanceBaselines["MaxResponseTimeMS"] / 1000f, 
                "用户操作响应时间应在基准范围内");
            
            // 记录用户体验指标
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["ResponseTimeMS"] = responseTime * 1000f;
                currentTestResult.metrics["WithinBaseline"] = responseTime < performanceBaselines["MaxResponseTimeMS"] / 1000f;
            }
        }
        
        /// <summary>
        /// 测试屏幕分辨率兼容性
        /// </summary>
        private IEnumerator TestScreenResolutionCompatibility()
        {
            // 测试当前屏幕分辨率
            int screenWidth = Screen.width;
            int screenHeight = Screen.height;
            
            Assert.Greater(screenWidth, 0, "屏幕宽度应大于0");
            Assert.Greater(screenHeight, 0, "屏幕高度应大于0");
            
            float aspectRatio = (float)screenWidth / screenHeight;
            Assert.IsTrue(aspectRatio >= 0.5f && aspectRatio <= 3f, "屏幕比例应在支持范围内");
            
            yield return new WaitForSeconds(0.1f);
            
            // 记录屏幕信息
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["ScreenWidth"] = screenWidth;
                currentTestResult.metrics["ScreenHeight"] = screenHeight;
                currentTestResult.metrics["AspectRatio"] = aspectRatio;
            }
        }
        
        /// <summary>
        /// 测试性能等级兼容性
        /// </summary>
        private IEnumerator TestPerformanceLevelCompatibility()
        {
            // 获取设备性能信息
            int systemMemory = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;
            
            Assert.Greater(systemMemory, 0, "系统内存应大于0");
            Assert.Greater(processorCount, 0, "处理器核心数应大于0");
            
            yield return new WaitForSeconds(0.1f);
            
            // 记录设备性能信息
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["SystemMemoryMB"] = systemMemory;
                currentTestResult.metrics["ProcessorCount"] = processorCount;
            }
        }
        
        /// <summary>
        /// 测试输入系统兼容性
        /// </summary>
        private IEnumerator TestInputSystemCompatibility()
        {
            // 测试触摸输入支持
            // 显式使用 UnityEngine.Input，避免与项目自定义命名空间冲突
            bool touchSupported = UnityEngine.Input.touchSupported;
            bool multiTouchEnabled = UnityEngine.Input.multiTouchEnabled;
            
            yield return new WaitForSeconds(0.1f);
            
            // 记录输入系统信息
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["TouchSupported"] = touchSupported;
                currentTestResult.metrics["MultiTouchEnabled"] = multiTouchEnabled;
            }
        }
        
        /// <summary>
        /// 测试帧率性能
        /// </summary>
        private IEnumerator TestFrameRatePerformance()
        {
            float startTime = Time.realtimeSinceStartup;
            int frameCount = 0;
            
            // 运行2秒并计算平均帧率
            while (Time.realtimeSinceStartup - startTime < 2f)
            {
                frameCount++;
                yield return null;
            }
            
            float duration = Time.realtimeSinceStartup - startTime;
            float averageFPS = frameCount / duration;
            
            Assert.Greater(averageFPS, performanceBaselines["MinFPS"], 
                $"平均帧率应高于基准: {averageFPS:F1} > {performanceBaselines["MinFPS"]}");
            
            // 记录帧率信息
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["AverageFPS"] = averageFPS;
                currentTestResult.metrics["FrameCount"] = frameCount;
                currentTestResult.metrics["Duration"] = duration;
            }
        }
        
        /// <summary>
        /// 测试内存使用
        /// </summary>
        private IEnumerator TestMemoryUsage()
        {
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 模拟内存使用
            List<byte[]> memoryBlocks = new List<byte[]>();
            for (int i = 0; i < 10; i++)
            {
                memoryBlocks.Add(new byte[1024 * 1024]); // 1MB blocks
                yield return null;
            }
            
            long peakMemory = System.GC.GetTotalMemory(false);
            
            // 清理内存
            memoryBlocks.Clear();
            System.GC.Collect();
            yield return new WaitForSeconds(0.5f);
            
            long finalMemory = System.GC.GetTotalMemory(false);
            long memoryUsedMB = (peakMemory - initialMemory) / (1024 * 1024);
            
            Assert.Less(memoryUsedMB, performanceBaselines["MaxMemoryMB"], 
                $"内存使用应在基准范围内: {memoryUsedMB}MB < {performanceBaselines["MaxMemoryMB"]}MB");
            
            // 记录内存信息
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["InitialMemoryMB"] = initialMemory / (1024 * 1024);
                currentTestResult.metrics["PeakMemoryMB"] = peakMemory / (1024 * 1024);
                currentTestResult.metrics["FinalMemoryMB"] = finalMemory / (1024 * 1024);
                currentTestResult.metrics["MemoryUsedMB"] = memoryUsedMB;
            }
        }
        
        /// <summary>
        /// 测试加载时间
        /// </summary>
        private IEnumerator TestLoadTime()
        {
            float startTime = Time.realtimeSinceStartup;
            
            // 模拟加载过程
            yield return new WaitForSeconds(0.5f);
            
            float loadTime = Time.realtimeSinceStartup - startTime;
            
            Assert.Less(loadTime, performanceBaselines["MaxLoadTimeSeconds"], 
                $"加载时间应在基准范围内: {loadTime:F2}s < {performanceBaselines["MaxLoadTimeSeconds"]}s");
            
            // 记录加载时间
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["LoadTimeSeconds"] = loadTime;
            }
        }
        
        /// <summary>
        /// 测试高频操作
        /// </summary>
        private IEnumerator TestHighFrequencyOperations()
        {
            float startTime = Time.realtimeSinceStartup;
            
            // 执行1000次高频操作
            for (int i = 0; i < 1000; i++)
            {
                // 模拟高频操作
                Vector3.Distance(Vector3.zero, Vector3.one);
                
                if (i % 100 == 0)
                {
                    yield return null;
                }
            }
            
            float duration = Time.realtimeSinceStartup - startTime;
            
            Assert.Less(duration, 2f, "高频操作应在合理时间内完成");
            
            // 记录操作性能
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["OperationCount"] = 1000;
                currentTestResult.metrics["OperationDuration"] = duration;
                currentTestResult.metrics["OperationsPerSecond"] = 1000f / duration;
            }
        }
        
        /// <summary>
        /// 测试内存压力
        /// </summary>
        private IEnumerator TestMemoryPressure()
        {
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 创建内存压力
            List<GameObject> objects = new List<GameObject>();
            for (int i = 0; i < 100; i++)
            {
                GameObject obj = new GameObject($"TestObject_{i}");
                obj.AddComponent<MeshRenderer>();
                obj.AddComponent<BoxCollider>();
                objects.Add(obj);
                
                if (i % 10 == 0)
                {
                    yield return null;
                }
            }
            
            long peakMemory = System.GC.GetTotalMemory(false);
            
            // 清理对象
            foreach (GameObject obj in objects)
            {
                Object.DestroyImmediate(obj);
            }
            objects.Clear();
            
            System.GC.Collect();
            yield return new WaitForSeconds(0.5f);
            
            long finalMemory = System.GC.GetTotalMemory(false);
            long memoryIncrease = finalMemory - initialMemory;
            
            Assert.Less(memoryIncrease, 50 * 1024 * 1024, "内存泄漏应在可接受范围内");
            
            // 记录内存压力测试结果
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["ObjectsCreated"] = 100;
                currentTestResult.metrics["MemoryIncreaseMB"] = memoryIncrease / (1024 * 1024);
                currentTestResult.metrics["MemoryLeakMB"] = (finalMemory - initialMemory) / (1024 * 1024);
            }
        }
        
        /// <summary>
        /// 测试并发操作
        /// </summary>
        private IEnumerator TestConcurrentOperations()
        {
            // 启动多个并发协程
            Coroutine[] coroutines = new Coroutine[3];
            coroutines[0] = StartCoroutine(ConcurrentOperation1());
            coroutines[1] = StartCoroutine(ConcurrentOperation2());
            coroutines[2] = StartCoroutine(ConcurrentOperation3());
            
            // 等待所有协程完成
            yield return new WaitForSeconds(2f);
            
            // 停止所有协程
            foreach (var coroutine in coroutines)
            {
                if (coroutine != null)
                {
                    StopCoroutine(coroutine);
                }
            }
            
            // 记录并发操作结果
            if (enablePerformanceMetrics)
            {
                currentTestResult.metrics["ConcurrentOperations"] = 3;
                currentTestResult.metrics["ConcurrentDuration"] = 2f;
            }
        }
        
        private IEnumerator ConcurrentOperation1()
        {
            for (int i = 0; i < 50; i++)
            {
                yield return new WaitForSeconds(0.02f);
            }
        }
        
        private IEnumerator ConcurrentOperation2()
        {
            for (int i = 0; i < 100; i++)
            {
                yield return new WaitForSeconds(0.01f);
            }
        }
        
        private IEnumerator ConcurrentOperation3()
        {
            for (int i = 0; i < 25; i++)
            {
                yield return new WaitForSeconds(0.04f);
            }
        }
        
        #endregion
        
        /// <summary>
        /// 生成最终报告
        /// </summary>
        private void GenerateFinalReport(float totalExecutionTime)
        {
            if (!generateDetailedReport) return;
            
            Debug.Log("=== 集成测试最终报告 ===");
            Debug.Log($"总执行时间: {totalExecutionTime:F2}秒");
            Debug.Log($"测试套件数量: {testSuiteResults.Count}");
            
            int totalTests = testSuiteResults.Sum(s => s.totalTests);
            int totalPassed = testSuiteResults.Sum(s => s.passedTests);
            int totalFailed = testSuiteResults.Sum(s => s.failedTests);
            float overallPassRate = totalTests > 0 ? (float)totalPassed / totalTests : 0f;
            
            Debug.Log($"总测试数量: {totalTests}");
            Debug.Log($"通过: {totalPassed}");
            Debug.Log($"失败: {totalFailed}");
            Debug.Log($"总体通过率: {overallPassRate:P1}");
            
            Debug.Log("\n=== 各套件详情 ===");
            foreach (var suite in testSuiteResults)
            {
                Debug.Log($"{suite.suiteName}: {suite.passedTests}/{suite.totalTests} 通过 " +
                         $"({suite.PassRate:P1}), 耗时: {suite.totalExecutionTime:F2}秒");
                
                foreach (var test in suite.testResults)
                {
                    string status = test.passed ? "✓" : "✗";
                    Debug.Log($"  {status} {test.testName} ({test.executionTime:F2}秒)");
                    
                    if (!test.passed && !string.IsNullOrEmpty(test.errorMessage))
                    {
                        Debug.Log($"    错误: {test.errorMessage}");
                    }
                }
            }
            
            // 性能指标汇总
            if (enablePerformanceMetrics)
            {
                Debug.Log("\n=== 性能指标汇总 ===");
                foreach (var suite in testSuiteResults)
                {
                    foreach (var test in suite.testResults)
                    {
                        if (test.metrics.Count > 0)
                        {
                            Debug.Log($"{test.testName} 指标:");
                            foreach (var metric in test.metrics)
                            {
                                Debug.Log($"  {metric.Key}: {metric.Value}");
                            }
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// 获取测试结果摘要
        /// </summary>
        public string GetTestResultSummary()
        {
            if (testSuiteResults.Count == 0)
            {
                return "没有测试结果";
            }
            
            int totalTests = testSuiteResults.Sum(s => s.totalTests);
            int totalPassed = testSuiteResults.Sum(s => s.passedTests);
            float overallPassRate = totalTests > 0 ? (float)totalPassed / totalTests : 0f;
            
            return $"测试摘要: {totalPassed}/{totalTests} 通过 ({overallPassRate:P1})";
        }
        
        /// <summary>
        /// 导出测试报告
        /// </summary>
        public string ExportTestReport()
        {
            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            
            sb.AppendLine("=== 集成测试报告 ===");
            sb.AppendLine($"生成时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"Unity版本: {Application.unityVersion}");
            sb.AppendLine($"平台: {Application.platform}");
            sb.AppendLine();
            
            foreach (var suite in testSuiteResults)
            {
                sb.AppendLine($"=== {suite.suiteName} ===");
                sb.AppendLine($"通过率: {suite.PassRate:P1} ({suite.passedTests}/{suite.totalTests})");
                sb.AppendLine($"执行时间: {suite.totalExecutionTime:F2}秒");
                sb.AppendLine();
                
                foreach (var test in suite.testResults)
                {
                    sb.AppendLine($"测试: {test.testName}");
                    sb.AppendLine($"结果: {(test.passed ? "通过" : "失败")}");
                    sb.AppendLine($"执行时间: {test.executionTime:F2}秒");
                    
                    if (!test.passed)
                    {
                        sb.AppendLine($"错误: {test.errorMessage}");
                    }
                    
                    if (test.metrics.Count > 0)
                    {
                        sb.AppendLine("性能指标:");
                        foreach (var metric in test.metrics)
                        {
                            sb.AppendLine($"  {metric.Key}: {metric.Value}");
                        }
                    }
                    
                    sb.AppendLine();
                }
            }
            
            return sb.ToString();
        }
    }
}
