using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Level;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 关卡管理器的单元测试
    /// </summary>
    public class LevelManagerTests
    {
        private GameObject _levelManagerObject;
        private LevelManager _levelManager;
        private GameObject _playerObject;
        
        [SetUp]
        public void SetUp()
        {
            // 创建LevelManager对象
            _levelManagerObject = new GameObject("LevelManager");
            _levelManager = _levelManagerObject.AddComponent<LevelManager>();
            
            // 创建玩家对象
            _playerObject = new GameObject("Player");
            _playerObject.tag = "Player";
            _playerObject.AddComponent<Health>();
            
            // 清除PlayerPrefs以确保测试环境干净
            PlayerPrefs.DeleteKey("GameProgress");
        }
        
        [TearDown]
        public void TearDown()
        {
            if (_levelManagerObject != null)
                Object.DestroyImmediate(_levelManagerObject);
            
            if (_playerObject != null)
                Object.DestroyImmediate(_playerObject);
            
            // 清理PlayerPrefs
            PlayerPrefs.DeleteKey("GameProgress");
        }
        
        [Test]
        public void GetCurrentLevel_InitialState_ReturnsZero()
        {
            // Act
            int currentLevel = _levelManager.GetCurrentLevel();
            
            // Assert
            Assert.AreEqual(0, currentLevel, "初始关卡应该是0");
        }
        
        [Test]
        public void IsLevelCompleted_InitialState_ReturnsFalse()
        {
            // Act
            bool isCompleted = _levelManager.IsLevelCompleted(0);
            
            // Assert
            Assert.IsFalse(isCompleted, "初始状态下关卡不应该被标记为完成");
        }
        
        [Test]
        public void SaveCheckpoint_ValidPosition_SavesCorrectly()
        {
            // Arrange
            Vector3 checkpointPosition = new Vector3(10, 5, 0);
            
            // Act
            _levelManager.SaveCheckpoint(checkpointPosition);
            
            // Assert
            var gameProgress = _levelManager.GetGameProgress();
            Assert.AreEqual(checkpointPosition, gameProgress.lastCheckpoint, "检查点位置应该被正确保存");
            Assert.IsNotEmpty(gameProgress.lastCheckpointId, "检查点ID不应该为空");
        }
        
        [Test]
        public void RespawnAtCheckpoint_WithSavedCheckpoint_MovesPlayerToCheckpoint()
        {
            // Arrange
            Vector3 checkpointPosition = new Vector3(15, 8, 0);
            _levelManager.SaveCheckpoint(checkpointPosition);
            
            // Act
            _levelManager.RespawnAtCheckpoint();
            
            // Assert
            Assert.AreEqual(checkpointPosition, _playerObject.transform.position, "玩家应该被移动到检查点位置");
        }
        
        [Test]
        public void RespawnAtCheckpoint_ResetsPlayerHealth()
        {
            // Arrange
            var healthComponent = _playerObject.GetComponent<Health>();
            healthComponent.TakeDamage(50); // 造成一些伤害
            Vector3 checkpointPosition = new Vector3(20, 10, 0);
            _levelManager.SaveCheckpoint(checkpointPosition);
            
            // Act
            _levelManager.RespawnAtCheckpoint();
            
            // Assert
            Assert.AreEqual(healthComponent.MaxHealth, healthComponent.CurrentHealth, "重生时玩家生命值应该被重置");
        }
        
        [UnityTest]
        public IEnumerator LoadLevel_InvalidIndex_DoesNotLoad()
        {
            // Arrange
            int invalidIndex = 999;
            bool levelStarted = false;
            _levelManager.OnLevelStarted += (index) => levelStarted = true;
            
            // Act
            yield return _levelManager.LoadLevel(invalidIndex);
            
            // Assert
            Assert.IsFalse(levelStarted, "无效索引的关卡不应该被加载");
        }
        
        [Test]
        public void GetTotalLevels_InitialState_ReturnsZero()
        {
            // Act
            int totalLevels = _levelManager.GetTotalLevels();
            
            // Assert
            Assert.AreEqual(0, totalLevels, "初始状态下应该没有关卡");
        }
        
        [Test]
        public void AddLevelData_ValidData_AddsSuccessfully()
        {
            // Arrange
            var levelData = new LevelData
            {
                levelName = "测试关卡",
                levelIndex = 0,
                spawnPoint = Vector3.zero
            };
            
            // Act
            _levelManager.AddLevelData(levelData);
            
            // Assert
            Assert.AreEqual(1, _levelManager.GetTotalLevels(), "关卡数量应该增加");
            Assert.AreEqual(levelData, _levelManager.GetLevelData(0), "关卡数据应该被正确添加");
        }
        
        [Test]
        public void GetGameProgress_InitialState_ReturnsValidProgress()
        {
            // Act
            var gameProgress = _levelManager.GetGameProgress();
            
            // Assert
            Assert.IsNotNull(gameProgress, "游戏进度不应该为空");
            Assert.AreEqual(0, gameProgress.currentLevel, "初始关卡应该是0");
            Assert.AreEqual(0, gameProgress.highScore, "初始最高分应该是0");
        }
        
        [Test]
        public void ClearAllProgress_ResetsGameProgress()
        {
            // Arrange
            _levelManager.SaveCheckpoint(new Vector3(10, 10, 0));
            var gameProgress = _levelManager.GetGameProgress();
            gameProgress.highScore = 1000;
            
            // Act
            _levelManager.ClearAllProgress();
            
            // Assert
            var newProgress = _levelManager.GetGameProgress();
            Assert.AreEqual(Vector3.zero, newProgress.lastCheckpoint, "检查点应该被重置");
            Assert.AreEqual(0, newProgress.currentLevel, "当前关卡应该被重置");
            Assert.AreEqual(0, newProgress.currentScore, "当前分数应该被重置");
        }
        
        [Test]
        public void GameProgressData_CompleteLevel_UpdatesCorrectly()
        {
            // Arrange
            var gameProgress = new GameProgressData();
            
            // Act
            gameProgress.CompleteLevel(0);
            gameProgress.CompleteLevel(2); // 跳过关卡1
            
            // Assert
            Assert.IsTrue(gameProgress.IsLevelCompleted(0), "关卡0应该被标记为完成");
            Assert.IsFalse(gameProgress.IsLevelCompleted(1), "关卡1不应该被标记为完成");
            Assert.IsTrue(gameProgress.IsLevelCompleted(2), "关卡2应该被标记为完成");
            Assert.AreEqual(3, gameProgress.currentLevel, "当前关卡应该更新为3");
        }
        
        [Test]
        public void GameProgressData_UpdateHighScore_WorksCorrectly()
        {
            // Arrange
            var gameProgress = new GameProgressData();
            
            // Act
            gameProgress.UpdateHighScore(500);
            gameProgress.UpdateHighScore(300); // 较低的分数
            gameProgress.UpdateHighScore(800); // 更高的分数
            
            // Assert
            Assert.AreEqual(800, gameProgress.highScore, "最高分应该被正确更新");
        }
        
        [Test]
        public void GameProgressData_SaveCheckpoint_UpdatesCorrectly()
        {
            // Arrange
            var gameProgress = new GameProgressData();
            Vector3 position = new Vector3(25, 15, 0);
            string checkpointId = "test_checkpoint";
            int levelIndex = 1;
            
            // Act
            gameProgress.SaveCheckpoint(position, checkpointId, levelIndex);
            
            // Assert
            Assert.AreEqual(position, gameProgress.lastCheckpoint, "检查点位置应该被保存");
            Assert.AreEqual(checkpointId, gameProgress.lastCheckpointId, "检查点ID应该被保存");
            Assert.AreEqual(levelIndex, gameProgress.checkpointLevel, "检查点关卡应该被保存");
            Assert.IsNotEmpty(gameProgress.lastSaveTime, "保存时间应该被记录");
        }
        
        [Test]
        public void GameProgressData_ResetLevelProgress_KeepsOverallStats()
        {
            // Arrange
            var gameProgress = new GameProgressData();
            gameProgress.currentScore = 500;
            gameProgress.highScore = 1000;
            gameProgress.totalCollectibles = 10;
            gameProgress.SaveCheckpoint(new Vector3(10, 10, 0), "test", 1);
            
            // Act
            gameProgress.ResetLevelProgress();
            
            // Assert
            Assert.AreEqual(0, gameProgress.currentScore, "当前分数应该被重置");
            Assert.AreEqual(Vector3.zero, gameProgress.lastCheckpoint, "检查点应该被重置");
            Assert.AreEqual("", gameProgress.lastCheckpointId, "检查点ID应该被重置");
            Assert.AreEqual(1000, gameProgress.highScore, "最高分应该保持不变");
            Assert.AreEqual(10, gameProgress.totalCollectibles, "总收集品数应该保持不变");
        }
    }
}