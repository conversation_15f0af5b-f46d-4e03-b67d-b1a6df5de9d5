using UnityEngine;
using MobileScrollingGame.Camera;
using MobileScrollingGame.Player;
using MobileScrollingGame.Core;
using MobileScrollingGame.Input;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 手动测试指南 - 用于在Unity编辑器中测试任务1-4
    /// </summary>
    public class ManualTestingGuide : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private KeyCode testKey = KeyCode.T;
        
        [Header("任务1测试 - 项目结构")]
        [SerializeField] private bool testProjectStructure = true;
        
        [Header("任务2测试 - 输入系统")]
        [SerializeField] private bool testInputSystem = true;
        
        [Header("任务3测试 - 角色控制")]
        [SerializeField] private bool testCharacterControl = true;
        
        [Header("任务4测试 - 摄像机系统")]
        [SerializeField] private bool testCameraSystem = true;
        
        private void Update()
        {
            if (global::UnityEngine.Input.GetKeyDown(testKey)) // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            {
                RunAllTests();
            }
        }
        
        /// <summary>
        /// 运行所有手动测试
        /// </summary>
        [ContextMenu("运行所有测试")]
        public void RunAllTests()
        {
            Debug.Log("=== 开始手动测试任务1-4 ===");
            
            if (testProjectStructure) TestTask1_ProjectStructure();
            if (testInputSystem) TestTask2_InputSystem();
            if (testCharacterControl) TestTask3_CharacterControl();
            if (testCameraSystem) TestTask4_CameraSystem();
            
            Debug.Log("=== 手动测试完成 ===");
        }
        
        /// <summary>
        /// 测试任务1：项目结构和核心接口
        /// </summary>
        private void TestTask1_ProjectStructure()
        {
            Debug.Log("--- 测试任务1：项目结构 ---");
            
            // 检查GameManager
            var gameManager = FindFirstObjectByType<GameManager>();
            LogTestResult("GameManager存在", gameManager != null);
            
            // 检查核心接口实现
            var inputHandlers = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None).Length;
            LogTestResult("找到组件数量", inputHandlers > 0, $"组件数量: {inputHandlers}");
        }
        
        /// <summary>
        /// 测试任务2：输入系统
        /// </summary>
        private void TestTask2_InputSystem()
        {
            Debug.Log("--- 测试任务2：输入系统 ---");
            
            // 检查输入处理器
            var touchInput = FindFirstObjectByType<TouchInputHandler>();
            LogTestResult("TouchInputHandler存在", touchInput != null);
            
            // 测试输入响应
            bool hasInput = global::UnityEngine.Input.anyKey || global::UnityEngine.Input.touchCount > 0; // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            LogTestResult("输入检测", true, $"当前输入状态: {hasInput}");
            
            Debug.Log("手动测试：请在Game视图中测试虚拟按钮");
        }
        
        /// <summary>
        /// 测试任务3：角色控制系统
        /// </summary>
        private void TestTask3_CharacterControl()
        {
            Debug.Log("--- 测试任务3：角色控制 ---");
            
            var character = FindFirstObjectByType<MobileScrollingGame.Player.CharacterController>();
            LogTestResult("CharacterController存在", character != null);
            
            var collisionDetector = FindFirstObjectByType<CollisionDetector>();
            LogTestResult("CollisionDetector存在", collisionDetector != null);
            
            if (collisionDetector != null)
            {
                LogTestResult("地面检测", collisionDetector.IsGrounded);
                LogTestResult("墙壁检测", collisionDetector.IsAgainstWall);
                LogTestResult("障碍物检测", collisionDetector.IsNearObstacle);
            }
            
            var platformHandler = FindFirstObjectByType<PlatformCollisionHandler>();
            LogTestResult("PlatformCollisionHandler存在", platformHandler != null);
            
            if (platformHandler != null)
            {
                LogTestResult("平台检测", platformHandler.IsOnPlatform);
            }
        }
        
        /// <summary>
        /// 测试任务4：摄像机跟随系统
        /// </summary>
        private void TestTask4_CameraSystem()
        {
            Debug.Log("--- 测试任务4：摄像机系统 ---");
            
            var cameraFollower = FindFirstObjectByType<CameraFollower>();
            LogTestResult("CameraFollower存在", cameraFollower != null);
            
            var cameraBounds = FindFirstObjectByType<CameraBounds>();
            LogTestResult("CameraBounds存在", cameraBounds != null);
            
            var cameraShake = FindFirstObjectByType<CameraShake>();
            LogTestResult("CameraShake存在", cameraShake != null);
            
            if (cameraFollower != null)
            {
                Vector3 cameraPos = cameraFollower.GetCameraPosition();
                LogTestResult("摄像机位置获取", true, $"位置: {cameraPos}");
            }
            
            if (cameraShake != null)
            {
                LogTestResult("摄像机震动状态", cameraShake.IsShaking());
                
                // 测试震动效果
                Debug.Log("测试摄像机震动...");
                cameraShake.StartShake(1f, 0.5f);
            }
        }
        
        /// <summary>
        /// 记录测试结果
        /// </summary>
        private void LogTestResult(string testName, bool passed, string details = "")
        {
            if (!enableDebugLogs) return;
            
            string status = passed ? "✓ 通过" : "✗ 失败";
            string message = $"[测试] {testName}: {status}";
            
            if (!string.IsNullOrEmpty(details))
            {
                message += $" ({details})";
            }
            
            if (passed)
            {
                Debug.Log(message);
            }
            else
            {
                Debug.LogWarning(message);
            }
        }
        
        /// <summary>
        /// 创建测试场景
        /// </summary>
        [ContextMenu("创建测试场景")]
        public void CreateTestScene()
        {
            Debug.Log("创建测试场景...");
            
            // 创建地面
            CreateTestGround();
            
            // 创建平台
            CreateTestPlatform();
            
            // 创建墙壁
            CreateTestWall();
            
            // 创建障碍物
            CreateTestObstacle();
            
            Debug.Log("测试场景创建完成！");
        }
        
        private void CreateTestGround()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
            ground.name = "TestGround";
            ground.transform.position = new Vector3(0, -2, 0);
            ground.transform.localScale = new Vector3(20, 1, 1);
            ground.layer = 0; // Default layer
        }
        
        private void CreateTestPlatform()
        {
            GameObject platform = GameObject.CreatePrimitive(PrimitiveType.Cube);
            platform.name = "TestPlatform";
            platform.transform.position = new Vector3(5, 1, 0);
            platform.transform.localScale = new Vector3(4, 0.5f, 1);
            platform.layer = 8; // Platform layer
        }
        
        private void CreateTestWall()
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = "TestWall";
            wall.transform.position = new Vector3(10, 0, 0);
            wall.transform.localScale = new Vector3(1, 5, 1);
            wall.layer = 9; // Wall layer
        }
        
        private void CreateTestObstacle()
        {
            GameObject obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
            obstacle.name = "TestObstacle";
            obstacle.transform.position = new Vector3(-5, -1, 0);
            obstacle.transform.localScale = new Vector3(1, 1, 1);
            obstacle.layer = 10; // Obstacle layer
        }
    }
}