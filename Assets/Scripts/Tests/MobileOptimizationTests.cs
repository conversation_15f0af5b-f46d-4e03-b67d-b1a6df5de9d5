using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using MobileScrollingGame.Mobile;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 移动设备优化测试
    /// 测试移动设备管理器、性能监控、中断处理等功能
    /// </summary>
    public class MobileOptimizationTests
    {
        private GameObject testGameObject;
        private MobileDeviceManager mobileDeviceManager;
        private PerformanceMonitor performanceMonitor;
        private MobileInterruptHandler interruptHandler;
        private ObjectPool objectPool;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试游戏对象
            testGameObject = new GameObject("MobileOptimizationTest");
            
            // 添加移动设备管理器
            mobileDeviceManager = testGameObject.AddComponent<MobileDeviceManager>();
            
            // 添加性能监控器
            performanceMonitor = testGameObject.AddComponent<PerformanceMonitor>();
            
            // 添加中断处理器
            interruptHandler = testGameObject.AddComponent<MobileInterruptHandler>();
            
            // 添加对象池
            objectPool = testGameObject.AddComponent<ObjectPool>();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
        }
        
        #region MobileDeviceManager Tests
        
        [Test]
        public void MobileDeviceManager_InitializesCorrectly()
        {
            // 验证移动设备管理器初始化
            Assert.IsNotNull(mobileDeviceManager);
            Assert.IsFalse(mobileDeviceManager.IsApplicationPaused);
            Assert.IsTrue(mobileDeviceManager.IsApplicationFocused);
        }
        
        [Test]
        public void MobileDeviceManager_GetDevicePerformanceLevel_ReturnsValidLevel()
        {
            // 测试设备性能等级获取
            DevicePerformanceLevel level = mobileDeviceManager.GetDevicePerformanceLevel();
            
            Assert.IsTrue(level == DevicePerformanceLevel.Low || 
                         level == DevicePerformanceLevel.Medium || 
                         level == DevicePerformanceLevel.High);
        }
        
        [Test]
        public void MobileDeviceManager_GetMemoryInfo_ReturnsValidInfo()
        {
            // 测试内存信息获取
            MemoryInfo memoryInfo = mobileDeviceManager.GetMemoryInfo();
            
            Assert.Greater(memoryInfo.totalSystemMemory, 0);
            Assert.GreaterOrEqual(memoryInfo.usedMemory, 0);
            Assert.GreaterOrEqual(memoryInfo.availableMemory, 0);
        }
        
        [Test]
        public void MobileDeviceManager_SetTargetFrameRate_UpdatesCorrectly()
        {
            // 测试目标帧率设置
            int testFrameRate = 30;
            mobileDeviceManager.SetTargetFrameRate(testFrameRate);
            
            // 验证帧率设置（在非低电量模式下）
            if (!mobileDeviceManager.IsLowPowerModeActive)
            {
                Assert.AreEqual(testFrameRate, Application.targetFrameRate);
            }
        }
        
        [Test]
        public void MobileDeviceManager_ForceGarbageCollection_DoesNotThrowException()
        {
            // 测试强制垃圾回收
            Assert.DoesNotThrow(() => mobileDeviceManager.ForceGarbageCollection());
        }
        
        #endregion
        
        #region PerformanceMonitor Tests
        
        [Test]
        public void PerformanceMonitor_InitializesCorrectly()
        {
            // 验证性能监控器初始化
            Assert.IsNotNull(performanceMonitor);
            Assert.GreaterOrEqual(performanceMonitor.CurrentFPS, 0f);
        }
        
        [Test]
        public void PerformanceMonitor_GetCurrentPerformanceData_ReturnsValidData()
        {
            // 测试性能数据获取
            PerformanceMonitor.PerformanceData data = performanceMonitor.GetCurrentPerformanceData();
            
            Assert.GreaterOrEqual(data.fps, 0f);
            Assert.GreaterOrEqual(data.averageFPS, 0f);
            Assert.GreaterOrEqual(data.memoryUsage, 0f);
            Assert.LessOrEqual(data.memoryUsage, 1f);
            Assert.GreaterOrEqual(data.cpuUsage, 0f);
            Assert.LessOrEqual(data.cpuUsage, 1f);
            Assert.GreaterOrEqual(data.qualityLevel, 0);
        }
        
        [Test]
        public void PerformanceMonitor_SetQualityLevel_UpdatesCorrectly()
        {
            // 测试质量等级设置
            int testQualityLevel = 2;
            performanceMonitor.SetQualityLevel(testQualityLevel);
            
            Assert.AreEqual(testQualityLevel, performanceMonitor.CurrentQualityLevel);
            Assert.AreEqual(testQualityLevel, QualitySettings.GetQualityLevel());
        }
        
        [Test]
        public void PerformanceMonitor_ResetPerformanceHistory_ClearsHistory()
        {
            // 测试性能历史重置
            performanceMonitor.ResetPerformanceHistory();
            
            float[] fpsHistory = performanceMonitor.GetFPSHistory();
            float[] memoryHistory = performanceMonitor.GetMemoryHistory();
            float[] cpuHistory = performanceMonitor.GetCPUHistory();
            
            Assert.AreEqual(0, fpsHistory.Length);
            Assert.AreEqual(0, memoryHistory.Length);
            Assert.AreEqual(0, cpuHistory.Length);
        }
        
        [UnityTest]
        public IEnumerator PerformanceMonitor_MonitorsPerformanceOverTime()
        {
            // 测试性能监控随时间变化
            float initialFPS = performanceMonitor.CurrentFPS;
            
            // 等待几帧让性能监控器收集数据
            yield return new WaitForSeconds(2f);
            
            float updatedFPS = performanceMonitor.CurrentFPS;
            
            // 验证FPS数据已更新（可能相同，但不应该是负数）
            Assert.GreaterOrEqual(updatedFPS, 0f);
        }
        
        #endregion
        
        #region MobileInterruptHandler Tests
        
        [Test]
        public void MobileInterruptHandler_InitializesCorrectly()
        {
            // 验证中断处理器初始化
            Assert.IsNotNull(interruptHandler);
            Assert.IsFalse(interruptHandler.IsInterrupted);
            Assert.AreEqual(MobileInterruptHandler.InterruptType.None, interruptHandler.CurrentInterruptType);
        }
        
        [Test]
        public void MobileInterruptHandler_HandleInterrupt_UpdatesState()
        {
            // 测试中断处理
            MobileInterruptHandler.InterruptType testInterrupt = MobileInterruptHandler.InterruptType.Notification;
            
            interruptHandler.HandleInterrupt(testInterrupt);
            
            Assert.IsTrue(interruptHandler.IsInterrupted);
            Assert.AreEqual(testInterrupt, interruptHandler.CurrentInterruptType);
        }
        
        [Test]
        public void MobileInterruptHandler_ResolveInterrupt_UpdatesState()
        {
            // 测试中断解决
            MobileInterruptHandler.InterruptType testInterrupt = MobileInterruptHandler.InterruptType.Notification;
            
            // 先触发中断
            interruptHandler.HandleInterrupt(testInterrupt);
            Assert.IsTrue(interruptHandler.IsInterrupted);
            
            // 然后解决中断
            interruptHandler.ResolveInterrupt(testInterrupt);
            Assert.IsFalse(interruptHandler.IsInterrupted);
            Assert.AreEqual(MobileInterruptHandler.InterruptType.None, interruptHandler.CurrentInterruptType);
        }
        
        [Test]
        public void MobileInterruptHandler_SaveGameState_CreatesValidState()
        {
            // 测试游戏状态保存
            interruptHandler.SaveGameState();
            
            MobileInterruptHandler.GameStateData savedState = interruptHandler.SavedGameState;
            
            Assert.IsNotNull(savedState);
            Assert.IsNotNull(savedState.saveTime);
            Assert.GreaterOrEqual(savedState.gameTime, 0f);
            Assert.IsFalse(interruptHandler.HasUnsavedChanges);
        }
        
        [Test]
        public void MobileInterruptHandler_MarkUnsavedChanges_UpdatesFlag()
        {
            // 测试未保存更改标记
            interruptHandler.MarkUnsavedChanges();
            
            Assert.IsTrue(interruptHandler.HasUnsavedChanges);
        }
        
        [Test]
        public void MobileInterruptHandler_ClearSavedGameState_ClearsState()
        {
            // 先保存状态
            interruptHandler.SaveGameState();
            Assert.IsNotNull(interruptHandler.SavedGameState);
            
            // 然后清除状态
            interruptHandler.ClearSavedGameState();
            Assert.IsNull(interruptHandler.SavedGameState);
            Assert.IsFalse(interruptHandler.HasUnsavedChanges);
        }
        
        #endregion
        
        #region ObjectPool Tests
        
        [Test]
        public void ObjectPool_InitializesCorrectly()
        {
            // 验证对象池初始化
            Assert.IsNotNull(objectPool);
        }
        
        [Test]
        public void ObjectPool_CreatePool_DoesNotThrowException()
        {
            // 测试创建对象池
            ObjectPool.PoolConfig config = new ObjectPool.PoolConfig
            {
                poolName = "TestPool",
                prefab = new GameObject("TestPrefab"),
                initialSize = 5,
                maxSize = 10
            };
            
            Assert.DoesNotThrow(() => objectPool.CreatePool(config));
        }
        
        [Test]
        public void ObjectPool_GetObject_WithInvalidPool_ReturnsNull()
        {
            // 测试从不存在的池获取对象
            GameObject obj = objectPool.GetObject("NonExistentPool");
            
            Assert.IsNull(obj);
        }
        
        [Test]
        public void ObjectPool_GetAndReturnObject_WorksCorrectly()
        {
            // 创建测试池
            GameObject testPrefab = new GameObject("TestPrefab");
            ObjectPool.PoolConfig config = new ObjectPool.PoolConfig
            {
                poolName = "TestPool",
                prefab = testPrefab,
                initialSize = 1,
                maxSize = 5
            };
            
            objectPool.CreatePool(config);
            
            // 获取对象
            GameObject obj = objectPool.GetObject("TestPool");
            Assert.IsNotNull(obj);
            Assert.IsTrue(obj.activeInHierarchy);
            
            // 返回对象
            objectPool.ReturnObject(obj);
            Assert.IsFalse(obj.activeInHierarchy);
        }
        
        [Test]
        public void ObjectPool_GetPoolStatistics_ReturnsValidData()
        {
            // 创建测试池
            GameObject testPrefab = new GameObject("TestPrefab");
            ObjectPool.PoolConfig config = new ObjectPool.PoolConfig
            {
                poolName = "TestPool",
                prefab = testPrefab,
                initialSize = 2,
                maxSize = 5
            };
            
            objectPool.CreatePool(config);
            
            // 获取统计信息
            ObjectPool.PoolStatistics stats = objectPool.GetPoolStatistics("TestPool");
            
            Assert.IsNotNull(stats);
            Assert.GreaterOrEqual(stats.totalCreated, 0);
            Assert.GreaterOrEqual(stats.currentInactive, 0);
        }
        
        [Test]
        public void ObjectPool_ClearPool_ClearsCorrectly()
        {
            // 创建测试池
            GameObject testPrefab = new GameObject("TestPrefab");
            ObjectPool.PoolConfig config = new ObjectPool.PoolConfig
            {
                poolName = "TestPool",
                prefab = testPrefab,
                initialSize = 2,
                maxSize = 5
            };
            
            objectPool.CreatePool(config);
            
            // 清空池
            Assert.DoesNotThrow(() => objectPool.ClearPool("TestPool"));
        }
        
        [Test]
        public void ObjectPool_WarmupPool_DoesNotThrowException()
        {
            // 创建测试池
            GameObject testPrefab = new GameObject("TestPrefab");
            ObjectPool.PoolConfig config = new ObjectPool.PoolConfig
            {
                poolName = "TestPool",
                prefab = testPrefab,
                initialSize = 1,
                maxSize = 10
            };
            
            objectPool.CreatePool(config);
            
            // 预热池
            Assert.DoesNotThrow(() => objectPool.WarmupPool("TestPool", 3));
        }
        
        #endregion
        
        #region Integration Tests
        
        [UnityTest]
        public IEnumerator MobileOptimization_Integration_WorksCorrectly()
        {
            // 集成测试：移动优化系统协同工作
            
            // 触发中断
            interruptHandler.HandleInterrupt(MobileInterruptHandler.InterruptType.LowMemory);
            
            // 等待系统响应
            yield return new WaitForSeconds(0.5f);
            
            // 验证中断状态
            Assert.IsTrue(interruptHandler.IsInterrupted);
            
            // 解决中断
            interruptHandler.ResolveInterrupt(MobileInterruptHandler.InterruptType.LowMemory);
            
            // 验证中断已解决
            Assert.IsFalse(interruptHandler.IsInterrupted);
        }
        
        [Test]
        public void MobileOptimization_PerformanceAndMemory_Integration()
        {
            // 集成测试：性能监控和内存管理
            
            // 获取初始性能数据
            PerformanceMonitor.PerformanceData initialData = performanceMonitor.GetCurrentPerformanceData();
            
            // 强制垃圾回收
            mobileDeviceManager.ForceGarbageCollection();
            
            // 获取内存信息
            MemoryInfo memoryInfo = mobileDeviceManager.GetMemoryInfo();
            
            // 验证数据有效性
            Assert.GreaterOrEqual(initialData.fps, 0f);
            Assert.Greater(memoryInfo.totalSystemMemory, 0);
        }
        
        #endregion
        
        #region Performance Tests
        
        [UnityTest]
        public IEnumerator MobileOptimization_Performance_HandlesMultipleOperations()
        {
            // 性能测试：同时处理多个操作
            
            float startTime = Time.realtimeSinceStartup;
            
            // 执行多个操作
            for (int i = 0; i < 10; i++)
            {
                mobileDeviceManager.GetMemoryInfo();
                performanceMonitor.GetCurrentPerformanceData();
                interruptHandler.MarkUnsavedChanges();
            }
            
            yield return null;
            
            float endTime = Time.realtimeSinceStartup;
            float duration = endTime - startTime;
            
            // 验证性能（应该在合理时间内完成）
            Assert.Less(duration, 0.1f, "移动优化系统处理多个操作的时间过长");
        }
        
        [Test]
        public void MobileOptimization_MemoryUsage_StaysWithinLimits()
        {
            // 内存使用测试
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 执行一些操作
            for (int i = 0; i < 100; i++)
            {
                mobileDeviceManager.GetMemoryInfo();
                performanceMonitor.GetCurrentPerformanceData();
            }
            
            long finalMemory = System.GC.GetTotalMemory(false);
            long memoryIncrease = finalMemory - initialMemory;
            
            // 验证内存增长在合理范围内（小于1MB）
            Assert.Less(memoryIncrease, 1024 * 1024, "移动优化系统内存使用增长过多");
        }
        
        #endregion
        
        #region Error Handling Tests
        
        [Test]
        public void MobileOptimization_ErrorHandling_HandlesNullReferences()
        {
            // 错误处理测试：空引用处理
            
            // 测试空池名称
            Assert.DoesNotThrow(() => objectPool.GetObject(null));
            Assert.DoesNotThrow(() => objectPool.GetObject(""));
            
            // 测试空游戏状态恢复
            Assert.DoesNotThrow(() => interruptHandler.RestoreGameState(null));
        }
        
        [Test]
        public void MobileOptimization_ErrorHandling_HandlesInvalidParameters()
        {
            // 错误处理测试：无效参数处理
            
            // 测试无效帧率
            Assert.DoesNotThrow(() => mobileDeviceManager.SetTargetFrameRate(-1));
            Assert.DoesNotThrow(() => mobileDeviceManager.SetTargetFrameRate(1000));
            
            // 测试无效质量等级
            Assert.DoesNotThrow(() => performanceMonitor.SetQualityLevel(-1));
            Assert.DoesNotThrow(() => performanceMonitor.SetQualityLevel(100));
        }
        
        #endregion
    }
}
