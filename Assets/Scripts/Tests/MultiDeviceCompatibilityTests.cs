using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using System.Collections.Generic;
using MobileScrollingGame.Core;
using MobileScrollingGame.Mobile;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 多设备兼容性测试
    /// 测试游戏在不同设备和配置下的兼容性
    /// </summary>
    public class MultiDeviceCompatibilityTests
    {
        /// <summary>
        /// 设备配置信息
        /// </summary>
        public struct DeviceConfig
        {
            public string deviceName;
            public int screenWidth;
            public int screenHeight;
            public float dpi;
            public int memoryMB;
            public int cpuCores;
            public DevicePerformanceLevel performanceLevel;
            public RuntimePlatform platform;
        }
        
        // 测试设备配置
        private static readonly DeviceConfig[] TestDevices = {
            new DeviceConfig {
                deviceName = "iPhone SE",
                screenWidth = 1136,
                screenHeight = 640,
                dpi = 326f,
                memoryMB = 2048,
                cpuCores = 2,
                performanceLevel = DevicePerformanceLevel.Medium,
                platform = RuntimePlatform.IPhonePlayer
            },
            new DeviceConfig {
                deviceName = "iPhone 12",
                screenWidth = 2532,
                screenHeight = 1170,
                dpi = 460f,
                memoryMB = 4096,
                cpuCores = 6,
                performanceLevel = DevicePerformanceLevel.High,
                platform = RuntimePlatform.IPhonePlayer
            },
            new DeviceConfig {
                deviceName = "Samsung Galaxy A50",
                screenWidth = 2340,
                screenHeight = 1080,
                dpi = 403f,
                memoryMB = 4096,
                cpuCores = 8,
                performanceLevel = DevicePerformanceLevel.Medium,
                platform = RuntimePlatform.Android
            },
            new DeviceConfig {
                deviceName = "Samsung Galaxy S21",
                screenWidth = 2400,
                screenHeight = 1080,
                dpi = 421f,
                memoryMB = 8192,
                cpuCores = 8,
                performanceLevel = DevicePerformanceLevel.High,
                platform = RuntimePlatform.Android
            },
            new DeviceConfig {
                deviceName = "Budget Android",
                screenWidth = 1280,
                screenHeight = 720,
                dpi = 294f,
                memoryMB = 1024,
                cpuCores = 4,
                performanceLevel = DevicePerformanceLevel.Low,
                platform = RuntimePlatform.Android
            }
        };
        
        private GameObject testGameObject;
        private MobileDeviceManager mobileDeviceManager;
        private PerformanceMonitor performanceMonitor;
        
        [SetUp]
        public void SetUp()
        {
            testGameObject = new GameObject("MultiDeviceCompatibilityTest");
            mobileDeviceManager = testGameObject.AddComponent<MobileDeviceManager>();
            performanceMonitor = testGameObject.AddComponent<PerformanceMonitor>();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
        }
        
        #region 屏幕分辨率兼容性测试
        
        [Test]
        public void MultiDevice_ScreenResolution_AllDevicesSupported()
        {
            Debug.Log("测试屏幕分辨率兼容性...");
            
            foreach (var device in TestDevices)
            {
                TestScreenResolutionForDevice(device);
            }
            
            Debug.Log("✓ 所有设备屏幕分辨率兼容性测试通过");
        }
        
        /// <summary>
        /// 测试特定设备的屏幕分辨率
        /// </summary>
        private void TestScreenResolutionForDevice(DeviceConfig device)
        {
            Debug.Log($"测试设备: {device.deviceName} ({device.screenWidth}x{device.screenHeight})");
            
            // 计算屏幕比例
            float aspectRatio = (float)device.screenWidth / device.screenHeight;
            
            // 验证支持的屏幕比例范围
            Assert.IsTrue(aspectRatio >= 0.5f && aspectRatio <= 3f, 
                $"设备 {device.deviceName} 的屏幕比例超出支持范围: {aspectRatio:F2}");
            
            // 验证最小分辨率要求
            Assert.GreaterOrEqual(device.screenWidth, 480, 
                $"设备 {device.deviceName} 屏幕宽度低于最小要求");
            Assert.GreaterOrEqual(device.screenHeight, 320, 
                $"设备 {device.deviceName} 屏幕高度低于最小要求");
            
            // 测试UI缩放
            TestUIScalingForDevice(device);
        }
        
        /// <summary>
        /// 测试UI缩放
        /// </summary>
        private void TestUIScalingForDevice(DeviceConfig device)
        {
            // 计算UI缩放因子
            float baseWidth = 1920f;
            float baseHeight = 1080f;
            
            float widthScale = device.screenWidth / baseWidth;
            float heightScale = device.screenHeight / baseHeight;
            float uiScale = Mathf.Min(widthScale, heightScale);
            
            // 验证UI缩放在合理范围内
            Assert.IsTrue(uiScale >= 0.3f && uiScale <= 3f, 
                $"设备 {device.deviceName} 的UI缩放因子超出合理范围: {uiScale:F2}");
            
            Debug.Log($"设备 {device.deviceName} UI缩放因子: {uiScale:F2}");
        }
        
        #endregion
        
        #region 性能等级兼容性测试
        
        [Test]
        public void MultiDevice_PerformanceLevel_OptimalSettingsApplied()
        {
            Debug.Log("测试性能等级兼容性...");
            
            foreach (var device in TestDevices)
            {
                TestPerformanceLevelForDevice(device);
            }
            
            Debug.Log("✓ 所有设备性能等级兼容性测试通过");
        }
        
        /// <summary>
        /// 测试特定设备的性能等级
        /// </summary>
        private void TestPerformanceLevelForDevice(DeviceConfig device)
        {
            Debug.Log($"测试设备性能: {device.deviceName} ({device.performanceLevel})");
            
            // 根据设备性能等级获取推荐设置
            var recommendedSettings = GetRecommendedSettingsForDevice(device);
            
            // 验证设置合理性
            ValidateDeviceSettings(device, recommendedSettings);
        }
        
        /// <summary>
        /// 获取设备推荐设置
        /// </summary>
        private DeviceSettings GetRecommendedSettingsForDevice(DeviceConfig device)
        {
            var settings = new DeviceSettings();
            
            switch (device.performanceLevel)
            {
                case DevicePerformanceLevel.Low:
                    settings.qualityLevel = 0;
                    settings.targetFrameRate = 30;
                    settings.enablePostProcessing = false;
                    settings.textureQuality = 2; // 1/4 resolution
                    settings.shadowQuality = 0;
                    break;
                    
                case DevicePerformanceLevel.Medium:
                    settings.qualityLevel = 2;
                    settings.targetFrameRate = 45;
                    settings.enablePostProcessing = true;
                    settings.textureQuality = 1; // 1/2 resolution
                    settings.shadowQuality = 1;
                    break;
                    
                case DevicePerformanceLevel.High:
                    settings.qualityLevel = 4;
                    settings.targetFrameRate = 60;
                    settings.enablePostProcessing = true;
                    settings.textureQuality = 0; // Full resolution
                    settings.shadowQuality = 2;
                    break;
            }
            
            return settings;
        }
        
        /// <summary>
        /// 验证设备设置
        /// </summary>
        private void ValidateDeviceSettings(DeviceConfig device, DeviceSettings settings)
        {
            // 验证质量等级
            Assert.IsTrue(settings.qualityLevel >= 0 && settings.qualityLevel <= 5, 
                $"设备 {device.deviceName} 的质量等级无效: {settings.qualityLevel}");
            
            // 验证目标帧率
            Assert.IsTrue(settings.targetFrameRate >= 15 && settings.targetFrameRate <= 120, 
                $"设备 {device.deviceName} 的目标帧率无效: {settings.targetFrameRate}");
            
            // 验证纹理质量
            Assert.IsTrue(settings.textureQuality >= 0 && settings.textureQuality <= 3, 
                $"设备 {device.deviceName} 的纹理质量无效: {settings.textureQuality}");
            
            Debug.Log($"设备 {device.deviceName} 设置验证通过: " +
                     $"质量={settings.qualityLevel}, 帧率={settings.targetFrameRate}, " +
                     $"纹理={settings.textureQuality}");
        }
        
        /// <summary>
        /// 设备设置
        /// </summary>
        private struct DeviceSettings
        {
            public int qualityLevel;
            public int targetFrameRate;
            public bool enablePostProcessing;
            public int textureQuality;
            public int shadowQuality;
        }
        
        #endregion
        
        #region 平台特定功能测试
        
        [Test]
        public void MultiDevice_PlatformSpecific_FeaturesWorkCorrectly()
        {
            Debug.Log("测试平台特定功能...");
            
            TestiOSSpecificFeatures();
            TestAndroidSpecificFeatures();
            TestUniversalFeatures();
            
            Debug.Log("✓ 平台特定功能测试通过");
        }
        
        /// <summary>
        /// 测试iOS特定功能
        /// </summary>
        private void TestiOSSpecificFeatures()
        {
            Debug.Log("测试iOS特定功能...");
            
            // 在实际iOS设备上运行时的测试
            if (Application.platform == RuntimePlatform.IPhonePlayer)
            {
                // 测试iOS特定的输入处理（显式使用 UnityEngine.Input，避免命名空间冲突）
                Assert.IsTrue(global::UnityEngine.Input.multiTouchEnabled, "iOS设备应启用多点触控"); // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
                
                // 测试iOS特定的性能设置
                Assert.IsTrue(Application.targetFrameRate > 0, "iOS设备应设置目标帧率");
                
                Debug.Log("iOS特定功能验证通过");
            }
            else
            {
                Debug.Log("非iOS平台，跳过iOS特定功能测试");
            }
        }
        
        /// <summary>
        /// 测试Android特定功能
        /// </summary>
        private void TestAndroidSpecificFeatures()
        {
            Debug.Log("测试Android特定功能...");
            
            // 在实际Android设备上运行时的测试
            if (Application.platform == RuntimePlatform.Android)
            {
                // 测试Android特定的输入处理（显式使用 UnityEngine.Input，避免命名空间冲突）
                Assert.IsTrue(global::UnityEngine.Input.multiTouchEnabled, "Android设备应启用多点触控"); // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
                
                // 测试Android特定的内存管理
                Assert.Greater(SystemInfo.systemMemorySize, 0, "Android设备应报告系统内存");
                
                Debug.Log("Android特定功能验证通过");
            }
            else
            {
                Debug.Log("非Android平台，跳过Android特定功能测试");
            }
        }
        
        /// <summary>
        /// 测试通用功能
        /// </summary>
        private void TestUniversalFeatures()
        {
            Debug.Log("测试通用移动功能...");
            
            // 测试通用的移动设备功能
            Assert.IsNotNull(SystemInfo.deviceModel, "设备应报告设备型号");
            Assert.IsNotNull(SystemInfo.operatingSystem, "设备应报告操作系统");
            Assert.Greater(SystemInfo.processorCount, 0, "设备应报告处理器核心数");
            
            // 测试电池状态（如果可用）
            if (SystemInfo.batteryLevel >= 0)
            {
                Assert.IsTrue(SystemInfo.batteryLevel >= 0f && SystemInfo.batteryLevel <= 1f, 
                    "电池电量应在0-1范围内");
            }
            
            Debug.Log("通用移动功能验证通过");
        }
        
        #endregion
        
        #region 内存使用兼容性测试
        
        [UnityTest]
        public IEnumerator MultiDevice_MemoryUsage_WithinLimitsForAllDevices()
        {
            Debug.Log("测试内存使用兼容性...");
            
            foreach (var device in TestDevices)
            {
                // 直接 yield return 子协程（本类不是 MonoBehaviour）
                yield return TestMemoryUsageForDevice(device);
            }
            
            Debug.Log("✓ 所有设备内存使用兼容性测试通过");
        }
        
        /// <summary>
        /// 测试特定设备的内存使用
        /// </summary>
        private IEnumerator TestMemoryUsageForDevice(DeviceConfig device)
        {
            Debug.Log($"测试设备内存使用: {device.deviceName} ({device.memoryMB}MB)");
            
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 模拟游戏运行
            // 直接 yield return 子协程（避免在测试中使用 StartCoroutine）
            yield return SimulateGameplayForDevice(device);
            
            long finalMemory = System.GC.GetTotalMemory(false);
            long memoryUsed = finalMemory - initialMemory;
            
            // 计算内存使用限制（基于设备总内存的百分比）
            long memoryLimitBytes = CalculateMemoryLimitForDevice(device);
            
            Assert.Less(memoryUsed, memoryLimitBytes, 
                $"设备 {device.deviceName} 内存使用超出限制: " +
                $"{memoryUsed / (1024 * 1024)}MB > {memoryLimitBytes / (1024 * 1024)}MB");
            
            Debug.Log($"设备 {device.deviceName} 内存使用: {memoryUsed / (1024 * 1024)}MB");
        }
        
        /// <summary>
        /// 计算设备内存限制
        /// </summary>
        private long CalculateMemoryLimitForDevice(DeviceConfig device)
        {
            // 根据设备内存大小设置使用限制
            float memoryLimitPercentage;
            
            if (device.memoryMB <= 1024)
            {
                memoryLimitPercentage = 0.3f; // 低内存设备使用30%
            }
            else if (device.memoryMB <= 2048)
            {
                memoryLimitPercentage = 0.4f; // 中等内存设备使用40%
            }
            else
            {
                memoryLimitPercentage = 0.5f; // 高内存设备使用50%
            }
            
            return (long)(device.memoryMB * 1024 * 1024 * memoryLimitPercentage);
        }
        
        /// <summary>
        /// 模拟特定设备的游戏玩法
        /// </summary>
        private IEnumerator SimulateGameplayForDevice(DeviceConfig device)
        {
            // 根据设备性能调整模拟强度
            int operationCount = device.performanceLevel == DevicePerformanceLevel.Low ? 50 :
                               device.performanceLevel == DevicePerformanceLevel.Medium ? 100 : 200;
            
            for (int i = 0; i < operationCount; i++)
            {
                // 模拟游戏操作
                CreateTemporaryGameObject();
                
                if (i % 10 == 0)
                {
                    yield return null;
                }
            }
            
            // 清理临时对象
            CleanupTemporaryObjects();
        }
        
        /// <summary>
        /// 创建临时游戏对象
        /// </summary>
        private void CreateTemporaryGameObject()
        {
            GameObject tempObj = new GameObject("TempObject");
            tempObj.tag = "TestObject";
            tempObj.AddComponent<MeshRenderer>();
            tempObj.AddComponent<BoxCollider>();
        }
        
        /// <summary>
        /// 清理临时对象
        /// </summary>
        private void CleanupTemporaryObjects()
        {
            GameObject[] tempObjects = GameObject.FindGameObjectsWithTag("TestObject");
            foreach (GameObject obj in tempObjects)
            {
                Object.DestroyImmediate(obj);
            }
        }
        
        #endregion
        
        #region 输入系统兼容性测试
        
        [Test]
        public void MultiDevice_InputSystem_SupportsAllInputMethods()
        {
            Debug.Log("测试输入系统兼容性...");
            
            TestTouchInputCompatibility();
            TestKeyboardInputCompatibility();
            TestAccelerometerCompatibility();
            
            Debug.Log("✓ 输入系统兼容性测试通过");
        }
        
        /// <summary>
        /// 测试触摸输入兼容性
        /// </summary>
        private void TestTouchInputCompatibility()
        {
            Debug.Log("测试触摸输入兼容性...");
            
            // 验证多点触控支持（显式使用 UnityEngine.Input，避免命名空间冲突）
            Assert.IsTrue(global::UnityEngine.Input.multiTouchEnabled, "应启用多点触控支持"); // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            
            // 验证触摸模拟（在编辑器中）
            if (!Application.isMobilePlatform)
            {
                Assert.IsTrue(global::UnityEngine.Input.simulateMouseWithTouches, "编辑器中应启用触摸模拟"); // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
            }
            
            Debug.Log("触摸输入兼容性验证通过");
        }
        
        /// <summary>
        /// 测试键盘输入兼容性
        /// </summary>
        private void TestKeyboardInputCompatibility()
        {
            Debug.Log("测试键盘输入兼容性...");
            
            // 在移动设备上，键盘输入主要用于调试
            if (Application.isMobilePlatform)
            {
                Debug.Log("移动平台，键盘输入用于调试目的");
            }
            else
            {
                Debug.Log("非移动平台，键盘输入正常可用");
            }
            
            Debug.Log("键盘输入兼容性验证通过");
        }
        
        /// <summary>
        /// 测试加速度计兼容性
        /// </summary>
        private void TestAccelerometerCompatibility()
        {
            Debug.Log("测试加速度计兼容性...");
            
            // 检查加速度计支持
            if (SystemInfo.supportsAccelerometer)
            {
                Vector3 acceleration = global::UnityEngine.Input.acceleration; // 使用全局限定避免命名空间冲突（Confirmed via 寸止）
                Debug.Log($"加速度计数据: {acceleration}");
            }
            else
            {
                Debug.Log("设备不支持加速度计");
            }
            
            Debug.Log("加速度计兼容性验证通过");
        }
        
        #endregion
        
        #region 网络连接兼容性测试
        
        [UnityTest]
        public IEnumerator MultiDevice_NetworkConnectivity_HandlesAllConnectionTypes()
        {
            Debug.Log("测试网络连接兼容性...");
            
            // 直接 yield return 子协程
            yield return TestNetworkReachability();
            TestInternetConnectionHandling();
            
            Debug.Log("✓ 网络连接兼容性测试通过");
        }
        
        /// <summary>
        /// 测试网络可达性
        /// </summary>
        private IEnumerator TestNetworkReachability()
        {
            Debug.Log("测试网络可达性...");
            
            NetworkReachability reachability = Application.internetReachability;
            
            switch (reachability)
            {
                case NetworkReachability.NotReachable:
                    Debug.Log("网络不可达 - 游戏应进入离线模式");
                    break;
                case NetworkReachability.ReachableViaCarrierDataNetwork:
                    Debug.Log("通过移动数据网络可达 - 应考虑数据使用量");
                    break;
                case NetworkReachability.ReachableViaLocalAreaNetwork:
                    Debug.Log("通过WiFi网络可达 - 可以进行完整的网络功能");
                    break;
            }
            
            yield return new WaitForSeconds(0.1f);
            
            Debug.Log("网络可达性测试完成");
        }
        
        /// <summary>
        /// 测试网络连接处理
        /// </summary>
        private void TestInternetConnectionHandling()
        {
            Debug.Log("测试网络连接处理...");
            
            // 验证游戏能够处理不同的网络状态
            bool hasInternetConnection = Application.internetReachability != NetworkReachability.NotReachable;
            
            if (hasInternetConnection)
            {
                Debug.Log("有网络连接 - 在线功能可用");
            }
            else
            {
                Debug.Log("无网络连接 - 离线模式");
            }
            
            Debug.Log("网络连接处理验证通过");
        }
        
        #endregion
        
        #region 设备特定优化测试
        
        [Test]
        public void MultiDevice_DeviceOptimization_AppliesCorrectSettings()
        {
            Debug.Log("测试设备特定优化...");
            
            foreach (var device in TestDevices)
            {
                TestDeviceSpecificOptimization(device);
            }
            
            Debug.Log("✓ 设备特定优化测试通过");
        }
        
        /// <summary>
        /// 测试设备特定优化
        /// </summary>
        private void TestDeviceSpecificOptimization(DeviceConfig device)
        {
            Debug.Log($"测试设备优化: {device.deviceName}");
            
            // 根据设备特性应用优化
            ApplyDeviceOptimizations(device);
            
            // 验证优化设置
            ValidateOptimizationSettings(device);
        }
        
        /// <summary>
        /// 应用设备优化
        /// </summary>
        private void ApplyDeviceOptimizations(DeviceConfig device)
        {
            // 根据设备性能等级设置质量
            int qualityLevel = GetQualityLevelForDevice(device);
            QualitySettings.SetQualityLevel(qualityLevel, true);
            
            // 根据设备设置目标帧率
            int targetFrameRate = GetTargetFrameRateForDevice(device);
            Application.targetFrameRate = targetFrameRate;
            
            // 根据设备内存设置纹理质量
            int textureQuality = GetTextureQualityForDevice(device);
            QualitySettings.globalTextureMipmapLimit = textureQuality;
        }
        
        /// <summary>
        /// 获取设备质量等级
        /// </summary>
        private int GetQualityLevelForDevice(DeviceConfig device)
        {
            switch (device.performanceLevel)
            {
                case DevicePerformanceLevel.Low: return 0;
                case DevicePerformanceLevel.Medium: return 2;
                case DevicePerformanceLevel.High: return 4;
                default: return 2;
            }
        }
        
        /// <summary>
        /// 获取设备目标帧率
        /// </summary>
        private int GetTargetFrameRateForDevice(DeviceConfig device)
        {
            switch (device.performanceLevel)
            {
                case DevicePerformanceLevel.Low: return 30;
                case DevicePerformanceLevel.Medium: return 45;
                case DevicePerformanceLevel.High: return 60;
                default: return 30;
            }
        }
        
        /// <summary>
        /// 获取设备纹理质量
        /// </summary>
        private int GetTextureQualityForDevice(DeviceConfig device)
        {
            if (device.memoryMB <= 1024) return 2; // 1/4 resolution
            if (device.memoryMB <= 2048) return 1; // 1/2 resolution
            return 0; // Full resolution
        }
        
        /// <summary>
        /// 验证优化设置
        /// </summary>
        private void ValidateOptimizationSettings(DeviceConfig device)
        {
            int expectedQuality = GetQualityLevelForDevice(device);
            int expectedFrameRate = GetTargetFrameRateForDevice(device);
            int expectedTextureQuality = GetTextureQualityForDevice(device);
            
            Assert.AreEqual(expectedQuality, QualitySettings.GetQualityLevel(), 
                $"设备 {device.deviceName} 质量等级设置不正确");
            
            Assert.AreEqual(expectedFrameRate, Application.targetFrameRate, 
                $"设备 {device.deviceName} 目标帧率设置不正确");
            
            Assert.AreEqual(expectedTextureQuality, QualitySettings.globalTextureMipmapLimit, 
                $"设备 {device.deviceName} 纹理质量设置不正确");
            
            Debug.Log($"设备 {device.deviceName} 优化设置验证通过");
        }
        
        #endregion
    }
}
