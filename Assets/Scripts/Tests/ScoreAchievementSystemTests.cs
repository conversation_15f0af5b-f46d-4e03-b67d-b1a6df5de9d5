using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using System.Collections.Generic;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 分数和成就系统测试
    /// 测试ScoreManager和AchievementManager的功能
    /// </summary>
    public class ScoreAchievementSystemTests
    {
        private GameObject testGameObject;
        private ScoreManager scoreManager;
        private AchievementManager achievementManager;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试游戏对象
            testGameObject = new GameObject("TestScoreAchievementSystem");
            
            // 添加ScoreManager组件
            scoreManager = testGameObject.AddComponent<ScoreManager>();
            
            // 添加AchievementManager组件
            achievementManager = testGameObject.AddComponent<AchievementManager>();
            
            // 清理PlayerPrefs以确保测试环境干净
            PlayerPrefs.DeleteAll();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
            
            // 清理PlayerPrefs
            PlayerPrefs.DeleteAll();
        }
        
        #region ScoreManager Tests
        
        [Test]
        public void ScoreManager_AddScore_UpdatesCurrentScore()
        {
            // Arrange
            int scoreToAdd = 100;
            
            // Act
            scoreManager.AddScore(scoreToAdd);
            
            // Assert
            Assert.AreEqual(scoreToAdd, scoreManager.CurrentScore);
        }
        
        [Test]
        public void ScoreManager_AddScore_TriggersScoreChangedEvent()
        {
            // Arrange
            int scoreToAdd = 100;
            bool eventTriggered = false;
            int receivedScore = 0;
            
            scoreManager.OnScoreChanged += (score) => {
                eventTriggered = true;
                receivedScore = score;
            };
            
            // Act
            scoreManager.AddScore(scoreToAdd);
            
            // Assert
            Assert.IsTrue(eventTriggered);
            Assert.AreEqual(scoreToAdd, receivedScore);
        }
        
        [Test]
        public void ScoreManager_AddScore_UpdatesHighScore()
        {
            // Arrange
            int scoreToAdd = 500;
            
            // Act
            scoreManager.AddScore(scoreToAdd);
            
            // Assert
            Assert.AreEqual(scoreToAdd, scoreManager.HighScore);
        }
        
        [Test]
        public void ScoreManager_AddScore_TriggersHighScoreChangedEvent()
        {
            // Arrange
            int scoreToAdd = 500;
            bool eventTriggered = false;
            int receivedHighScore = 0;
            
            scoreManager.OnHighScoreChanged += (highScore) => {
                eventTriggered = true;
                receivedHighScore = highScore;
            };
            
            // Act
            scoreManager.AddScore(scoreToAdd);
            
            // Assert
            Assert.IsTrue(eventTriggered);
            Assert.AreEqual(scoreToAdd, receivedHighScore);
        }
        
        [Test]
        public void ScoreManager_AddScore_TriggersMilestoneEvent()
        {
            // Arrange
            int scoreToAdd = 100; // 应该触发100分里程碑
            bool milestoneTriggered = false;
            int receivedMilestone = 0;
            
            scoreManager.OnMilestoneReached += (milestone) => {
                milestoneTriggered = true;
                receivedMilestone = milestone;
            };
            
            // Act
            scoreManager.AddScore(scoreToAdd);
            
            // Assert
            Assert.IsTrue(milestoneTriggered);
            Assert.AreEqual(100, receivedMilestone);
        }
        
        [Test]
        public void ScoreManager_AddScore_IgnoresNegativeScore()
        {
            // Arrange
            int negativeScore = -50;
            int initialScore = scoreManager.CurrentScore;
            
            // Act
            scoreManager.AddScore(negativeScore);
            
            // Assert
            Assert.AreEqual(initialScore, scoreManager.CurrentScore);
        }
        
        [Test]
        public void ScoreManager_SetScoreMultiplier_UpdatesMultiplier()
        {
            // Arrange
            float newMultiplier = 2.5f;
            
            // Act
            scoreManager.SetScoreMultiplier(newMultiplier);
            
            // Assert
            Assert.AreEqual(newMultiplier, scoreManager.ScoreMultiplier);
        }
        
        [Test]
        public void ScoreManager_SetScoreMultiplier_IgnoresNegativeValue()
        {
            // Arrange
            float negativeMultiplier = -1.5f;
            float initialMultiplier = scoreManager.ScoreMultiplier;
            
            // Act
            scoreManager.SetScoreMultiplier(negativeMultiplier);
            
            // Assert
            Assert.AreEqual(0f, scoreManager.ScoreMultiplier); // 应该被设置为0而不是负值
        }
        
        [Test]
        public void ScoreManager_ResetCurrentScore_ClearsScore()
        {
            // Arrange
            scoreManager.AddScore(500);
            
            // Act
            scoreManager.ResetCurrentScore();
            
            // Assert
            Assert.AreEqual(0, scoreManager.CurrentScore);
            Assert.AreEqual(0, scoreManager.ComboCount);
        }
        
        [Test]
        public void ScoreManager_ComboSystem_IncreasesComboCount()
        {
            // Arrange
            var collectibleData = new CollectibleData
            {
                type = CollectibleType.Coin,
                value = 10,
                quantity = 1
            };
            
            // Act - 模拟连续收集物品
            for (int i = 0; i < 3; i++)
            {
                // 这里需要模拟收集品收集事件
                // 由于我们无法直接访问私有方法，我们通过AddScore来测试连击系统
                scoreManager.AddScore(10);
            }
            
            // Assert
            // 注意：由于连击系统的实现细节，这个测试可能需要调整
            Assert.GreaterOrEqual(scoreManager.ComboCount, 0);
        }
        
        #endregion
        
        #region AchievementManager Tests
        
        [Test]
        public void AchievementManager_Initialize_CreatesDefaultAchievements()
        {
            // Act
            // AchievementManager在Awake中初始化
            
            // Assert
            Assert.Greater(achievementManager.TotalCount, 0);
            Assert.AreEqual(0, achievementManager.UnlockedCount);
        }
        
        [Test]
        public void AchievementManager_GetAchievement_ReturnsCorrectAchievement()
        {
            // Arrange
            string achievementId = "score_100";
            
            // Act
            var achievement = achievementManager.GetAchievement(achievementId);
            
            // Assert
            Assert.IsNotNull(achievement);
            Assert.AreEqual(achievementId, achievement.id);
        }
        
        [Test]
        public void AchievementManager_UnlockAchievementById_UnlocksAchievement()
        {
            // Arrange
            string achievementId = "score_100";
            var achievement = achievementManager.GetAchievement(achievementId);
            Assert.IsNotNull(achievement);
            Assert.IsFalse(achievement.isUnlocked);
            
            // Act
            achievementManager.UnlockAchievementById(achievementId);
            
            // Assert
            Assert.IsTrue(achievement.isUnlocked);
            Assert.AreEqual(1, achievementManager.UnlockedCount);
        }
        
        [Test]
        public void AchievementManager_UnlockAchievement_TriggersEvent()
        {
            // Arrange
            string achievementId = "score_100";
            bool eventTriggered = false;
            Achievement receivedAchievement = null;
            
            achievementManager.OnAchievementUnlocked += (achievement) => {
                eventTriggered = true;
                receivedAchievement = achievement;
            };
            
            // Act
            achievementManager.UnlockAchievementById(achievementId);
            
            // Assert
            Assert.IsTrue(eventTriggered);
            Assert.IsNotNull(receivedAchievement);
            Assert.AreEqual(achievementId, receivedAchievement.id);
        }
        
        [Test]
        public void AchievementManager_GetAchievementsByType_ReturnsCorrectAchievements()
        {
            // Arrange
            AchievementType targetType = AchievementType.Score;
            
            // Act
            var achievements = achievementManager.GetAchievementsByType(targetType);
            
            // Assert
            Assert.Greater(achievements.Count, 0);
            foreach (var achievement in achievements)
            {
                Assert.AreEqual(targetType, achievement.type);
            }
        }
        
        [Test]
        public void AchievementManager_GetUnlockedAchievements_ReturnsOnlyUnlocked()
        {
            // Arrange
            achievementManager.UnlockAchievementById("score_100");
            achievementManager.UnlockAchievementById("collect_10");
            
            // Act
            var unlockedAchievements = achievementManager.GetUnlockedAchievements();
            
            // Assert
            Assert.AreEqual(2, unlockedAchievements.Count);
            foreach (var achievement in unlockedAchievements)
            {
                Assert.IsTrue(achievement.isUnlocked);
            }
        }
        
        [Test]
        public void AchievementManager_ResetAllAchievements_ResetsProgress()
        {
            // Arrange
            achievementManager.UnlockAchievementById("score_100");
            achievementManager.UnlockAchievementById("collect_10");
            Assert.AreEqual(2, achievementManager.UnlockedCount);
            
            // Act
            achievementManager.ResetAllAchievements();
            
            // Assert
            Assert.AreEqual(0, achievementManager.UnlockedCount);
        }
        
        #endregion
        
        #region Achievement Class Tests
        
        [Test]
        public void Achievement_UpdateProgress_UpdatesCurrentValue()
        {
            // Arrange
            var achievement = new Achievement("test", "Test", "Test Description", AchievementType.Score, 100);
            
            // Act
            achievement.UpdateProgress(50);
            
            // Assert
            Assert.AreEqual(50, achievement.currentValue);
            Assert.IsFalse(achievement.isUnlocked);
        }
        
        [Test]
        public void Achievement_UpdateProgress_UnlocksWhenTargetReached()
        {
            // Arrange
            var achievement = new Achievement("test", "Test", "Test Description", AchievementType.Score, 100);
            
            // Act
            bool unlocked = achievement.UpdateProgress(100);
            
            // Assert
            Assert.IsTrue(unlocked);
            Assert.IsTrue(achievement.isUnlocked);
            Assert.AreEqual(100, achievement.currentValue);
        }
        
        [Test]
        public void Achievement_UpdateProgress_DoesNotDecrease()
        {
            // Arrange
            var achievement = new Achievement("test", "Test", "Test Description", AchievementType.Score, 100);
            achievement.UpdateProgress(50);
            
            // Act
            achievement.UpdateProgress(30); // 尝试设置更低的值
            
            // Assert
            Assert.AreEqual(50, achievement.currentValue); // 应该保持原值
        }
        
        [Test]
        public void Achievement_GetProgressPercentage_ReturnsCorrectPercentage()
        {
            // Arrange
            var achievement = new Achievement("test", "Test", "Test Description", AchievementType.Score, 100);
            achievement.UpdateProgress(25);
            
            // Act
            float percentage = achievement.GetProgressPercentage();
            
            // Assert
            Assert.AreEqual(0.25f, percentage, 0.001f);
        }
        
        [Test]
        public void Achievement_CanDisplay_ReturnsCorrectValue()
        {
            // Arrange
            var visibleAchievement = new Achievement("test1", "Test1", "Test Description", AchievementType.Score, 100);
            var hiddenAchievement = new Achievement("test2", "Test2", "Test Description", AchievementType.Score, 100);
            hiddenAchievement.isHidden = true;
            
            // Act & Assert
            Assert.IsTrue(visibleAchievement.CanDisplay());
            Assert.IsFalse(hiddenAchievement.CanDisplay());
            
            // 解锁隐藏成就后应该可以显示
            hiddenAchievement.isUnlocked = true;
            Assert.IsTrue(hiddenAchievement.CanDisplay());
        }
        
        [Test]
        public void Achievement_Reset_ClearsProgress()
        {
            // Arrange
            var achievement = new Achievement("test", "Test", "Test Description", AchievementType.Score, 100);
            achievement.UpdateProgress(100); // 解锁成就
            
            // Act
            achievement.Reset();
            
            // Assert
            Assert.AreEqual(0, achievement.currentValue);
            Assert.IsFalse(achievement.isUnlocked);
        }
        
        #endregion
        
        #region Integration Tests
        
        [UnityTest]
        public IEnumerator ScoreAndAchievement_Integration_UnlocksScoreAchievement()
        {
            // Arrange
            bool achievementUnlocked = false;
            Achievement unlockedAchievement = null;
            
            achievementManager.OnAchievementUnlocked += (achievement) => {
                achievementUnlocked = true;
                unlockedAchievement = achievement;
            };
            
            // Act
            scoreManager.AddScore(100); // 应该解锁"score_100"成就
            
            // 等待一帧以确保事件处理完成
            yield return null;
            
            // Assert
            Assert.IsTrue(achievementUnlocked);
            Assert.IsNotNull(unlockedAchievement);
            Assert.AreEqual("score_100", unlockedAchievement.id);
        }
        
        [UnityTest]
        public IEnumerator ScoreManager_MilestoneReached_TriggersCorrectMilestone()
        {
            // Arrange
            bool milestoneReached = false;
            int reachedMilestone = 0;
            
            scoreManager.OnMilestoneReached += (milestone) => {
                milestoneReached = true;
                reachedMilestone = milestone;
            };
            
            // Act
            scoreManager.AddScore(250); // 应该触发250分里程碑
            
            // 等待一帧
            yield return null;
            
            // Assert
            Assert.IsTrue(milestoneReached);
            Assert.AreEqual(250, reachedMilestone);
        }
        
        #endregion
        
        #region Performance Tests
        
        [Test]
        public void ScoreManager_AddScore_PerformanceTest()
        {
            // Arrange
            int iterations = 1000;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // Act
            for (int i = 0; i < iterations; i++)
            {
                scoreManager.AddScore(1);
            }
            
            stopwatch.Stop();
            
            // Assert
            Assert.Less(stopwatch.ElapsedMilliseconds, 100); // 应该在100ms内完成
            Debug.Log($"AddScore performance: {stopwatch.ElapsedMilliseconds}ms for {iterations} iterations");
        }
        
        [Test]
        public void AchievementManager_CheckAchievements_PerformanceTest()
        {
            // Arrange
            int iterations = 100;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // Act
            for (int i = 0; i < iterations; i++)
            {
                scoreManager.AddScore(1); // 这会触发成就检查
            }
            
            stopwatch.Stop();
            
            // Assert
            Assert.Less(stopwatch.ElapsedMilliseconds, 50); // 应该在50ms内完成
            Debug.Log($"Achievement checking performance: {stopwatch.ElapsedMilliseconds}ms for {iterations} iterations");
        }
        
        #endregion
    }
}