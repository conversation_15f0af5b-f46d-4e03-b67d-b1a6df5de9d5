<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="2" result="Passed" total="2" passed="2" failed="0" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 08:12:12Z" end-time="2025-08-05 08:12:13Z" duration="0.0829581">
  <test-suite type="TestSuite" id="1008" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="2" result="Passed" start-time="2025-08-05 08:12:12Z" end-time="2025-08-05 08:12:13Z" duration="0.082958" total="2" passed="2" failed="0" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="PlayMode" />
    </properties>
    <test-suite type="Assembly" id="1004" name="Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/Tests.dll" runstate="Runnable" testcasecount="2" result="Passed" start-time="2025-08-05 08:12:13Z" end-time="2025-08-05 08:12:13Z" duration="0.057785" total="2" passed="2" failed="0" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="4631" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="PlayMode" />
        <property name="EditorOnly" value="False" />
      </properties>
      <test-suite type="TestFixture" id="1001" name="NewTestScript" fullname="NewTestScript" classname="NewTestScript" runstate="Runnable" testcasecount="2" result="Passed" start-time="2025-08-05 08:12:13Z" end-time="2025-08-05 08:12:13Z" duration="0.052181" total="2" passed="2" failed="0" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <test-case id="1002" name="NewTestScriptSimplePasses" fullname="NewTestScript.NewTestScriptSimplePasses" methodname="NewTestScriptSimplePasses" classname="NewTestScript" runstate="Runnable" seed="1239920051" result="Passed" start-time="2025-08-05 08:12:13Z" end-time="2025-08-05 08:12:13Z" duration="0.014791" asserts="0">
          <properties>
            <property name="retryIteration" value="0" />
            <property name="repeatIteration" value="0" />
          </properties>
        </test-case>
        <test-case id="1003" name="NewTestScriptWithEnumeratorPasses" fullname="NewTestScript.NewTestScriptWithEnumeratorPasses" methodname="NewTestScriptWithEnumeratorPasses" classname="NewTestScript" runstate="Runnable" seed="*********" result="Passed" start-time="2025-08-05 08:12:13Z" end-time="2025-08-05 08:12:13Z" duration="0.015011" asserts="0">
          <properties>
            <property name="_JOINTYPE" value="UnityCombinatorial" />
            <property name="retryIteration" value="0" />
            <property name="repeatIteration" value="0" />
          </properties>
          <output><![CDATA[Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
        </test-case>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>