using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 成就通知UI组件
    /// 显示成就解锁的通知和反馈
    /// </summary>
    public class AchievementNotification : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private GameObject notificationPanel;
        [SerializeField] private TextMeshProUGUI achievementNameText;
        [SerializeField] private TextMeshProUGUI achievementDescriptionText;
        [SerializeField] private TextMeshProUGUI scoreRewardText;
        [SerializeField] private Image achievementIcon;
        [SerializeField] private Button closeButton;
        
        [Header("动画设置")]
        [SerializeField] private float displayDuration = 3f;
        [SerializeField] private float fadeInDuration = 0.5f;
        [SerializeField] private float fadeOutDuration = 0.5f;
        [SerializeField] private AnimationCurve fadeInCurve = new AnimationCurve(
            new Keyframe(0, 0, 0, 2),
            new Keyframe(1, 1, 0, 0)
        );
        [SerializeField] private AnimationCurve fadeOutCurve = new AnimationCurve(
            new Keyframe(0, 1, 0, 0),
            new Keyframe(1, 0, -2, 0)
        );
        
        [Header("音效设置")]
        [SerializeField] private AudioClip achievementUnlockSound;
        [SerializeField] private float soundVolume = 1f;
        
        [Header("视觉效果")]
        [SerializeField] private ParticleSystem celebrationEffect;
        [SerializeField] private Color highlightColor = Color.yellow;
        [SerializeField] private bool enableScreenFlash = true;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private CanvasGroup canvasGroup;
        private AudioSource audioSource;
        
        // 状态
        private Coroutine displayCoroutine;
        private Queue<Achievement> notificationQueue = new Queue<Achievement>();
        private bool isDisplaying = false;
        
        private void Awake()
        {
            SetupComponents();
            SetupUI();
        }
        
        private void Start()
        {
            RegisterEvents();
            HideNotification();
        }
        
        /// <summary>
        /// 设置组件引用
        /// </summary>
        private void SetupComponents()
        {
            // 获取或添加CanvasGroup
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }
            
            // 获取或添加AudioSource
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            audioSource.playOnAwake = false;
            audioSource.volume = soundVolume;
        }
        
        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            if (closeButton != null)
            {
                closeButton.onClick.AddListener(HideNotification);
            }
            
            // 确保通知面板初始状态为隐藏
            if (notificationPanel != null)
            {
                notificationPanel.SetActive(false);
            }
        }
        
        /// <summary>
        /// 注册事件
        /// </summary>
        private void RegisterEvents()
        {
            if (AchievementManager.Instance != null)
            {
                AchievementManager.Instance.OnAchievementUnlocked += ShowAchievementNotification;
            }
        }
        
        /// <summary>
        /// 显示成就通知
        /// </summary>
        public void ShowAchievementNotification(Achievement achievement)
        {
            if (achievement == null) return;
            
            // 添加到队列
            notificationQueue.Enqueue(achievement);
            
            // 如果当前没有显示通知，开始显示
            if (!isDisplaying)
            {
                ProcessNotificationQueue();
            }
        }
        
        /// <summary>
        /// 处理通知队列
        /// </summary>
        private void ProcessNotificationQueue()
        {
            if (notificationQueue.Count == 0)
            {
                isDisplaying = false;
                return;
            }
            
            isDisplaying = true;
            Achievement achievement = notificationQueue.Dequeue();
            
            if (displayCoroutine != null)
            {
                StopCoroutine(displayCoroutine);
            }
            
            displayCoroutine = StartCoroutine(DisplayNotificationCoroutine(achievement));
        }
        
        /// <summary>
        /// 显示通知协程
        /// </summary>
        private System.Collections.IEnumerator DisplayNotificationCoroutine(Achievement achievement)
        {
            // 更新UI内容
            UpdateNotificationContent(achievement);
            
            // 显示面板
            if (notificationPanel != null)
            {
                notificationPanel.SetActive(true);
            }
            
            // 播放音效
            PlayUnlockSound();
            
            // 播放视觉效果
            PlayCelebrationEffect();
            
            // 淡入动画
            yield return StartCoroutine(FadeInAnimation());
            
            // 显示持续时间
            yield return new WaitForSecondsRealtime(displayDuration);
            
            // 淡出动画
            yield return StartCoroutine(FadeOutAnimation());
            
            // 隐藏面板
            if (notificationPanel != null)
            {
                notificationPanel.SetActive(false);
            }
            
            // 处理下一个通知
            ProcessNotificationQueue();
        }
        
        /// <summary>
        /// 更新通知内容
        /// </summary>
        private void UpdateNotificationContent(Achievement achievement)
        {
            if (achievementNameText != null)
            {
                achievementNameText.text = achievement.name;
            }
            
            if (achievementDescriptionText != null)
            {
                achievementDescriptionText.text = achievement.description;
            }
            
            if (scoreRewardText != null)
            {
                if (achievement.scoreReward > 0)
                {
                    scoreRewardText.text = $"+{achievement.scoreReward} 分";
                    scoreRewardText.gameObject.SetActive(true);
                }
                else
                {
                    scoreRewardText.gameObject.SetActive(false);
                }
            }
            
            if (achievementIcon != null)
            {
                if (achievement.icon != null)
                {
                    achievementIcon.sprite = achievement.icon;
                    achievementIcon.gameObject.SetActive(true);
                }
                else
                {
                    achievementIcon.gameObject.SetActive(false);
                }
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"显示成就通知: {achievement.name}");
            }
        }
        
        /// <summary>
        /// 淡入动画
        /// </summary>
        private System.Collections.IEnumerator FadeInAnimation()
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < fadeInDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / fadeInDuration;
                float curveValue = fadeInCurve.Evaluate(progress);
                
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = curveValue;
                }
                
                yield return null;
            }
            
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 1f;
            }
        }
        
        /// <summary>
        /// 淡出动画
        /// </summary>
        private System.Collections.IEnumerator FadeOutAnimation()
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < fadeOutDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / fadeOutDuration;
                float curveValue = fadeOutCurve.Evaluate(progress);
                
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = curveValue;
                }
                
                yield return null;
            }
            
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 0f;
            }
        }
        
        /// <summary>
        /// 播放解锁音效
        /// </summary>
        private void PlayUnlockSound()
        {
            if (achievementUnlockSound != null && audioSource != null)
            {
                audioSource.clip = achievementUnlockSound;
                audioSource.Play();
            }
        }
        
        /// <summary>
        /// 播放庆祝特效
        /// </summary>
        private void PlayCelebrationEffect()
        {
            if (celebrationEffect != null)
            {
                celebrationEffect.Play();
            }
            
            // 屏幕闪烁效果
            if (enableScreenFlash)
            {
                StartCoroutine(ScreenFlashEffect());
            }
        }
        
        /// <summary>
        /// 屏幕闪烁效果
        /// </summary>
        private System.Collections.IEnumerator ScreenFlashEffect()
        {
            // 这里可以实现屏幕闪烁效果
            // 例如：创建一个全屏的白色图像，快速淡入淡出
            yield return null;
        }
        
        /// <summary>
        /// 隐藏通知
        /// </summary>
        public void HideNotification()
        {
            if (displayCoroutine != null)
            {
                StopCoroutine(displayCoroutine);
                displayCoroutine = null;
            }
            
            if (notificationPanel != null)
            {
                notificationPanel.SetActive(false);
            }
            
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 0f;
            }
            
            // 继续处理队列中的下一个通知
            if (notificationQueue.Count > 0)
            {
                ProcessNotificationQueue();
            }
            else
            {
                isDisplaying = false;
            }
        }
        
        /// <summary>
        /// 清空通知队列
        /// </summary>
        public void ClearNotificationQueue()
        {
            notificationQueue.Clear();
            HideNotification();
        }
        
        /// <summary>
        /// 测试显示成就通知
        /// </summary>
        [ContextMenu("Test Achievement Notification")]
        public void TestNotification()
        {
            var testAchievement = new Achievement("test", "测试成就", "这是一个测试成就", AchievementType.Special, 1);
            testAchievement.scoreReward = 100;
            testAchievement.isUnlocked = true;
            
            ShowAchievementNotification(testAchievement);
        }
        
        private void OnDestroy()
        {
            // 清理事件订阅
            if (AchievementManager.Instance != null)
            {
                AchievementManager.Instance.OnAchievementUnlocked -= ShowAchievementNotification;
            }
        }
    }
}