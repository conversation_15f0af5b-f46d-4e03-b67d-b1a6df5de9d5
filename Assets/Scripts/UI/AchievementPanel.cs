using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using System.Linq;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 成就面板UI组件
    /// 显示所有成就的列表和进度
    /// </summary>
    public class AchievementPanel : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private GameObject achievementItemPrefab;
        [SerializeField] private Transform achievementContainer;
        [SerializeField] private ScrollRect scrollRect;
        [SerializeField] private Button closeButton;
        
        [Header("统计显示")]
        [SerializeField] private TextMeshProUGUI statsText;
        [SerializeField] private Slider progressSlider;
        [SerializeField] private TextMeshProUGUI progressText;
        
        [Header("筛选选项")]
        [SerializeField] private Toggle showAllToggle;
        [SerializeField] private Toggle showUnlockedToggle;
        [SerializeField] private Toggle showLockedToggle;
        [SerializeField] private Dropdown typeFilterDropdown;
        
        [Header("排序选项")]
        [SerializeField] private Dropdown sortDropdown;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 状态
        private List<AchievementItem> achievementItems = new List<AchievementItem>();
        private AchievementFilter currentFilter = AchievementFilter.All;
        private AchievementSort currentSort = AchievementSort.Default;
        
        // 筛选和排序枚举
        public enum AchievementFilter
        {
            All,
            Unlocked,
            Locked
        }
        
        public enum AchievementSort
        {
            Default,
            Name,
            Progress,
            Type
        }
        
        private void Awake()
        {
            SetupUI();
        }
        
        private void Start()
        {
            RefreshAchievementList();
        }
        
        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            // 设置关闭按钮
            if (closeButton != null)
            {
                closeButton.onClick.AddListener(Hide);
            }
            
            // 设置筛选选项
            if (showAllToggle != null)
            {
                showAllToggle.onValueChanged.AddListener((isOn) => {
                    if (isOn) SetFilter(AchievementFilter.All);
                });
            }
            
            if (showUnlockedToggle != null)
            {
                showUnlockedToggle.onValueChanged.AddListener((isOn) => {
                    if (isOn) SetFilter(AchievementFilter.Unlocked);
                });
            }
            
            if (showLockedToggle != null)
            {
                showLockedToggle.onValueChanged.AddListener((isOn) => {
                    if (isOn) SetFilter(AchievementFilter.Locked);
                });
            }
            
            // 设置类型筛选下拉菜单
            if (typeFilterDropdown != null)
            {
                SetupTypeFilterDropdown();
            }
            
            // 设置排序下拉菜单
            if (sortDropdown != null)
            {
                SetupSortDropdown();
            }
        }
        
        /// <summary>
        /// 设置类型筛选下拉菜单
        /// </summary>
        private void SetupTypeFilterDropdown()
        {
            typeFilterDropdown.ClearOptions();
            
            List<string> options = new List<string> { "全部类型" };
            foreach (AchievementType type in System.Enum.GetValues(typeof(AchievementType)))
            {
                options.Add(GetTypeDisplayName(type));
            }
            
            typeFilterDropdown.AddOptions(options);
            typeFilterDropdown.onValueChanged.AddListener(OnTypeFilterChanged);
        }
        
        /// <summary>
        /// 设置排序下拉菜单
        /// </summary>
        private void SetupSortDropdown()
        {
            sortDropdown.ClearOptions();
            
            List<string> options = new List<string>
            {
                "默认排序",
                "按名称排序",
                "按进度排序",
                "按类型排序"
            };
            
            sortDropdown.AddOptions(options);
            sortDropdown.onValueChanged.AddListener(OnSortChanged);
        }
        
        /// <summary>
        /// 获取类型显示名称
        /// </summary>
        private string GetTypeDisplayName(AchievementType type)
        {
            switch (type)
            {
                case AchievementType.Score: return "分数";
                case AchievementType.Collectible: return "收集";
                case AchievementType.Combo: return "连击";
                case AchievementType.Level: return "关卡";
                case AchievementType.Survival: return "生存";
                case AchievementType.Special: return "特殊";
                default: return type.ToString();
            }
        }
        
        /// <summary>
        /// 类型筛选改变
        /// </summary>
        private void OnTypeFilterChanged(int index)
        {
            RefreshAchievementList();
        }
        
        /// <summary>
        /// 排序改变
        /// </summary>
        private void OnSortChanged(int index)
        {
            currentSort = (AchievementSort)index;
            RefreshAchievementList();
        }
        
        /// <summary>
        /// 设置筛选器
        /// </summary>
        public void SetFilter(AchievementFilter filter)
        {
            currentFilter = filter;
            RefreshAchievementList();
        }
        
        /// <summary>
        /// 刷新成就列表
        /// </summary>
        public void RefreshAchievementList()
        {
            if (AchievementManager.Instance == null) return;
            
            // 清理现有项目
            ClearAchievementItems();
            
            // 获取成就列表
            List<Achievement> achievements = GetFilteredAchievements();
            
            // 排序成就
            achievements = SortAchievements(achievements);
            
            // 创建成就项目
            CreateAchievementItems(achievements);
            
            // 更新统计信息
            UpdateStatistics();
            
            if (showDebugInfo)
            {
                Debug.Log($"刷新成就列表，显示 {achievements.Count} 个成就");
            }
        }
        
        /// <summary>
        /// 获取筛选后的成就
        /// </summary>
        private List<Achievement> GetFilteredAchievements()
        {
            List<Achievement> achievements = AchievementManager.Instance.GetDisplayableAchievements();
            
            // 应用状态筛选
            switch (currentFilter)
            {
                case AchievementFilter.Unlocked:
                    achievements = achievements.Where(a => a.isUnlocked).ToList();
                    break;
                case AchievementFilter.Locked:
                    achievements = achievements.Where(a => !a.isUnlocked).ToList();
                    break;
            }
            
            // 应用类型筛选
            if (typeFilterDropdown != null && typeFilterDropdown.value > 0)
            {
                AchievementType selectedType = (AchievementType)(typeFilterDropdown.value - 1);
                achievements = achievements.Where(a => a.type == selectedType).ToList();
            }
            
            return achievements;
        }
        
        /// <summary>
        /// 排序成就
        /// </summary>
        private List<Achievement> SortAchievements(List<Achievement> achievements)
        {
            switch (currentSort)
            {
                case AchievementSort.Name:
                    return achievements.OrderBy(a => a.name).ToList();
                
                case AchievementSort.Progress:
                    return achievements.OrderByDescending(a => a.GetProgressPercentage()).ToList();
                
                case AchievementSort.Type:
                    return achievements.OrderBy(a => a.type).ThenBy(a => a.name).ToList();
                
                default:
                    return achievements;
            }
        }
        
        /// <summary>
        /// 清理成就项目
        /// </summary>
        private void ClearAchievementItems()
        {
            foreach (var item in achievementItems)
            {
                if (item != null && item.gameObject != null)
                {
                    DestroyImmediate(item.gameObject);
                }
            }
            achievementItems.Clear();
        }
        
        /// <summary>
        /// 创建成就项目
        /// </summary>
        private void CreateAchievementItems(List<Achievement> achievements)
        {
            if (achievementItemPrefab == null || achievementContainer == null) return;
            
            foreach (var achievement in achievements)
            {
                GameObject itemObj = Instantiate(achievementItemPrefab, achievementContainer);
                AchievementItem item = itemObj.GetComponent<AchievementItem>();
                
                if (item != null)
                {
                    item.Setup(achievement);
                    achievementItems.Add(item);
                }
            }
        }
        
        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            if (AchievementManager.Instance == null) return;
            
            int unlockedCount = AchievementManager.Instance.UnlockedCount;
            int totalCount = AchievementManager.Instance.TotalCount;
            float completionPercentage = totalCount > 0 ? (float)unlockedCount / totalCount : 0f;
            
            // 更新统计文本
            if (statsText != null)
            {
                statsText.text = $"成就进度: {unlockedCount}/{totalCount} ({completionPercentage:P0})";
            }
            
            // 更新进度条
            if (progressSlider != null)
            {
                progressSlider.value = completionPercentage;
            }
            
            // 更新进度文本
            if (progressText != null)
            {
                progressText.text = $"{completionPercentage:P0}";
            }
        }
        
        /// <summary>
        /// 显示面板
        /// </summary>
        public void Show()
        {
            gameObject.SetActive(true);
            RefreshAchievementList();
            
            // 滚动到顶部
            if (scrollRect != null)
            {
                scrollRect.verticalNormalizedPosition = 1f;
            }
        }
        
        /// <summary>
        /// 隐藏面板
        /// </summary>
        public void Hide()
        {
            gameObject.SetActive(false);
        }
        
        /// <summary>
        /// 切换面板显示状态
        /// </summary>
        public void Toggle()
        {
            if (gameObject.activeInHierarchy)
            {
                Hide();
            }
            else
            {
                Show();
            }
        }
    }
    
    /// <summary>
    /// 成就项目UI组件
    /// </summary>
    public class AchievementItem : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshProUGUI nameText;
        [SerializeField] private TextMeshProUGUI descriptionText;
        [SerializeField] private TextMeshProUGUI statusText;
        [SerializeField] private TextMeshProUGUI rewardText;
        [SerializeField] private Image iconImage;
        [SerializeField] private Slider progressSlider;
        [SerializeField] private GameObject unlockedIndicator;
        [SerializeField] private GameObject lockedIndicator;
        
        [Header("视觉设置")]
        [SerializeField] private Color unlockedColor = Color.white;
        [SerializeField] private Color lockedColor = Color.gray;
        
        private Achievement achievement;
        
        /// <summary>
        /// 设置成就数据
        /// </summary>
        public void Setup(Achievement achievement)
        {
            this.achievement = achievement;
            UpdateDisplay();
        }
        
        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            if (achievement == null) return;
            
            // 更新文本
            if (nameText != null)
            {
                nameText.text = achievement.name;
            }
            
            if (descriptionText != null)
            {
                descriptionText.text = achievement.description;
            }
            
            if (statusText != null)
            {
                statusText.text = achievement.GetStatusDescription();
            }
            
            if (rewardText != null)
            {
                if (achievement.scoreReward > 0)
                {
                    rewardText.text = $"奖励: {achievement.scoreReward} 分";
                    rewardText.gameObject.SetActive(true);
                }
                else
                {
                    rewardText.gameObject.SetActive(false);
                }
            }
            
            // 更新图标
            if (iconImage != null)
            {
                if (achievement.icon != null)
                {
                    iconImage.sprite = achievement.icon;
                    iconImage.gameObject.SetActive(true);
                }
                else
                {
                    iconImage.gameObject.SetActive(false);
                }
            }
            
            // 更新进度条
            if (progressSlider != null)
            {
                progressSlider.value = achievement.GetProgressPercentage();
                progressSlider.gameObject.SetActive(!achievement.isUnlocked);
            }
            
            // 更新解锁状态指示器
            if (unlockedIndicator != null)
            {
                unlockedIndicator.SetActive(achievement.isUnlocked);
            }
            
            if (lockedIndicator != null)
            {
                lockedIndicator.SetActive(!achievement.isUnlocked);
            }
            
            // 更新颜色
            UpdateColors();
        }
        
        /// <summary>
        /// 更新颜色
        /// </summary>
        private void UpdateColors()
        {
            Color targetColor = achievement.isUnlocked ? unlockedColor : lockedColor;
            
            if (nameText != null)
            {
                nameText.color = targetColor;
            }
            
            if (descriptionText != null)
            {
                descriptionText.color = targetColor;
            }
            
            if (iconImage != null)
            {
                iconImage.color = targetColor;
            }
        }
    }
}