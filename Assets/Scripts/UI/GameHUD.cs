using UnityEngine;
using UnityEngine.UI;
using TMPro;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 游戏内HUD界面
    /// 显示生命值、分数、控制按钮等游戏信息
    /// </summary>
    public class GameHUD : MonoBehaviour
    {
        [Header("生命值显示")]
        [SerializeField] private Slider healthSlider;
        [SerializeField] private TextMeshProUGUI healthText;
        [SerializeField] private Image healthFillImage;
        [SerializeField] private Color healthFullColor = Color.green;
        [SerializeField] private Color healthLowColor = Color.red;
        [SerializeField] private float healthLowThreshold = 0.3f;
        
        [Header("分数显示")]
        [SerializeField] private TextMeshProUGUI scoreText;
        [SerializeField] private TextMeshProUGUI highScoreText;
        [SerializeField] private TextMeshProUGUI comboText;
        [SerializeField] private GameObject comboPanel;
        [SerializeField] private float comboDisplayDuration = 2f;
        
        [Header("控制按钮")]
        [SerializeField] private Button jumpButton;
        [SerializeField] private Button actionButton;
        [SerializeField] private Button pauseButton;
        [SerializeField] private VirtualJoystick movementJoystick;
        
        [Header("其他UI元素")]
        [SerializeField] private TextMeshProUGUI levelText;
        [SerializeField] private GameObject collectibleCounter;
        [SerializeField] private TextMeshProUGUI collectibleText;
        
        [Header("动画设置")]
        [SerializeField] private bool enableScoreAnimation = true;
        [SerializeField] private float scoreAnimationDuration = 0.5f;
        [SerializeField] private AnimationCurve scoreAnimationCurve = new AnimationCurve(
            new Keyframe(0, 0, 0, 2),
            new Keyframe(1, 1, 0, 0)
        );
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private Health playerHealth;
        private ScoreManager scoreManager;
        
        // 状态
        private int lastDisplayedScore = 0;
        private Coroutine comboHideCoroutine;
        private Coroutine scoreAnimationCoroutine;
        
        // 事件
        public System.Action OnJumpPressed;
        public System.Action OnActionPressed;
        public System.Action OnPausePressed;
        public System.Action<Vector2> OnMovementInput;
        
        public void Initialize()
        {
            SetupComponents();
            SetupButtons();
            RegisterEvents();
            UpdateDisplay();
        }
        
        /// <summary>
        /// 设置组件引用
        /// </summary>
        private void SetupComponents()
        {
            // 查找玩家健康组件
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerHealth = player.GetComponent<Health>();
            }
            
            // 获取分数管理器
            scoreManager = ScoreManager.Instance;
            
            // 设置虚拟摇杆
            if (movementJoystick == null)
            {
                movementJoystick = GetComponentInChildren<VirtualJoystick>();
            }
        }
        
        /// <summary>
        /// 设置按钮事件
        /// </summary>
        private void SetupButtons()
        {
            if (jumpButton != null)
            {
                jumpButton.onClick.AddListener(() => OnJumpPressed?.Invoke());
            }
            
            if (actionButton != null)
            {
                actionButton.onClick.AddListener(() => OnActionPressed?.Invoke());
            }
            
            if (pauseButton != null)
            {
                pauseButton.onClick.AddListener(() => OnPausePressed?.Invoke());
            }
            
            if (movementJoystick != null)
            {
                movementJoystick.OnValueChanged += (value) => OnMovementInput?.Invoke(value);
            }
        }
        
        /// <summary>
        /// 注册事件
        /// </summary>
        private void RegisterEvents()
        {
            if (playerHealth != null)
            {
                playerHealth.OnHealthChanged += UpdateHealthDisplay;
            }
            
            if (scoreManager != null)
            {
                scoreManager.OnScoreChanged += UpdateScoreDisplay;
                scoreManager.OnHighScoreChanged += UpdateHighScoreDisplay;
                scoreManager.OnComboChanged += UpdateComboDisplay;
                scoreManager.OnItemCollected += UpdateCollectibleDisplay;
                scoreManager.OnMilestoneReached += OnScoreMilestoneReached; // 新增里程碑事件
            }
        }
        
        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            UpdateHealthDisplay();
            UpdateScoreDisplay();
            UpdateHighScoreDisplay();
            UpdateComboDisplay();
            UpdateLevelDisplay();
        }
        
        /// <summary>
        /// 更新生命值显示
        /// </summary>
        private void UpdateHealthDisplay()
        {
            if (playerHealth == null) return;
            
            UpdateHealthDisplay(playerHealth.CurrentHealth, playerHealth.MaxHealth);
        }
        
        /// <summary>
        /// 更新生命值显示
        /// </summary>
        private void UpdateHealthDisplay(int currentHealth, int maxHealth)
        {
            float healthPercentage = maxHealth > 0 ? (float)currentHealth / maxHealth : 0f;
            
            // 更新滑动条
            if (healthSlider != null)
            {
                healthSlider.value = healthPercentage;
            }
            
            // 更新文本
            if (healthText != null)
            {
                healthText.text = $"{currentHealth}/{maxHealth}";
            }
            
            // 更新颜色
            if (healthFillImage != null)
            {
                Color targetColor = healthPercentage <= healthLowThreshold ? healthLowColor : healthFullColor;
                healthFillImage.color = Color.Lerp(healthLowColor, healthFullColor, healthPercentage);
            }
        }
        
        /// <summary>
        /// 更新分数显示
        /// </summary>
        private void UpdateScoreDisplay()
        {
            if (scoreManager == null) return;
            
            UpdateScoreDisplay(scoreManager.CurrentScore);
        }
        
        /// <summary>
        /// 更新分数显示
        /// </summary>
        private void UpdateScoreDisplay(int newScore)
        {
            if (scoreText == null) return;
            
            if (enableScoreAnimation && Application.isPlaying)
            {
                if (scoreAnimationCoroutine != null)
                {
                    StopCoroutine(scoreAnimationCoroutine);
                }
                scoreAnimationCoroutine = StartCoroutine(AnimateScoreChange(lastDisplayedScore, newScore));
            }
            else
            {
                scoreText.text = $"分数: {newScore:N0}";
                lastDisplayedScore = newScore;
            }
        }
        
        /// <summary>
        /// 分数变化动画
        /// </summary>
        private System.Collections.IEnumerator AnimateScoreChange(int fromScore, int toScore)
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < scoreAnimationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / scoreAnimationDuration;
                float curveValue = scoreAnimationCurve.Evaluate(progress);
                
                int currentScore = Mathf.RoundToInt(Mathf.Lerp(fromScore, toScore, curveValue));
                scoreText.text = $"分数: {currentScore:N0}";
                
                yield return null;
            }
            
            scoreText.text = $"分数: {toScore:N0}";
            lastDisplayedScore = toScore;
            scoreAnimationCoroutine = null;
        }
        
        /// <summary>
        /// 更新最高分显示
        /// </summary>
        private void UpdateHighScoreDisplay()
        {
            if (scoreManager == null) return;
            
            UpdateHighScoreDisplay(scoreManager.HighScore);
        }
        
        /// <summary>
        /// 更新最高分显示
        /// </summary>
        private void UpdateHighScoreDisplay(int highScore)
        {
            if (highScoreText != null)
            {
                highScoreText.text = $"最高分: {highScore:N0}";
            }
        }
        
        /// <summary>
        /// 更新连击显示
        /// </summary>
        private void UpdateComboDisplay()
        {
            if (scoreManager == null) return;
            
            UpdateComboDisplay(scoreManager.ComboCount);
        }
        
        /// <summary>
        /// 更新连击显示
        /// </summary>
        private void UpdateComboDisplay(int comboCount)
        {
            if (comboText == null || comboPanel == null) return;
            
            if (comboCount > 1)
            {
                comboText.text = $"连击 x{comboCount}";
                comboPanel.SetActive(true);
                
                // 重置隐藏计时器
                if (comboHideCoroutine != null)
                {
                    StopCoroutine(comboHideCoroutine);
                }
                comboHideCoroutine = StartCoroutine(HideComboAfterDelay());
            }
            else
            {
                comboPanel.SetActive(false);
            }
        }
        
        /// <summary>
        /// 延迟隐藏连击显示
        /// </summary>
        private System.Collections.IEnumerator HideComboAfterDelay()
        {
            yield return new WaitForSecondsRealtime(comboDisplayDuration);
            
            if (comboPanel != null)
            {
                comboPanel.SetActive(false);
            }
            
            comboHideCoroutine = null;
        }
        
        /// <summary>
        /// 更新收集品显示
        /// </summary>
        private void UpdateCollectibleDisplay(CollectibleType type, int quantity)
        {
            if (collectibleText == null || scoreManager == null) return;
            
            int totalCollected = scoreManager.GetTotalCollectedItems();
            collectibleText.text = $"收集: {totalCollected}";
            
            // 提供里程碑反馈 (需求6.2)
            ShowMilestoneFeedback(type, quantity, totalCollected);
        }
        
        /// <summary>
        /// 显示里程碑反馈
        /// 实现需求6.2: 当玩家达成里程碑时，应该显示适当的反馈
        /// </summary>
        private void ShowMilestoneFeedback(CollectibleType type, int quantity, int totalCollected)
        {
            // 检查是否达到收集里程碑
            if (totalCollected > 0 && totalCollected % 10 == 0)
            {
                // 每收集10个物品显示里程碑反馈
                ShowMilestoneNotification($"收集里程碑", $"收集了 {totalCollected} 个物品!");
                
                if (showDebugInfo)
                {
                    Debug.Log($"里程碑达成: 收集了 {totalCollected} 个物品!");
                }
            }
            
            // 检查连击里程碑
            if (scoreManager != null && scoreManager.ComboCount >= 5 && scoreManager.ComboCount % 5 == 0)
            {
                // 每5次连击显示里程碑反馈
                ShowMilestoneNotification($"连击里程碑", $"{scoreManager.ComboCount} 连击!");
                
                if (showDebugInfo)
                {
                    Debug.Log($"连击里程碑: {scoreManager.ComboCount} 连击!");
                }
            }
        }
        
        /// <summary>
        /// 显示里程碑通知
        /// 实现需求6.2: 当玩家达成里程碑时，应该显示适当的反馈
        /// </summary>
        private void ShowMilestoneNotification(string title, string message)
        {
            // 这里可以显示一个简单的里程碑通知
            // 可以是一个临时的UI元素或者调用通知系统
            
            if (showDebugInfo)
            {
                Debug.Log($"[里程碑] {title}: {message}");
            }
            
            // 可以在这里添加视觉效果，如：
            // - 显示浮动文本
            // - 播放特效
            // - 触发屏幕震动
            // - 播放音效
        }
        
        /// <summary>
        /// 更新关卡显示
        /// </summary>
        private void UpdateLevelDisplay()
        {
            if (levelText == null) return;
            
            // 这里可以从LevelManager获取当前关卡信息
            levelText.text = "关卡 1";
        }
        
        /// <summary>
        /// 设置控制按钮可见性
        /// </summary>
        public void SetControlsVisible(bool visible)
        {
            if (jumpButton != null) jumpButton.gameObject.SetActive(visible);
            if (actionButton != null) actionButton.gameObject.SetActive(visible);
            if (movementJoystick != null) movementJoystick.gameObject.SetActive(visible);
        }
        
        /// <summary>
        /// 处理分数里程碑事件
        /// 实现需求6.2: 当玩家达成里程碑时，应该显示适当的反馈
        /// </summary>
        private void OnScoreMilestoneReached(int milestone)
        {
            ShowMilestoneNotification("分数里程碑", $"达到 {milestone} 分!");
        }
        
        /// <summary>
        /// 设置HUD可见性
        /// </summary>
        public void SetHUDVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }
        
        private void OnDestroy()
        {
            // 清理事件订阅
            if (playerHealth != null)
            {
                playerHealth.OnHealthChanged -= UpdateHealthDisplay;
            }
            
            if (scoreManager != null)
            {
                scoreManager.OnScoreChanged -= UpdateScoreDisplay;
                scoreManager.OnHighScoreChanged -= UpdateHighScoreDisplay;
                scoreManager.OnComboChanged -= UpdateComboDisplay;
                scoreManager.OnItemCollected -= UpdateCollectibleDisplay;
                scoreManager.OnMilestoneReached -= OnScoreMilestoneReached; // 清理里程碑事件
            }
        }
    }
}