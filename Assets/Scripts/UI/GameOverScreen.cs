using UnityEngine;
using UnityEngine.UI;
using TMPro;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 游戏结束界面
    /// 显示游戏结束信息和选项
    /// </summary>
    public class GameOverScreen : MonoBehaviour
    {
        [Header("UI元素")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI finalScoreText;
        [SerializeField] private TextMeshProUGUI highScoreText;
        [SerializeField] private TextMeshProUGUI newRecordText;
        [SerializeField] private TextMeshProUGUI statisticsText;
        
        [Header("按钮")]
        [SerializeField] private Button restartButton;
        [SerializeField] private Button mainMenuButton;
        [SerializeField] private Button shareButton;
        
        [Header("动画设置")]
        [SerializeField] private bool enableAnimation = true;
        [SerializeField] private float animationDuration = 0.5f;
        [SerializeField] private float delayBetweenElements = 0.1f;
        [SerializeField] private AnimationCurve animationCurve = new AnimationCurve(
            new Keyframe(0, 0, 0, 0),
            new Keyframe(0.7f, 1.1f, 2, 0),
            new Keyframe(1, 1, -0.5f, 0)
        );
        
        [Header("音效设置")]
        [SerializeField] private bool enableSoundEffects = true;
        [SerializeField] private AudioClip gameOverSound;
        [SerializeField] private AudioClip newRecordSound;
        [SerializeField] private AudioClip buttonClickSound;
        
        [Header("视觉效果")]
        [SerializeField] private ParticleSystem celebrationEffect;
        [SerializeField] private Color normalScoreColor = Color.white;
        [SerializeField] private Color newRecordColor = Color.gold;
        
        // 组件引用
        private CanvasGroup canvasGroup;
        private AudioSource audioSource;
        private ScoreManager scoreManager;
        
        // 状态
        private bool isNewRecord = false;
        private int finalScore = 0;
        private int previousHighScore = 0;
        
        // 事件
        public System.Action OnRestartClicked;
        public System.Action OnMainMenuClicked;
        public System.Action OnShareClicked;
        
        public void Initialize()
        {
            SetupComponents();
            SetupButtons();
            SetupText();
        }
        
        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponents()
        {
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }
            
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null && enableSoundEffects)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }
            
            scoreManager = ScoreManager.Instance;
        }
        
        /// <summary>
        /// 设置按钮事件
        /// </summary>
        private void SetupButtons()
        {
            if (restartButton != null)
            {
                restartButton.onClick.AddListener(HandleRestartClick);
            }
            
            if (mainMenuButton != null)
            {
                mainMenuButton.onClick.AddListener(HandleMainMenuClick);
            }
            
            if (shareButton != null)
            {
                shareButton.onClick.AddListener(HandleShareClick);
            }
        }
        
        /// <summary>
        /// 设置文本
        /// </summary>
        private void SetupText()
        {
            if (titleText != null)
                titleText.text = "游戏结束";
        }
        
        /// <summary>
        /// 显示游戏结束界面
        /// </summary>
        public void ShowGameOver()
        {
            gameObject.SetActive(true);
            
            // 获取分数信息
            if (scoreManager != null)
            {
                finalScore = scoreManager.CurrentScore;
                previousHighScore = scoreManager.HighScore;
                isNewRecord = finalScore > previousHighScore;
            }
            
            UpdateScoreDisplay();
            
            if (enableAnimation)
            {
                StartCoroutine(AnimateGameOverScreen());
            }
            else
            {
                SetScreenVisible(true);
            }
            
            // 播放音效
            if (isNewRecord)
            {
                PlaySound(newRecordSound);
                ShowCelebrationEffect();
            }
            else
            {
                PlaySound(gameOverSound);
            }
        }
        
        /// <summary>
        /// 更新分数显示
        /// </summary>
        private void UpdateScoreDisplay()
        {
            // 最终分数
            if (finalScoreText != null)
            {
                finalScoreText.text = $"最终分数: {finalScore:N0}";
                finalScoreText.color = isNewRecord ? newRecordColor : normalScoreColor;
            }
            
            // 最高分
            if (highScoreText != null)
            {
                int displayHighScore = isNewRecord ? finalScore : previousHighScore;
                highScoreText.text = $"最高分: {displayHighScore:N0}";
            }
            
            // 新纪录提示
            if (newRecordText != null)
            {
                newRecordText.gameObject.SetActive(isNewRecord);
                if (isNewRecord)
                {
                    newRecordText.text = "新纪录！";
                    newRecordText.color = newRecordColor;
                }
            }
            
            // 统计信息
            if (statisticsText != null && scoreManager != null)
            {
                string stats = $"收集物品: {scoreManager.GetTotalCollectedItems()}\n";
                stats += $"最高连击: {scoreManager.ComboCount}";
                statisticsText.text = stats;
            }
        }
        
        /// <summary>
        /// 游戏结束界面动画
        /// </summary>
        private System.Collections.IEnumerator AnimateGameOverScreen()
        {
            // 初始状态
            canvasGroup.alpha = 0f;
            transform.localScale = Vector3.zero;
            
            // 设置所有UI元素为不可见
            SetUIElementsVisible(false);
            
            // 主界面动画
            float elapsedTime = 0f;
            while (elapsedTime < animationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / animationDuration;
                float curveValue = animationCurve.Evaluate(progress);
                
                canvasGroup.alpha = progress;
                transform.localScale = Vector3.one * curveValue;
                
                yield return null;
            }
            
            canvasGroup.alpha = 1f;
            transform.localScale = Vector3.one;
            
            // 逐个显示UI元素
            yield return StartCoroutine(AnimateUIElements());
        }
        
        /// <summary>
        /// UI元素动画
        /// </summary>
        private System.Collections.IEnumerator AnimateUIElements()
        {
            Transform[] elements = {
                titleText?.transform,
                finalScoreText?.transform,
                highScoreText?.transform,
                newRecordText?.transform,
                statisticsText?.transform,
                restartButton?.transform,
                mainMenuButton?.transform,
                shareButton?.transform
            };
            
            foreach (var element in elements)
            {
                if (element != null)
                {
                    element.gameObject.SetActive(true);
                    
                    // 从小到大的缩放动画
                    element.localScale = Vector3.zero;
                    float elapsedTime = 0f;
                    
                    while (elapsedTime < animationDuration * 0.5f)
                    {
                        elapsedTime += Time.unscaledDeltaTime;
                        float progress = elapsedTime / (animationDuration * 0.5f);
                        float curveValue = animationCurve.Evaluate(progress);
                        
                        element.localScale = Vector3.one * curveValue;
                        
                        yield return null;
                    }
                    
                    element.localScale = Vector3.one;
                    
                    // 延迟显示下一个元素
                    yield return new WaitForSecondsRealtime(delayBetweenElements);
                }
            }
        }
        
        /// <summary>
        /// 设置UI元素可见性
        /// </summary>
        private void SetUIElementsVisible(bool visible)
        {
            if (titleText != null) titleText.gameObject.SetActive(visible);
            if (finalScoreText != null) finalScoreText.gameObject.SetActive(visible);
            if (highScoreText != null) highScoreText.gameObject.SetActive(visible);
            if (newRecordText != null) newRecordText.gameObject.SetActive(visible && isNewRecord);
            if (statisticsText != null) statisticsText.gameObject.SetActive(visible);
            if (restartButton != null) restartButton.gameObject.SetActive(visible);
            if (mainMenuButton != null) mainMenuButton.gameObject.SetActive(visible);
            if (shareButton != null) shareButton.gameObject.SetActive(visible);
        }
        
        /// <summary>
        /// 设置界面可见性
        /// </summary>
        private void SetScreenVisible(bool visible)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = visible ? 1f : 0f;
                canvasGroup.interactable = visible;
                canvasGroup.blocksRaycasts = visible;
            }
            
            transform.localScale = Vector3.one;
            SetUIElementsVisible(visible);
        }
        
        /// <summary>
        /// 显示庆祝效果
        /// </summary>
        private void ShowCelebrationEffect()
        {
            if (celebrationEffect != null)
            {
                celebrationEffect.Play();
            }
        }
        
        /// <summary>
        /// 播放音效
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (!enableSoundEffects || audioSource == null || clip == null) return;
            
            audioSource.PlayOneShot(clip);
        }
        
        #region 按钮事件处理
        
        private void HandleRestartClick()
        {
            PlaySound(buttonClickSound);
            OnRestartClicked?.Invoke();
        }
        
        private void HandleMainMenuClick()
        {
            PlaySound(buttonClickSound);
            OnMainMenuClicked?.Invoke();
        }
        
        private void HandleShareClick()
        {
            PlaySound(buttonClickSound);
            ShareScore();
            OnShareClicked?.Invoke();
        }
        
        #endregion
        
        /// <summary>
        /// 分享分数
        /// </summary>
        private void ShareScore()
        {
            string shareText = $"我在2D移动卷轴游戏中获得了 {finalScore:N0} 分！";
            if (isNewRecord)
            {
                shareText += " 这是我的新纪录！";
            }
            
            // 在移动设备上分享
            #if UNITY_ANDROID || UNITY_IOS
            // 这里可以集成原生分享功能
            Debug.Log($"分享内容: {shareText}");
            #else
            // 在编辑器中复制到剪贴板
            GUIUtility.systemCopyBuffer = shareText;
            Debug.Log($"分数已复制到剪贴板: {shareText}");
            #endif
        }
        
        /// <summary>
        /// 隐藏界面
        /// </summary>
        public void HideScreen()
        {
            gameObject.SetActive(false);
        }
        
        private void OnEnable()
        {
            if (Application.isPlaying)
            {
                ShowGameOver();
            }
        }
        
        private void OnDisable()
        {
            StopAllCoroutines();
        }
    }
}