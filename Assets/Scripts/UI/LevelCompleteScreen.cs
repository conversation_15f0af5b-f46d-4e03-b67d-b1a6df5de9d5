using UnityEngine;
using UnityEngine.UI;
using TMPro;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 关卡完成界面
    /// 显示关卡完成信息和选项
    /// </summary>
    public class LevelCompleteScreen : MonoBehaviour
    {
        [Header("UI元素")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI levelScoreText;
        [SerializeField] private TextMeshProUGUI totalScoreText;
        [SerializeField] private TextMeshProUGUI timeText;
        [SerializeField] private TextMeshProUGUI collectiblesText;
        [SerializeField] private TextMeshProUGUI bonusText;
        
        [Header("评级系统")]
        [SerializeField] private Image[] starImages;
        [SerializeField] private Color starActiveColor = Color.yellow;
        [SerializeField] private Color starInactiveColor = Color.gray;
        [SerializeField] private float starAnimationDelay = 0.2f;
        
        [Header("按钮")]
        [SerializeField] private Button nextLevelButton;
        [SerializeField] private Button restartButton;
        [SerializeField] private Button mainMenuButton;
        
        [Header("动画设置")]
        [SerializeField] private bool enableAnimation = true;
        [SerializeField] private float animationDuration = 0.5f;
        [SerializeField] private float scoreCountDuration = 2f;
        [SerializeField] private AnimationCurve animationCurve = new AnimationCurve(
            new Keyframe(0, 0, 0, 0),
            new Keyframe(0.7f, 1.1f, 2, 0),
            new Keyframe(1, 1, -0.5f, 0)
        );
        [SerializeField] private AnimationCurve scoreCountCurve = new AnimationCurve(
            new Keyframe(0, 0, 0, 2),
            new Keyframe(1, 1, 0, 0)
        );
        
        [Header("音效设置")]
        [SerializeField] private bool enableSoundEffects = true;
        [SerializeField] private AudioClip levelCompleteSound;
        [SerializeField] private AudioClip starEarnedSound;
        [SerializeField] private AudioClip scoreCountSound;
        [SerializeField] private AudioClip buttonClickSound;
        
        [Header("奖励设置")]
        [SerializeField] private int timeBonus = 100;
        [SerializeField] private int perfectBonus = 500;
        [SerializeField] private float perfectTimeThreshold = 60f;
        
        // 组件引用
        private CanvasGroup canvasGroup;
        private AudioSource audioSource;
        private ScoreManager scoreManager;
        
        // 关卡数据
        private int levelScore = 0;
        private int totalScore = 0;
        private float completionTime = 0f;
        private int collectiblesFound = 0;
        private int totalCollectibles = 0;
        private int earnedStars = 0;
        private int bonusScore = 0;
        
        // 事件
        public System.Action OnNextLevelClicked;
        public System.Action OnRestartClicked;
        public System.Action OnMainMenuClicked;
        
        public void Initialize()
        {
            SetupComponents();
            SetupButtons();
            SetupText();
            SetupStars();
        }
        
        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponents()
        {
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }
            
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null && enableSoundEffects)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }
            
            scoreManager = ScoreManager.Instance;
        }
        
        /// <summary>
        /// 设置按钮事件
        /// </summary>
        private void SetupButtons()
        {
            if (nextLevelButton != null)
            {
                nextLevelButton.onClick.AddListener(HandleNextLevelClick);
            }
            
            if (restartButton != null)
            {
                restartButton.onClick.AddListener(HandleRestartClick);
            }
            
            if (mainMenuButton != null)
            {
                mainMenuButton.onClick.AddListener(HandleMainMenuClick);
            }
        }
        
        /// <summary>
        /// 设置文本
        /// </summary>
        private void SetupText()
        {
            if (titleText != null)
                titleText.text = "关卡完成！";
        }
        
        /// <summary>
        /// 设置星星
        /// </summary>
        private void SetupStars()
        {
            if (starImages == null) return;
            
            foreach (var star in starImages)
            {
                if (star != null)
                {
                    star.color = starInactiveColor;
                }
            }
        }
        
        /// <summary>
        /// 显示关卡完成界面
        /// </summary>
        public void ShowLevelComplete(float time, int collectibles, int totalCollectiblesInLevel)
        {
            gameObject.SetActive(true);
            
            // 设置关卡数据
            completionTime = time;
            collectiblesFound = collectibles;
            totalCollectibles = totalCollectiblesInLevel;
            
            if (scoreManager != null)
            {
                levelScore = scoreManager.CurrentScore;
                totalScore = scoreManager.CurrentScore;
            }
            
            // 计算奖励和星级
            CalculateBonusAndStars();
            
            if (enableAnimation)
            {
                StartCoroutine(AnimateLevelComplete());
            }
            else
            {
                SetScreenVisible(true);
                UpdateAllDisplays();
            }
            
            PlaySound(levelCompleteSound);
        }
        
        /// <summary>
        /// 计算奖励和星级
        /// </summary>
        private void CalculateBonusAndStars()
        {
            bonusScore = 0;
            earnedStars = 1; // 基础1星（完成关卡）
            
            // 时间奖励
            if (completionTime <= perfectTimeThreshold)
            {
                bonusScore += timeBonus;
                earnedStars = Mathf.Max(earnedStars, 2);
            }
            
            // 收集品奖励
            if (collectiblesFound >= totalCollectibles && totalCollectibles > 0)
            {
                bonusScore += perfectBonus;
                earnedStars = 3; // 完美完成
            }
            else if (collectiblesFound >= totalCollectibles * 0.8f)
            {
                bonusScore += perfectBonus / 2;
                earnedStars = Mathf.Max(earnedStars, 2);
            }
            
            // 添加奖励分数
            totalScore = levelScore + bonusScore;
            if (scoreManager != null)
            {
                scoreManager.AddScore(bonusScore);
            }
        }
        
        /// <summary>
        /// 关卡完成动画
        /// </summary>
        private System.Collections.IEnumerator AnimateLevelComplete()
        {
            // 初始状态
            canvasGroup.alpha = 0f;
            transform.localScale = Vector3.zero;
            SetUIElementsVisible(false);
            
            // 主界面动画
            float elapsedTime = 0f;
            while (elapsedTime < animationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / animationDuration;
                float curveValue = animationCurve.Evaluate(progress);
                
                canvasGroup.alpha = progress;
                transform.localScale = Vector3.one * curveValue;
                
                yield return null;
            }
            
            canvasGroup.alpha = 1f;
            transform.localScale = Vector3.one;
            
            // 显示UI元素
            SetUIElementsVisible(true);
            
            // 分数计数动画
            yield return StartCoroutine(AnimateScoreCount());
            
            // 星星动画
            yield return StartCoroutine(AnimateStars());
        }
        
        /// <summary>
        /// 分数计数动画
        /// </summary>
        private System.Collections.IEnumerator AnimateScoreCount()
        {
            PlaySound(scoreCountSound);
            
            float elapsedTime = 0f;
            int startScore = 0;
            
            while (elapsedTime < scoreCountDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / scoreCountDuration;
                float curveValue = scoreCountCurve.Evaluate(progress);
                
                int currentDisplayScore = Mathf.RoundToInt(Mathf.Lerp(startScore, levelScore, curveValue));
                
                UpdateScoreDisplays(currentDisplayScore);
                
                yield return null;
            }
            
            UpdateScoreDisplays(levelScore);
            
            // 显示奖励分数
            if (bonusScore > 0)
            {
                yield return new WaitForSecondsRealtime(0.5f);
                UpdateBonusDisplay();
                UpdateTotalScoreDisplay();
            }
        }
        
        /// <summary>
        /// 星星动画
        /// </summary>
        private System.Collections.IEnumerator AnimateStars()
        {
            if (starImages == null) yield break;
            
            for (int i = 0; i < earnedStars && i < starImages.Length; i++)
            {
                if (starImages[i] != null)
                {
                    // 星星出现动画
                    starImages[i].transform.localScale = Vector3.zero;
                    starImages[i].color = starActiveColor;
                    
                    float elapsedTime = 0f;
                    while (elapsedTime < animationDuration * 0.5f)
                    {
                        elapsedTime += Time.unscaledDeltaTime;
                        float progress = elapsedTime / (animationDuration * 0.5f);
                        float curveValue = animationCurve.Evaluate(progress);
                        
                        starImages[i].transform.localScale = Vector3.one * curveValue;
                        
                        yield return null;
                    }
                    
                    starImages[i].transform.localScale = Vector3.one;
                    PlaySound(starEarnedSound);
                    
                    yield return new WaitForSecondsRealtime(starAnimationDelay);
                }
            }
        }
        
        /// <summary>
        /// 更新所有显示
        /// </summary>
        private void UpdateAllDisplays()
        {
            UpdateScoreDisplays(levelScore);
            UpdateTimeDisplay();
            UpdateCollectiblesDisplay();
            UpdateBonusDisplay();
            UpdateTotalScoreDisplay();
            UpdateStarsDisplay();
        }
        
        /// <summary>
        /// 更新分数显示
        /// </summary>
        private void UpdateScoreDisplays(int score)
        {
            if (levelScoreText != null)
            {
                levelScoreText.text = $"关卡分数: {score:N0}";
            }
        }
        
        /// <summary>
        /// 更新时间显示
        /// </summary>
        private void UpdateTimeDisplay()
        {
            if (timeText != null)
            {
                int minutes = Mathf.FloorToInt(completionTime / 60f);
                int seconds = Mathf.FloorToInt(completionTime % 60f);
                timeText.text = $"完成时间: {minutes:00}:{seconds:00}";
            }
        }
        
        /// <summary>
        /// 更新收集品显示
        /// </summary>
        private void UpdateCollectiblesDisplay()
        {
            if (collectiblesText != null)
            {
                collectiblesText.text = $"收集品: {collectiblesFound}/{totalCollectibles}";
            }
        }
        
        /// <summary>
        /// 更新奖励显示
        /// </summary>
        private void UpdateBonusDisplay()
        {
            if (bonusText != null)
            {
                bonusText.text = $"奖励分数: +{bonusScore:N0}";
                bonusText.gameObject.SetActive(bonusScore > 0);
            }
        }
        
        /// <summary>
        /// 更新总分显示
        /// </summary>
        private void UpdateTotalScoreDisplay()
        {
            if (totalScoreText != null)
            {
                totalScoreText.text = $"总分: {totalScore:N0}";
            }
        }
        
        /// <summary>
        /// 更新星星显示
        /// </summary>
        private void UpdateStarsDisplay()
        {
            if (starImages == null) return;
            
            for (int i = 0; i < starImages.Length; i++)
            {
                if (starImages[i] != null)
                {
                    starImages[i].color = i < earnedStars ? starActiveColor : starInactiveColor;
                }
            }
        }
        
        /// <summary>
        /// 设置UI元素可见性
        /// </summary>
        private void SetUIElementsVisible(bool visible)
        {
            if (titleText != null) titleText.gameObject.SetActive(visible);
            if (levelScoreText != null) levelScoreText.gameObject.SetActive(visible);
            if (totalScoreText != null) totalScoreText.gameObject.SetActive(visible);
            if (timeText != null) timeText.gameObject.SetActive(visible);
            if (collectiblesText != null) collectiblesText.gameObject.SetActive(visible);
            if (bonusText != null) bonusText.gameObject.SetActive(visible && bonusScore > 0);
            if (nextLevelButton != null) nextLevelButton.gameObject.SetActive(visible);
            if (restartButton != null) restartButton.gameObject.SetActive(visible);
            if (mainMenuButton != null) mainMenuButton.gameObject.SetActive(visible);
        }
        
        /// <summary>
        /// 设置界面可见性
        /// </summary>
        private void SetScreenVisible(bool visible)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = visible ? 1f : 0f;
                canvasGroup.interactable = visible;
                canvasGroup.blocksRaycasts = visible;
            }
            
            transform.localScale = Vector3.one;
            SetUIElementsVisible(visible);
        }
        
        /// <summary>
        /// 播放音效
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (!enableSoundEffects || audioSource == null || clip == null) return;
            
            audioSource.PlayOneShot(clip);
        }
        
        #region 按钮事件处理
        
        private void HandleNextLevelClick()
        {
            PlaySound(buttonClickSound);
            OnNextLevelClicked?.Invoke();
        }
        
        private void HandleRestartClick()
        {
            PlaySound(buttonClickSound);
            OnRestartClicked?.Invoke();
        }
        
        private void HandleMainMenuClick()
        {
            PlaySound(buttonClickSound);
            OnMainMenuClicked?.Invoke();
        }
        
        #endregion
        
        /// <summary>
        /// 隐藏界面
        /// </summary>
        public void HideScreen()
        {
            gameObject.SetActive(false);
        }
        
        private void OnEnable()
        {
            if (Application.isPlaying && completionTime > 0)
            {
                ShowLevelComplete(completionTime, collectiblesFound, totalCollectibles);
            }
        }
        
        private void OnDisable()
        {
            StopAllCoroutines();
        }
    }
}