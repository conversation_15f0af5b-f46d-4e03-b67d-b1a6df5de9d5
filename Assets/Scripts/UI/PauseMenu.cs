using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 暂停菜单
    /// 处理游戏暂停时的菜单选项
    /// </summary>
    public class PauseMenu : MonoBehaviour
    {
        [Header("菜单按钮")]
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button restartButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button mainMenuButton;
        
        [Header("菜单文本")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI resumeText;
        [SerializeField] private TextMeshP<PERSON>UGUI restartText;
        [SerializeField] private TextMeshProUGUI settingsText;
        [SerializeField] private TextMeshProUGUI mainMenuText;
        
        [Header("动画设置")]
        [SerializeField] private bool enableAnimation = true;
        [SerializeField] private float animationDuration = 0.3f;
        [SerializeField] private AnimationCurve animationCurve = new AnimationCurve(
            new Keyframe(0, 0, 0, 2),
            new Keyframe(1, 1, 0, 0)
        );
        
        [Header("音效设置")]
        [SerializeField] private bool enableSoundEffects = true;
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip menuOpenSound;
        [SerializeField] private AudioClip menuCloseSound;
        
        // 组件引用
        private CanvasGroup canvasGroup;
        private AudioSource audioSource;
        
        // 状态
        private bool isAnimating = false;
        
        // 事件
        public System.Action OnResumeClicked;
        public System.Action OnRestartClicked;
        public System.Action OnSettingsClicked;
        public System.Action OnMainMenuClicked;
        
        public void Initialize()
        {
            SetupComponents();
            SetupButtons();
            SetupText();
        }
        
        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponents()
        {
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }
            
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null && enableSoundEffects)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }
        }
        
        /// <summary>
        /// 设置按钮事件
        /// </summary>
        private void SetupButtons()
        {
            if (resumeButton != null)
            {
                resumeButton.onClick.AddListener(HandleResumeClick);
            }
            
            if (restartButton != null)
            {
                restartButton.onClick.AddListener(HandleRestartClick);
            }
            
            if (settingsButton != null)
            {
                settingsButton.onClick.AddListener(HandleSettingsClick);
            }
            
            if (mainMenuButton != null)
            {
                mainMenuButton.onClick.AddListener(HandleMainMenuClick);
            }
        }
        
        /// <summary>
        /// 设置文本
        /// </summary>
        private void SetupText()
        {
            if (titleText != null)
                titleText.text = "游戏暂停";
            
            if (resumeText != null)
                resumeText.text = "继续游戏";
            
            if (restartText != null)
                restartText.text = "重新开始";
            
            if (settingsText != null)
                settingsText.text = "设置";
            
            if (mainMenuText != null)
                mainMenuText.text = "主菜单";
        }
        
        /// <summary>
        /// 显示菜单
        /// </summary>
        public void ShowMenu()
        {
            if (isAnimating) return;
            
            gameObject.SetActive(true);
            
            if (enableAnimation)
            {
                StartCoroutine(AnimateMenu(true));
            }
            else
            {
                SetMenuVisible(true);
            }
            
            PlaySound(menuOpenSound);
        }
        
        /// <summary>
        /// 隐藏菜单
        /// </summary>
        public void HideMenu()
        {
            if (isAnimating) return;
            
            if (enableAnimation)
            {
                StartCoroutine(AnimateMenu(false));
            }
            else
            {
                SetMenuVisible(false);
                gameObject.SetActive(false);
            }
            
            PlaySound(menuCloseSound);
        }
        
        /// <summary>
        /// 菜单动画
        /// </summary>
        private System.Collections.IEnumerator AnimateMenu(bool show)
        {
            isAnimating = true;
            
            float startAlpha = show ? 0f : 1f;
            float endAlpha = show ? 1f : 0f;
            float startScale = show ? 0.8f : 1f;
            float endScale = show ? 1f : 0.8f;
            
            float elapsedTime = 0f;
            
            while (elapsedTime < animationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float progress = elapsedTime / animationDuration;
                float curveValue = animationCurve.Evaluate(progress);
                
                // 透明度动画
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = Mathf.Lerp(startAlpha, endAlpha, curveValue);
                }
                
                // 缩放动画
                float scale = Mathf.Lerp(startScale, endScale, curveValue);
                transform.localScale = Vector3.one * scale;
                
                yield return null;
            }
            
            // 确保最终状态
            if (canvasGroup != null)
            {
                canvasGroup.alpha = endAlpha;
            }
            transform.localScale = Vector3.one * endScale;
            
            if (!show)
            {
                gameObject.SetActive(false);
            }
            
            isAnimating = false;
        }
        
        /// <summary>
        /// 设置菜单可见性
        /// </summary>
        private void SetMenuVisible(bool visible)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = visible ? 1f : 0f;
                canvasGroup.interactable = visible;
                canvasGroup.blocksRaycasts = visible;
            }
            
            transform.localScale = Vector3.one;
        }
        
        /// <summary>
        /// 播放音效
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (!enableSoundEffects || audioSource == null || clip == null) return;
            
            audioSource.PlayOneShot(clip);
        }
        
        #region 按钮事件处理
        
        private void HandleResumeClick()
        {
            PlaySound(buttonClickSound);
            OnResumeClicked?.Invoke();
        }
        
        private void HandleRestartClick()
        {
            PlaySound(buttonClickSound);
            
            // 显示确认对话框
            if (ShowConfirmDialog("确定要重新开始吗？"))
            {
                OnRestartClicked?.Invoke();
            }
        }
        
        private void HandleSettingsClick()
        {
            PlaySound(buttonClickSound);
            OnSettingsClicked?.Invoke();
        }
        
        private void HandleMainMenuClick()
        {
            PlaySound(buttonClickSound);
            
            // 显示确认对话框
            if (ShowConfirmDialog("确定要返回主菜单吗？"))
            {
                OnMainMenuClicked?.Invoke();
            }
        }
        
        #endregion
        
        /// <summary>
        /// 显示确认对话框
        /// </summary>
        private bool ShowConfirmDialog(string message)
        {
            // 这里应该显示一个确认对话框
            // 暂时返回true，实际项目中应该实现对话框系统
            return true;
        }
        
        /// <summary>
        /// 设置按钮可交互性
        /// </summary>
        public void SetButtonsInteractable(bool interactable)
        {
            if (resumeButton != null) resumeButton.interactable = interactable;
            if (restartButton != null) restartButton.interactable = interactable;
            if (settingsButton != null) settingsButton.interactable = interactable;
            if (mainMenuButton != null) mainMenuButton.interactable = interactable;
        }
        
        private void OnEnable()
        {
            ShowMenu();
        }
        
        private void OnDisable()
        {
            if (isAnimating)
            {
                StopAllCoroutines();
                isAnimating = false;
            }
        }
    }
}