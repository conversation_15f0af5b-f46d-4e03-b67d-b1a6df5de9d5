using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 设置菜单
    /// 处理游戏设置选项
    /// </summary>
    public class SettingsMenu : MonoBehaviour
    {
        [Header("音频设置")]
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Slider sfxVolumeSlider;
        [SerializeField] private Toggle muteToggle;
        
        [Header("图形设置")]
        [SerializeField] private Dropdown qualityDropdown;
        [SerializeField] private Toggle fullscreenToggle;
        [SerializeField] private Dropdown resolutionDropdown;
        [SerializeField] private Toggle vsyncToggle;
        
        [Header("控制设置")]
        [SerializeField] private Slider sensitivitySlider;
        [SerializeField] private Toggle invertYToggle;
        [SerializeField] private Toggle vibrationToggle;
        
        [Header("游戏设置")]
        [SerializeField] private Dropdown languageDropdown;
        [SerializeField] private Toggle showFPSToggle;
        [SerializeField] private Toggle autoSaveToggle;
        
        [Header("按钮")]
        [SerializeField] private But<PERSON> resetButton;
        [SerializeField] private Button applyButton;
        [SerializeField] private Button backButton;
        
        [Header("文本标签")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI masterVolumeLabel;
        [SerializeField] private TextMeshProUGUI musicVolumeLabel;
        [SerializeField] private TextMeshProUGUI sfxVolumeLabel;
        [SerializeField] private TextMeshProUGUI qualityLabel;
        [SerializeField] private TextMeshProUGUI sensitivityLabel;
        
        [Header("音效设置")]
        [SerializeField] private bool enableSoundEffects = true;
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip sliderChangeSound;
        
        // 组件引用
        private AudioSource audioSource;
        
        // 设置键名
        private const string MASTER_VOLUME_KEY = "MasterVolume";
        private const string MUSIC_VOLUME_KEY = "MusicVolume";
        private const string SFX_VOLUME_KEY = "SFXVolume";
        private const string MUTE_KEY = "Mute";
        private const string QUALITY_KEY = "Quality";
        private const string FULLSCREEN_KEY = "Fullscreen";
        private const string VSYNC_KEY = "VSync";
        private const string SENSITIVITY_KEY = "Sensitivity";
        private const string INVERT_Y_KEY = "InvertY";
        private const string VIBRATION_KEY = "Vibration";
        private const string LANGUAGE_KEY = "Language";
        private const string SHOW_FPS_KEY = "ShowFPS";
        private const string AUTO_SAVE_KEY = "AutoSave";
        
        // 事件
        public System.Action OnBackClicked;
        public System.Action OnSettingsChanged;
        
        public void Initialize()
        {
            SetupComponents();
            SetupButtons();
            SetupText();
            LoadSettings();
        }
        
        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponents()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null && enableSoundEffects)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
            }
        }
        
        /// <summary>
        /// 设置按钮事件
        /// </summary>
        private void SetupButtons()
        {
            if (resetButton != null)
            {
                resetButton.onClick.AddListener(HandleResetClick);
            }
            
            if (applyButton != null)
            {
                applyButton.onClick.AddListener(HandleApplyClick);
            }
            
            if (backButton != null)
            {
                backButton.onClick.AddListener(HandleBackClick);
            }
            
            // 设置滑动条事件
            SetupSliderEvents();
            
            // 设置切换按钮事件
            SetupToggleEvents();
            
            // 设置下拉菜单事件
            SetupDropdownEvents();
        }
        
        /// <summary>
        /// 设置滑动条事件
        /// </summary>
        private void SetupSliderEvents()
        {
            if (masterVolumeSlider != null)
            {
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
            }
            
            if (musicVolumeSlider != null)
            {
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            }
            
            if (sfxVolumeSlider != null)
            {
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
            }
            
            if (sensitivitySlider != null)
            {
                sensitivitySlider.onValueChanged.AddListener(OnSensitivityChanged);
            }
        }
        
        /// <summary>
        /// 设置切换按钮事件
        /// </summary>
        private void SetupToggleEvents()
        {
            if (muteToggle != null)
            {
                muteToggle.onValueChanged.AddListener(OnMuteChanged);
            }
            
            if (fullscreenToggle != null)
            {
                fullscreenToggle.onValueChanged.AddListener(OnFullscreenChanged);
            }
            
            if (vsyncToggle != null)
            {
                vsyncToggle.onValueChanged.AddListener(OnVSyncChanged);
            }
            
            if (invertYToggle != null)
            {
                invertYToggle.onValueChanged.AddListener(OnInvertYChanged);
            }
            
            if (vibrationToggle != null)
            {
                vibrationToggle.onValueChanged.AddListener(OnVibrationChanged);
            }
            
            if (showFPSToggle != null)
            {
                showFPSToggle.onValueChanged.AddListener(OnShowFPSChanged);
            }
            
            if (autoSaveToggle != null)
            {
                autoSaveToggle.onValueChanged.AddListener(OnAutoSaveChanged);
            }
        }
        
        /// <summary>
        /// 设置下拉菜单事件
        /// </summary>
        private void SetupDropdownEvents()
        {
            if (qualityDropdown != null)
            {
                qualityDropdown.onValueChanged.AddListener(OnQualityChanged);
            }
            
            if (resolutionDropdown != null)
            {
                resolutionDropdown.onValueChanged.AddListener(OnResolutionChanged);
            }
            
            if (languageDropdown != null)
            {
                languageDropdown.onValueChanged.AddListener(OnLanguageChanged);
            }
        }
        
        /// <summary>
        /// 设置文本
        /// </summary>
        private void SetupText()
        {
            if (titleText != null)
                titleText.text = "设置";
            
            if (masterVolumeLabel != null)
                masterVolumeLabel.text = "主音量";
            
            if (musicVolumeLabel != null)
                musicVolumeLabel.text = "音乐音量";
            
            if (sfxVolumeLabel != null)
                sfxVolumeLabel.text = "音效音量";
            
            if (qualityLabel != null)
                qualityLabel.text = "图形质量";
            
            if (sensitivityLabel != null)
                sensitivityLabel.text = "控制灵敏度";
        }
        
        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            // 音频设置
            if (masterVolumeSlider != null)
                masterVolumeSlider.value = PlayerPrefs.GetFloat(MASTER_VOLUME_KEY, 1f);
            
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = PlayerPrefs.GetFloat(MUSIC_VOLUME_KEY, 1f);
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = PlayerPrefs.GetFloat(SFX_VOLUME_KEY, 1f);
            
            if (muteToggle != null)
                muteToggle.isOn = PlayerPrefs.GetInt(MUTE_KEY, 0) == 1;
            
            // 图形设置
            if (qualityDropdown != null)
                qualityDropdown.value = PlayerPrefs.GetInt(QUALITY_KEY, QualitySettings.GetQualityLevel());
            
            if (fullscreenToggle != null)
                fullscreenToggle.isOn = PlayerPrefs.GetInt(FULLSCREEN_KEY, Screen.fullScreen ? 1 : 0) == 1;
            
            if (vsyncToggle != null)
                vsyncToggle.isOn = PlayerPrefs.GetInt(VSYNC_KEY, QualitySettings.vSyncCount > 0 ? 1 : 0) == 1;
            
            // 控制设置
            if (sensitivitySlider != null)
                sensitivitySlider.value = PlayerPrefs.GetFloat(SENSITIVITY_KEY, 1f);
            
            if (invertYToggle != null)
                invertYToggle.isOn = PlayerPrefs.GetInt(INVERT_Y_KEY, 0) == 1;
            
            if (vibrationToggle != null)
                vibrationToggle.isOn = PlayerPrefs.GetInt(VIBRATION_KEY, 1) == 1;
            
            // 游戏设置
            if (languageDropdown != null)
                languageDropdown.value = PlayerPrefs.GetInt(LANGUAGE_KEY, 0);
            
            if (showFPSToggle != null)
                showFPSToggle.isOn = PlayerPrefs.GetInt(SHOW_FPS_KEY, 0) == 1;
            
            if (autoSaveToggle != null)
                autoSaveToggle.isOn = PlayerPrefs.GetInt(AUTO_SAVE_KEY, 1) == 1;
        }
        
        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            // 音频设置
            if (masterVolumeSlider != null)
                PlayerPrefs.SetFloat(MASTER_VOLUME_KEY, masterVolumeSlider.value);
            
            if (musicVolumeSlider != null)
                PlayerPrefs.SetFloat(MUSIC_VOLUME_KEY, musicVolumeSlider.value);
            
            if (sfxVolumeSlider != null)
                PlayerPrefs.SetFloat(SFX_VOLUME_KEY, sfxVolumeSlider.value);
            
            if (muteToggle != null)
                PlayerPrefs.SetInt(MUTE_KEY, muteToggle.isOn ? 1 : 0);
            
            // 图形设置
            if (qualityDropdown != null)
                PlayerPrefs.SetInt(QUALITY_KEY, qualityDropdown.value);
            
            if (fullscreenToggle != null)
                PlayerPrefs.SetInt(FULLSCREEN_KEY, fullscreenToggle.isOn ? 1 : 0);
            
            if (vsyncToggle != null)
                PlayerPrefs.SetInt(VSYNC_KEY, vsyncToggle.isOn ? 1 : 0);
            
            // 控制设置
            if (sensitivitySlider != null)
                PlayerPrefs.SetFloat(SENSITIVITY_KEY, sensitivitySlider.value);
            
            if (invertYToggle != null)
                PlayerPrefs.SetInt(INVERT_Y_KEY, invertYToggle.isOn ? 1 : 0);
            
            if (vibrationToggle != null)
                PlayerPrefs.SetInt(VIBRATION_KEY, vibrationToggle.isOn ? 1 : 0);
            
            // 游戏设置
            if (languageDropdown != null)
                PlayerPrefs.SetInt(LANGUAGE_KEY, languageDropdown.value);
            
            if (showFPSToggle != null)
                PlayerPrefs.SetInt(SHOW_FPS_KEY, showFPSToggle.isOn ? 1 : 0);
            
            if (autoSaveToggle != null)
                PlayerPrefs.SetInt(AUTO_SAVE_KEY, autoSaveToggle.isOn ? 1 : 0);
            
            PlayerPrefs.Save();
        }
        
        /// <summary>
        /// 应用设置
        /// </summary>
        private void ApplySettings()
        {
            // 应用音频设置
            ApplyAudioSettings();
            
            // 应用图形设置
            ApplyGraphicsSettings();
            
            // 触发设置改变事件
            OnSettingsChanged?.Invoke();
        }
        
        /// <summary>
        /// 应用音频设置
        /// </summary>
        private void ApplyAudioSettings()
        {
            float masterVolume = masterVolumeSlider != null ? masterVolumeSlider.value : 1f;
            float musicVolume = musicVolumeSlider != null ? musicVolumeSlider.value : 1f;
            float sfxVolume = sfxVolumeSlider != null ? sfxVolumeSlider.value : 1f;
            bool isMuted = muteToggle != null && muteToggle.isOn;
            
            // 设置音频混合器音量
            AudioListener.volume = isMuted ? 0f : masterVolume;
            
            // 这里可以设置具体的音频混合器组
            // audioMixer.SetFloat("MasterVolume", Mathf.Log10(masterVolume) * 20);
            // audioMixer.SetFloat("MusicVolume", Mathf.Log10(musicVolume) * 20);
            // audioMixer.SetFloat("SFXVolume", Mathf.Log10(sfxVolume) * 20);
        }
        
        /// <summary>
        /// 应用图形设置
        /// </summary>
        private void ApplyGraphicsSettings()
        {
            if (qualityDropdown != null)
            {
                QualitySettings.SetQualityLevel(qualityDropdown.value);
            }
            
            if (fullscreenToggle != null)
            {
                Screen.fullScreen = fullscreenToggle.isOn;
            }
            
            if (vsyncToggle != null)
            {
                QualitySettings.vSyncCount = vsyncToggle.isOn ? 1 : 0;
            }
        }
        
        /// <summary>
        /// 重置为默认设置
        /// </summary>
        private void ResetToDefaults()
        {
            // 音频默认值
            if (masterVolumeSlider != null) masterVolumeSlider.value = 1f;
            if (musicVolumeSlider != null) musicVolumeSlider.value = 1f;
            if (sfxVolumeSlider != null) sfxVolumeSlider.value = 1f;
            if (muteToggle != null) muteToggle.isOn = false;
            
            // 图形默认值
            if (qualityDropdown != null) qualityDropdown.value = 2; // 中等质量
            if (fullscreenToggle != null) fullscreenToggle.isOn = true;
            if (vsyncToggle != null) vsyncToggle.isOn = true;
            
            // 控制默认值
            if (sensitivitySlider != null) sensitivitySlider.value = 1f;
            if (invertYToggle != null) invertYToggle.isOn = false;
            if (vibrationToggle != null) vibrationToggle.isOn = true;
            
            // 游戏默认值
            if (languageDropdown != null) languageDropdown.value = 0;
            if (showFPSToggle != null) showFPSToggle.isOn = false;
            if (autoSaveToggle != null) autoSaveToggle.isOn = true;
        }
        
        /// <summary>
        /// 播放音效
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (!enableSoundEffects || audioSource == null || clip == null) return;
            
            audioSource.PlayOneShot(clip);
        }
        
        #region 设置改变事件处理
        
        private void OnMasterVolumeChanged(float value)
        {
            PlaySound(sliderChangeSound);
            ApplyAudioSettings();
        }
        
        private void OnMusicVolumeChanged(float value)
        {
            PlaySound(sliderChangeSound);
            ApplyAudioSettings();
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            PlaySound(sliderChangeSound);
            ApplyAudioSettings();
        }
        
        private void OnMuteChanged(bool value)
        {
            PlaySound(buttonClickSound);
            ApplyAudioSettings();
        }
        
        private void OnQualityChanged(int value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnFullscreenChanged(bool value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnVSyncChanged(bool value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnResolutionChanged(int value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnSensitivityChanged(float value)
        {
            PlaySound(sliderChangeSound);
        }
        
        private void OnInvertYChanged(bool value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnVibrationChanged(bool value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnLanguageChanged(int value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnShowFPSChanged(bool value)
        {
            PlaySound(buttonClickSound);
        }
        
        private void OnAutoSaveChanged(bool value)
        {
            PlaySound(buttonClickSound);
        }
        
        #endregion
        
        #region 按钮事件处理
        
        private void HandleResetClick()
        {
            PlaySound(buttonClickSound);
            ResetToDefaults();
        }
        
        private void HandleApplyClick()
        {
            PlaySound(buttonClickSound);
            SaveSettings();
            ApplySettings();
        }
        
        private void HandleBackClick()
        {
            PlaySound(buttonClickSound);
            SaveSettings();
            ApplySettings();
            OnBackClicked?.Invoke();
        }
        
        #endregion
        
        private void OnEnable()
        {
            LoadSettings();
        }
        
        private void OnDisable()
        {
            SaveSettings();
        }
    }
}