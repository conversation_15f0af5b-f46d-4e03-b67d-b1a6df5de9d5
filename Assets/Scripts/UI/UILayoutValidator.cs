using UnityEngine;
using UnityEngine.UI;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// UI布局验证器
    /// 确保UI元素不会遮挡重要的游戏区域
    /// 实现需求6.6: 当显示UI元素时，不应该遮挡重要的游戏区域
    /// </summary>
    public class UILayoutValidator : MonoBehaviour
    {
        [Header("游戏区域设置")]
        [SerializeField] private RectTransform gameArea;
        [SerializeField] private float gameAreaMargin = 50f;
        
        [Header("UI元素")]
        [SerializeField] private RectTransform[] uiElements;
        
        [Header("验证设置")]
        [SerializeField] private bool validateOnStart = true;
        [SerializeField] private bool showDebugInfo = false;
        
        // 游戏区域边界
        private Rect gameAreaBounds;
        
        private void Start()
        {
            if (validateOnStart)
            {
                ValidateLayout();
            }
        }
        
        /// <summary>
        /// 验证UI布局
        /// </summary>
        public void ValidateLayout()
        {
            CalculateGameAreaBounds();
            ValidateUIElements();
        }
        
        /// <summary>
        /// 计算游戏区域边界
        /// </summary>
        private void CalculateGameAreaBounds()
        {
            if (gameArea != null)
            {
                // 使用指定的游戏区域
                Vector3[] corners = new Vector3[4];
                gameArea.GetWorldCorners(corners);
                
                float minX = corners[0].x + gameAreaMargin;
                float minY = corners[0].y + gameAreaMargin;
                float maxX = corners[2].x - gameAreaMargin;
                float maxY = corners[2].y - gameAreaMargin;
                
                gameAreaBounds = new Rect(minX, minY, maxX - minX, maxY - minY);
            }
            else
            {
                // 使用屏幕中心区域作为游戏区域
                float screenWidth = Screen.width;
                float screenHeight = Screen.height;
                
                float centerWidth = screenWidth * 0.6f; // 中心60%区域
                float centerHeight = screenHeight * 0.6f;
                
                float centerX = (screenWidth - centerWidth) * 0.5f;
                float centerY = (screenHeight - centerHeight) * 0.5f;
                
                gameAreaBounds = new Rect(centerX, centerY, centerWidth, centerHeight);
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"游戏区域边界: {gameAreaBounds}");
            }
        }
        
        /// <summary>
        /// 验证UI元素
        /// </summary>
        private void ValidateUIElements()
        {
            if (uiElements == null) return;
            
            foreach (var uiElement in uiElements)
            {
                if (uiElement != null)
                {
                    ValidateUIElement(uiElement);
                }
            }
        }
        
        /// <summary>
        /// 验证单个UI元素
        /// </summary>
        private void ValidateUIElement(RectTransform uiElement)
        {
            Vector3[] corners = new Vector3[4];
            uiElement.GetWorldCorners(corners);
            
            Rect elementBounds = new Rect(
                corners[0].x,
                corners[0].y,
                corners[2].x - corners[0].x,
                corners[2].y - corners[0].y
            );
            
            // 检查是否与游戏区域重叠
            bool overlaps = gameAreaBounds.Overlaps(elementBounds);
            
            if (overlaps)
            {
                Debug.LogWarning($"UI元素 {uiElement.name} 可能遮挡游戏区域!");
                
                if (showDebugInfo)
                {
                    Debug.Log($"元素边界: {elementBounds}");
                    Debug.Log($"游戏区域: {gameAreaBounds}");
                }
                
                // 尝试自动调整位置
                AdjustUIElementPosition(uiElement, elementBounds);
            }
            else if (showDebugInfo)
            {
                Debug.Log($"UI元素 {uiElement.name} 位置正确，不遮挡游戏区域");
            }
        }
        
        /// <summary>
        /// 调整UI元素位置
        /// </summary>
        private void AdjustUIElementPosition(RectTransform uiElement, Rect elementBounds)
        {
            // 简单的调整策略：将元素移动到屏幕边缘
            Vector2 anchoredPosition = uiElement.anchoredPosition;
            
            // 检查元素更适合放在哪个边缘
            float distanceToLeft = elementBounds.center.x;
            float distanceToRight = Screen.width - elementBounds.center.x;
            float distanceToTop = Screen.height - elementBounds.center.y;
            float distanceToBottom = elementBounds.center.y;
            
            float minDistance = Mathf.Min(distanceToLeft, distanceToRight, distanceToTop, distanceToBottom);
            
            if (minDistance == distanceToLeft)
            {
                // 移动到左边缘
                uiElement.anchorMin = new Vector2(0, uiElement.anchorMin.y);
                uiElement.anchorMax = new Vector2(0, uiElement.anchorMax.y);
                uiElement.anchoredPosition = new Vector2(elementBounds.width * 0.5f, anchoredPosition.y);
            }
            else if (minDistance == distanceToRight)
            {
                // 移动到右边缘
                uiElement.anchorMin = new Vector2(1, uiElement.anchorMin.y);
                uiElement.anchorMax = new Vector2(1, uiElement.anchorMax.y);
                uiElement.anchoredPosition = new Vector2(-elementBounds.width * 0.5f, anchoredPosition.y);
            }
            else if (minDistance == distanceToTop)
            {
                // 移动到顶部边缘
                uiElement.anchorMin = new Vector2(uiElement.anchorMin.x, 1);
                uiElement.anchorMax = new Vector2(uiElement.anchorMax.x, 1);
                uiElement.anchoredPosition = new Vector2(anchoredPosition.x, -elementBounds.height * 0.5f);
            }
            else
            {
                // 移动到底部边缘
                uiElement.anchorMin = new Vector2(uiElement.anchorMin.x, 0);
                uiElement.anchorMax = new Vector2(uiElement.anchorMax.x, 0);
                uiElement.anchoredPosition = new Vector2(anchoredPosition.x, elementBounds.height * 0.5f);
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"已调整UI元素 {uiElement.name} 的位置");
            }
        }
        
        /// <summary>
        /// 设置游戏区域
        /// </summary>
        public void SetGameArea(RectTransform newGameArea)
        {
            gameArea = newGameArea;
            ValidateLayout();
        }
        
        /// <summary>
        /// 添加UI元素进行验证
        /// </summary>
        public void AddUIElement(RectTransform uiElement)
        {
            if (uiElement == null) return;
            
            // 扩展数组
            RectTransform[] newArray = new RectTransform[uiElements.Length + 1];
            System.Array.Copy(uiElements, newArray, uiElements.Length);
            newArray[uiElements.Length] = uiElement;
            uiElements = newArray;
            
            // 验证新添加的元素
            ValidateUIElement(uiElement);
        }
        
        private void OnDrawGizmosSelected()
        {
            if (Application.isPlaying)
            {
                // 绘制游戏区域边界
                Gizmos.color = Color.green;
                Vector3 center = new Vector3(gameAreaBounds.center.x, gameAreaBounds.center.y, 0);
                Vector3 size = new Vector3(gameAreaBounds.width, gameAreaBounds.height, 0);
                Gizmos.DrawWireCube(center, size);
            }
        }
    }
}