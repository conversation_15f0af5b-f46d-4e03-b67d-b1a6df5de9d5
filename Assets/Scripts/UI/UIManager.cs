using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityInput = UnityEngine.Input;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// UI管理器
    /// 管理游戏内所有UI界面和交互
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("游戏内UI")]
        [SerializeField] private GameHUD gameHUD;
        [SerializeField] private PauseMenu pauseMenu;
        [SerializeField] private GameOverScreen gameOverScreen;
        [SerializeField] private LevelCompleteScreen levelCompleteScreen;
        [SerializeField] private SettingsMenu settingsMenu;
        
        [Header("UI面板")]
        [SerializeField] private GameObject hudPanel;
        [SerializeField] private GameObject pausePanel;
        [SerializeField] private GameObject gameOverPanel;
        [SerializeField] private GameObject levelCompletePanel;
        [SerializeField] private GameObject settingsPanel;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 状态
        private UIState currentState = UIState.Game;
        private UIState previousState = UIState.Game;
        
        // 事件
        public System.Action<UIState> OnUIStateChanged;
        public System.Action OnPauseRequested;
        public System.Action OnResumeRequested;
        public System.Action OnRestartRequested;
        public System.Action OnMainMenuRequested;
        
        // 属性
        public UIState CurrentState => currentState;
        public bool IsPaused => currentState == UIState.Pause;
        
        private void Awake()
        {
            InitializeUI();
        }
        
        private void Start()
        {
            SetUIState(UIState.Game);
            ValidateUILayout();
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 确保所有UI组件都存在
            if (gameHUD == null) gameHUD = GetComponentInChildren<GameHUD>();
            if (pauseMenu == null) pauseMenu = GetComponentInChildren<PauseMenu>();
            if (gameOverScreen == null) gameOverScreen = GetComponentInChildren<GameOverScreen>();
            if (levelCompleteScreen == null) levelCompleteScreen = GetComponentInChildren<LevelCompleteScreen>();
            if (settingsMenu == null) settingsMenu = GetComponentInChildren<SettingsMenu>();
            
            // 初始化UI组件
            gameHUD?.Initialize();
            pauseMenu?.Initialize();
            gameOverScreen?.Initialize();
            levelCompleteScreen?.Initialize();
            settingsMenu?.Initialize();
            
            // 注册事件
            RegisterEvents();
        }
        
        /// <summary>
        /// 注册事件
        /// </summary>
        private void RegisterEvents()
        {
            if (pauseMenu != null)
            {
                pauseMenu.OnResumeClicked += HandleResumeClicked;
                pauseMenu.OnRestartClicked += HandleRestartClicked;
                pauseMenu.OnSettingsClicked += HandleSettingsClicked;
                pauseMenu.OnMainMenuClicked += HandleMainMenuClicked;
            }
            
            if (gameOverScreen != null)
            {
                gameOverScreen.OnRestartClicked += HandleRestartClicked;
                gameOverScreen.OnMainMenuClicked += HandleMainMenuClicked;
            }
            
            if (levelCompleteScreen != null)
            {
                levelCompleteScreen.OnNextLevelClicked += HandleNextLevelClicked;
                levelCompleteScreen.OnRestartClicked += HandleRestartClicked;
                levelCompleteScreen.OnMainMenuClicked += HandleMainMenuClicked;
            }
            
            if (settingsMenu != null)
            {
                settingsMenu.OnBackClicked += HandleSettingsBackClicked;
            }
        }
        
        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            // ESC键或返回键暂停/恢复游戏
            if (UnityInput.GetKeyDown(KeyCode.Escape) || UnityInput.GetKeyDown(KeyCode.Menu))
            {
                if (currentState == UIState.Game)
                {
                    PauseGame();
                }
                else if (currentState == UIState.Pause)
                {
                    ResumeGame();
                }
                else if (currentState == UIState.Settings)
                {
                    SetUIState(previousState);
                }
            }
        }
        
        /// <summary>
        /// 设置UI状态
        /// </summary>
        public void SetUIState(UIState newState)
        {
            if (currentState == newState) return;
            
            previousState = currentState;
            currentState = newState;
            
            UpdateUIVisibility();
            OnUIStateChanged?.Invoke(currentState);
            
            if (showDebugInfo)
            {
                Debug.Log($"UI状态改变: {previousState} -> {currentState}");
            }
        }
        
        /// <summary>
        /// 更新UI可见性
        /// </summary>
        private void UpdateUIVisibility()
        {
            // 隐藏所有面板
            SetPanelActive(hudPanel, false);
            SetPanelActive(pausePanel, false);
            SetPanelActive(gameOverPanel, false);
            SetPanelActive(levelCompletePanel, false);
            SetPanelActive(settingsPanel, false);
            
            // 根据状态显示对应面板
            switch (currentState)
            {
                case UIState.Game:
                    SetPanelActive(hudPanel, true);
                    Time.timeScale = 1f;
                    break;
                    
                case UIState.Pause:
                    SetPanelActive(hudPanel, true);
                    SetPanelActive(pausePanel, true);
                    Time.timeScale = 0f;
                    break;
                    
                case UIState.GameOver:
                    SetPanelActive(gameOverPanel, true);
                    Time.timeScale = 0f;
                    break;
                    
                case UIState.LevelComplete:
                    SetPanelActive(levelCompletePanel, true);
                    Time.timeScale = 0f;
                    break;
                    
                case UIState.Settings:
                    SetPanelActive(settingsPanel, true);
                    Time.timeScale = 0f;
                    break;
            }
        }
        
        /// <summary>
        /// 设置面板激活状态
        /// </summary>
        private void SetPanelActive(GameObject panel, bool active)
        {
            if (panel != null)
            {
                panel.SetActive(active);
            }
        }
        
        /// <summary>
        /// 暂停游戏
        /// </summary>
        public void PauseGame()
        {
            SetUIState(UIState.Pause);
            OnPauseRequested?.Invoke();
        }
        
        /// <summary>
        /// 恢复游戏
        /// </summary>
        public void ResumeGame()
        {
            SetUIState(UIState.Game);
            OnResumeRequested?.Invoke();
        }
        
        /// <summary>
        /// 显示游戏结束界面
        /// </summary>
        public void ShowGameOver()
        {
            SetUIState(UIState.GameOver);
        }
        
        /// <summary>
        /// 显示关卡完成界面
        /// </summary>
        public void ShowLevelComplete()
        {
            SetUIState(UIState.LevelComplete);
        }
        
        /// <summary>
        /// 显示设置界面
        /// </summary>
        public void ShowSettings()
        {
            SetUIState(UIState.Settings);
        }
        
        #region 事件处理
        
        private void HandleResumeClicked()
        {
            ResumeGame();
        }
        
        private void HandleRestartClicked()
        {
            OnRestartRequested?.Invoke();
            SetUIState(UIState.Game);
        }
        
        private void HandleSettingsClicked()
        {
            ShowSettings();
        }
        
        private void HandleMainMenuClicked()
        {
            OnMainMenuRequested?.Invoke();
        }
        
        private void HandleNextLevelClicked()
        {
            // 这里应该触发下一关卡加载
            SetUIState(UIState.Game);
        }
        
        private void HandleSettingsBackClicked()
        {
            SetUIState(previousState);
        }
        
        #endregion
        
        /// <summary>
        /// 验证UI布局不会遮挡重要游戏区域
        /// 实现需求6.6: 当显示UI元素时，不应该遮挡重要的游戏区域
        /// </summary>
        public void ValidateUILayout()
        {
            // 确保游戏HUD元素位于屏幕边缘，不遮挡中心游戏区域
            if (gameHUD != null)
            {
                // 游戏HUD应该只占用屏幕边缘区域
                RectTransform hudRect = gameHUD.GetComponent<RectTransform>();
                if (hudRect != null)
                {
                    // 确保HUD不会占用屏幕中心区域
                    // 这里可以添加具体的布局验证逻辑
                    if (showDebugInfo)
                    {
                        Debug.Log("UI布局验证: GameHUD位置正确");
                    }
                }
            }
            
            // 确保控制按钮位于合适位置
            if (gameHUD != null)
            {
                // 虚拟控制应该位于屏幕底部角落
                gameHUD.SetControlsVisible(true);
            }
        }
        
        /// <summary>
        /// 获取当前UI状态信息
        /// </summary>
        public string GetUIStateInfo()
        {
            return $"当前状态: {currentState}, 上一状态: {previousState}, 是否暂停: {IsPaused}";
        }
        
        #region 静态实例管理
        
        private static UIManager instance;
        public static UIManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<UIManager>();
                }
                return instance;
            }
        }
        
        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// UI状态枚举
    /// </summary>
    public enum UIState
    {
        Game,           // 游戏中
        Pause,          // 暂停
        GameOver,       // 游戏结束
        LevelComplete,  // 关卡完成
        Settings        // 设置
    }
}