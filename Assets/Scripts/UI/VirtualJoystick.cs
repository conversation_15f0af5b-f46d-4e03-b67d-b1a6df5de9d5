using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace MobileScrollingGame.UI
{
    /// <summary>
    /// 虚拟摇杆UI组件
    /// 提供移动设备上的虚拟摇杆控制
    /// </summary>
    public class VirtualJoystick : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpH<PERSON>ler, IDragHandler
    {
        [Header("摇杆组件")]
        [SerializeField] private RectTransform joystickBackground;
        [SerializeField] private RectTransform joystickHandle;
        
        [Header("摇杆设置")]
        [SerializeField] private float handleRange = 50f;
        [SerializeField] private bool snapToCenter = true;
        [SerializeField] private float snapSpeed = 10f;
        
        [Header("输入设置")]
        [SerializeField] private float deadZone = 0.1f;
        [SerializeField] private bool invertX = false;
        [SerializeField] private bool invertY = false;
        
        [Header("视觉反馈")]
        [SerializeField] private bool enableVisualFeedback = true;
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color activeColor = Color.yellow;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 状态
        private Vector2 inputVector = Vector2.zero;
        private bool isDragging = false;
        private Vector2 centerPosition;
        
        // 组件引用
        private Image backgroundImage;
        private Image handleImage;
        
        // 事件
        public System.Action<Vector2> OnValueChanged;
        
        // 属性
        public Vector2 InputVector => inputVector;
        public bool IsDragging => isDragging;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            SetupJoystick();
        }
        
        private void Update()
        {
            if (!isDragging && snapToCenter)
            {
                SnapHandleToCenter();
            }
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            if (joystickBackground == null)
            {
                joystickBackground = transform as RectTransform;
            }
            
            if (joystickHandle == null)
            {
                joystickHandle = transform.GetChild(0) as RectTransform;
            }
            
            backgroundImage = joystickBackground.GetComponent<Image>();
            handleImage = joystickHandle.GetComponent<Image>();
            
            centerPosition = joystickBackground.anchoredPosition;
        }
        
        /// <summary>
        /// 设置摇杆
        /// </summary>
        private void SetupJoystick()
        {
            // 确保摇杆手柄在中心位置
            if (joystickHandle != null)
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
            
            // 设置初始颜色
            UpdateVisualFeedback(false);
        }
        
        /// <summary>
        /// 处理指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            isDragging = true;
            UpdateJoystickPosition(eventData);
            UpdateVisualFeedback(true);
            
            if (showDebugInfo)
            {
                Debug.Log("虚拟摇杆开始拖拽");
            }
        }
        
        /// <summary>
        /// 处理指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            isDragging = false;
            
            if (snapToCenter)
            {
                ResetJoystick();
            }
            
            UpdateVisualFeedback(false);
            
            if (showDebugInfo)
            {
                Debug.Log("虚拟摇杆结束拖拽");
            }
        }
        
        /// <summary>
        /// 处理拖拽事件
        /// </summary>
        public void OnDrag(PointerEventData eventData)
        {
            if (isDragging)
            {
                UpdateJoystickPosition(eventData);
            }
        }
        
        /// <summary>
        /// 更新摇杆位置
        /// </summary>
        private void UpdateJoystickPosition(PointerEventData eventData)
        {
            Vector2 localPoint;
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                joystickBackground, eventData.position, eventData.pressEventCamera, out localPoint))
            {
                // 限制在摇杆范围内
                Vector2 clampedPosition = Vector2.ClampMagnitude(localPoint, handleRange);
                
                // 更新手柄位置
                if (joystickHandle != null)
                {
                    joystickHandle.anchoredPosition = clampedPosition;
                }
                
                // 计算输入向量
                CalculateInputVector(clampedPosition);
            }
        }
        
        /// <summary>
        /// 计算输入向量
        /// </summary>
        private void CalculateInputVector(Vector2 handlePosition)
        {
            Vector2 rawInput = handlePosition / handleRange;
            
            // 应用死区
            if (rawInput.magnitude < deadZone)
            {
                rawInput = Vector2.zero;
            }
            else
            {
                // 重新映射到死区外的范围
                rawInput = rawInput.normalized * ((rawInput.magnitude - deadZone) / (1f - deadZone));
            }
            
            // 应用反转设置
            if (invertX) rawInput.x = -rawInput.x;
            if (invertY) rawInput.y = -rawInput.y;
            
            inputVector = rawInput;
            
            // 触发事件
            OnValueChanged?.Invoke(inputVector);
            
            if (showDebugInfo && inputVector.magnitude > 0.1f)
            {
                Debug.Log($"摇杆输入: {inputVector}");
            }
        }
        
        /// <summary>
        /// 将手柄回到中心
        /// </summary>
        private void SnapHandleToCenter()
        {
            if (joystickHandle != null && joystickHandle.anchoredPosition.magnitude > 0.1f)
            {
                Vector2 currentPosition = joystickHandle.anchoredPosition;
                Vector2 targetPosition = Vector2.zero;
                
                joystickHandle.anchoredPosition = Vector2.Lerp(currentPosition, targetPosition, snapSpeed * Time.deltaTime);
                
                // 更新输入向量
                CalculateInputVector(joystickHandle.anchoredPosition);
            }
        }
        
        /// <summary>
        /// 重置摇杆
        /// </summary>
        public void ResetJoystick()
        {
            if (joystickHandle != null)
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
            
            inputVector = Vector2.zero;
            OnValueChanged?.Invoke(inputVector);
        }
        
        /// <summary>
        /// 更新视觉反馈
        /// </summary>
        private void UpdateVisualFeedback(bool isActive)
        {
            if (!enableVisualFeedback) return;
            
            Color targetColor = isActive ? activeColor : normalColor;
            
            if (backgroundImage != null)
            {
                backgroundImage.color = targetColor;
            }
            
            if (handleImage != null)
            {
                handleImage.color = targetColor;
            }
        }
        
        /// <summary>
        /// 设置摇杆可见性
        /// </summary>
        public void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }
        
        /// <summary>
        /// 设置摇杆启用状态
        /// </summary>
        public void SetEnabled(bool enabled)
        {
            this.enabled = enabled;
            
            if (!enabled)
            {
                ResetJoystick();
                UpdateVisualFeedback(false);
            }
        }
    }
}
