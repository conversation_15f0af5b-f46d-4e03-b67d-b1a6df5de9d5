# Scriptable Brushes

You can script Brushes to paint items based on the position and conditions of the cell it targets on the Grid Layout.
Brush paint behavior can be further modified by the selected editing Tool, such as __Erase__ or __Floodfill__.

Here are some implementations of **Scriptable Brushes** which can help save time when designing your Tilemap:

- [GameObject Brush](GameObjectBrush.md)
- [Random Brush](RandomBrush.md)
- [Line Brush](LineBrush.md)
- [Group Brush](GroupBrush.md)

Refer to the [Scriptable Brushes](https://docs.unity3d.com/Manual/Tilemap-ScriptableBrushes.html) documentation for more
information.

