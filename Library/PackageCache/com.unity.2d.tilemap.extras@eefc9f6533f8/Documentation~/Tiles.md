# Scriptable Tiles

You can script Tiles to adapt to different criteria and conditions, such as its position on the Tilemap. It then
displays the Sprite which meets its scripted requirements. This allows you to create different Tiles that can help you
save time and be more efficient when creating Tilemaps. Refer to
the [Scriptable Tiles](https://docs.unity3d.com/Manual/Tilemap-ScriptableTiles.html) page for more information.

The following **Scriptable Tiles** are included in this package, with examples of how they are implemented. You can use
these Tiles as the base for your own custom Tiles as well.

- [Animated Tile](AnimatedTile.md)
- [Rule Tile](RuleTile.md)
- [Rule Override Tile](RuleOverrideTile.md)

