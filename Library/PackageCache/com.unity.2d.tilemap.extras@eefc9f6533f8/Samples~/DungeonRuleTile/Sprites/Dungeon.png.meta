fileFormatVersion: 2
guid: 4e794009527fa49a7a0e11880aaa3a50
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8074338581418846440
    second: Dungeon_00
  - first:
      213: 4519664136019108208
    second: Dungeon_01
  - first:
      213: -6337999885091750218
    second: Dungeon_02
  - first:
      213: -5779282676337348671
    second: Dungeon_03
  - first:
      213: -1238933728272178944
    second: Dungeon_04
  - first:
      213: -4637172546458061636
    second: Dungeon_05
  - first:
      213: 1404196382344981941
    second: Dungeon_06
  - first:
      213: -6537453368205885854
    second: Dungeon_07
  - first:
      213: -3932762474534642510
    second: Dungeon_08
  - first:
      213: -38647862416911262
    second: Dungeon_09
  - first:
      213: 9158922217335370285
    second: Dungeon_10
  - first:
      213: -2913927307954446974
    second: Dungeon_11
  - first:
      213: -8314697479797604436
    second: Dungeon_12
  - first:
      213: -8560909203809222747
    second: Dungeon_13
  - first:
      213: -2860982009417361707
    second: Dungeon_14
  - first:
      213: 833101472512629755
    second: Dungeon_15
  externalObjects: {}
  serializedVersion: 11
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: -1
    aniso: -1
    mipBias: -100
    wrapU: 1
    wrapV: 1
    wrapW: -1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 128
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Dungeon_00
      rect:
        serializedVersion: 2
        x: 8
        y: 440
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8e8a3bdf820dd0070800000000000000
      internalID: 8074338581418846440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_01
      rect:
        serializedVersion: 2
        x: 152
        y: 440
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 071ed73e59219be30800000000000000
      internalID: 4519664136019108208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_02
      rect:
        serializedVersion: 2
        x: 296
        y: 440
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6beb3a64fa6ea08a0800000000000000
      internalID: -6337999885091750218
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_03
      rect:
        serializedVersion: 2
        x: 440
        y: 440
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1cfe75c960ddbcfa0800000000000000
      internalID: -5779282676337348671
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_04
      rect:
        serializedVersion: 2
        x: 8
        y: 296
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 001261a965c6ecee0800000000000000
      internalID: -1238933728272178944
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_05
      rect:
        serializedVersion: 2
        x: 152
        y: 296
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cb47c26432475afb0800000000000000
      internalID: -4637172546458061636
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_06
      rect:
        serializedVersion: 2
        x: 296
        y: 296
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5bdaf848b25bc7310800000000000000
      internalID: 1404196382344981941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_07
      rect:
        serializedVersion: 2
        x: 440
        y: 296
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 26e386c6ccc4645a0800000000000000
      internalID: -6537453368205885854
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_08
      rect:
        serializedVersion: 2
        x: 8
        y: 152
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2bc5067bd550c69c0800000000000000
      internalID: -3932762474534642510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_09
      rect:
        serializedVersion: 2
        x: 152
        y: 152
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 264df4159f1b67ff0800000000000000
      internalID: -38647862416911262
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_10
      rect:
        serializedVersion: 2
        x: 296
        y: 152
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d2235b90b370b1f70800000000000000
      internalID: 9158922217335370285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_11
      rect:
        serializedVersion: 2
        x: 440
        y: 152
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 28d90145d86af87d0800000000000000
      internalID: -2913927307954446974
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_12
      rect:
        serializedVersion: 2
        x: 8
        y: 8
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: caff3ddf1b24c9c80800000000000000
      internalID: -8314697479797604436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_13
      rect:
        serializedVersion: 2
        x: 152
        y: 8
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5af3490df6a813980800000000000000
      internalID: -8560909203809222747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_14
      rect:
        serializedVersion: 2
        x: 296
        y: 8
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d615e34500cb48d0800000000000000
      internalID: -2860982009417361707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dungeon_15
      rect:
        serializedVersion: 2
        x: 440
        y: 8
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bfb58f21065cf8b00800000000000000
      internalID: 833101472512629755
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 1
  userData: 
  assetBundleName: 
  assetBundleVariant: 
