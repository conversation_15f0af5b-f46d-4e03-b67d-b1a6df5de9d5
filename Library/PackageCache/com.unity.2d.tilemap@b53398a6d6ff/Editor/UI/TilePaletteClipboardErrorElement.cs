using UnityEngine.UIElements;

namespace UnityEditor.Tilemaps
{
    /// <summary>
    /// VisualElement for showing errors in the Tile Palette Clipboard
    /// </summary>
    [UxmlElement]
    public partial class TilePaletteClipboardErrorElement : VisualElement
    {
        private static readonly string ussClassName = "unity-tilepalette-clipboard-error-element";
        private static readonly string k_Name = L10n.Tr("Tile Palette Clipboard Error Element");

        internal static class Styles
        {
            public static readonly string emptyPaletteInfo = L10n.Tr("Drag Tile, Sprite or Texture (Sprite type) asset/s here.");
            public static readonly string emptyModelPrefabInfo = L10n.Tr("To begin, add Textures with Sprites to your imported asset.");
            public static readonly string invalidPaletteInfo = L10n.Tr("This is an invalid palette. Did you delete the palette asset?");
            public static readonly string invalidGridInfo = L10n.Tr("The palette has an invalid Grid. Did you add a Grid to the palette asset?");
            public static readonly string invalidDragAndDropInfo = L10n.Tr("You have dragged invalid items to the palette.");
        }

        private Label m_LabelElement;

        /// <summary>
        /// Constructor for TilePaletteClipboardErrorElement
        /// </summary>
        public TilePaletteClipboardErrorElement()
        {
            AddToClassList(ussClassName);

            name = k_Name;
            TilePaletteOverlayUtility.SetStyleSheet(this);

            m_LabelElement = new Label();
            Add(m_LabelElement);
        }

        /// <summary>
        /// Set error text for an empty Tile Palette
        /// </summary>
        public void SetEmptyPaletteText()
        {
            m_LabelElement.text = Styles.emptyPaletteInfo;
        }

        /// <summary>
        /// Set error text for an empty Tile Palette generated by an asset
        /// </summary>
        public void SetEmptyModelPaletteText()
        {
            m_LabelElement.text = Styles.emptyModelPrefabInfo;
        }

        /// <summary>
        /// Set error text for an invalid Tile Palette
        /// </summary>
        public void SetInvalidPaletteText()
        {
            m_LabelElement.text = Styles.invalidPaletteInfo;
        }

        /// <summary>
        /// Set error text for an invalid Grid in the Tile Palette
        /// </summary>
        public void SetInvalidGridText()
        {
            m_LabelElement.text = Styles.invalidGridInfo;
        }

        /// <summary>
        /// Set error text for an invalid drag and drop on the Tile Palette
        /// </summary>
        public void SetInvalidDragAndDropText()
        {
            m_LabelElement.text = Styles.invalidDragAndDropInfo;
        }

        /// <summary>
        /// Clears any error text set
        /// </summary>
        public void ClearText()
        {
            m_LabelElement.text = null;
        }
    }
}
