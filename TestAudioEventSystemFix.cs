using UnityEngine;
using MobileScrollingGame.Core;

/// <summary>
/// 简单的测试脚本来验证AudioEventSystem的修复
/// </summary>
public class TestAudioEventSystemFix : MonoBehaviour
{
    void Start()
    {
        TestDelayedAudioEvent();
    }
    
    void TestDelayedAudioEvent()
    {
        Debug.Log("开始测试 AudioEventSystem.PlayDelayedAudioEvent...");
        
        try
        {
            // 创建一个新的AudioEventSystem实例（模拟测试环境）
            GameObject testObj = new GameObject("TestAudioEventSystem");
            AudioEventSystem audioEventSystem = testObj.AddComponent<AudioEventSystem>();
            
            // 直接调用PlayDelayedAudioEvent，不等待Awake/Start
            audioEventSystem.PlayDelayedAudioEvent("TestSound", 0.1f);
            
            Debug.Log("✅ PlayDelayedAudioEvent 调用成功，没有抛出NullReferenceException");
            
            // 清理
            DestroyImmediate(testObj);
        }
        catch (System.NullReferenceException ex)
        {
            Debug.LogError("❌ 仍然存在NullReferenceException: " + ex.Message);
        }
        catch (System.Exception ex)
        {
            Debug.LogError("❌ 其他异常: " + ex.Message);
        }
    }
}
