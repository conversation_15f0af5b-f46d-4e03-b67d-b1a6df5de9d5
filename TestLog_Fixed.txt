Unity Editor version:    6000.1.1f1 (7197418f847b)
Branch:                  6000.1/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.3.2 (Build 24D81)
Darwin version:          24.3.0
Architecture:            x86_64
Running under Rosetta:   NO
Available memory:        32768 MB
Date:                    2025-08-05T08:32:51Z
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-silverflute" at "2025-08-05T08:32:51.625298Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 4589, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              cabeddfcd46247eabd689f91bb6fa17b
  Correlation Id:          857763e2c20671d56c2dd0179cfa67ef
  External correlation Id: 6223876933133763598
  Machine Id:              hzVApD/zBvVejW++rhjRfhQ/cyQ=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-silverflute" (connect: 0.00s, validation: 0.01s, handshake: 0.72s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-silverflute-notifications" at "2025-08-05T08:32:52.35927Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "1573678-UnityPersXXXX"
[Licensing::Client] Successfully updated license, isAync: True, time: 0.01
[Licensing::Client] Successfully resolved entitlement details
Pro License: NO
Launching external process: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/MacOS/Unity
-batchmode
-runTests
-testPlatform
EditMode
-testFilter
CameraFollowerTestDiagnostics
-testResults
TestResults_Fixed.xml
-logFile
TestLog_Fixed.txt
-projectPath
.
Successfully changed project path to: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [140704257624832]  Target information:

Player connection [140704257624832]  * "[IP] ************* [Port] 55504 [Flags] 2 [Guid] 3606931627 [EditorId] 3606931627 [Version] 1048832 [Id] OSXEditor(0,*************) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [140704257624832] Host joined multi-casting on [***********:54997]...
Player connection [140704257624832] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Package Manager] Connected to IPC stream "Upm-33518" after 0.4 seconds.
Library Redirect Path: Library/
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 70 packages:
  Packages from [https://packages.unity.com]:
    com.unity.collab-proxy@2.8.2 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f)
    com.unity.ide.rider@3.0.36 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.ide.rider@4d374c7eb6db)
    com.unity.ide.visualstudio@2.0.23 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13)
    com.unity.inputsystem@1.14.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.inputsystem@7fe8299111a7)
    com.unity.timeline@1.8.7 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.timeline@c58b4ee65782)
    com.unity.visualscripting@1.9.6 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.visualscripting@7dcdc439b230)
    com.unity.mobile.android-logcat@1.4.5 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.mobile.android-logcat@0ddcd2133dc3)
    com.unity.adaptiveperformance@5.1.3 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.adaptiveperformance@ffde895a1c6a)
    com.unity.mobile.notifications@2.4.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.mobile.notifications@439e41e635a0)
    com.unity.2d.animation@10.2.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.animation@53bd21164c9c)
    com.unity.2d.pixel-perfect@5.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.pixel-perfect@2f2037a56bf7)
    com.unity.2d.psdimporter@10.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.psdimporter@7cfb89931061)
    com.unity.2d.spriteshape@10.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.spriteshape@49c1d1a01890)
    com.unity.2d.tilemap.extras@4.3.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.tilemap.extras@eefc9f6533f8)
    com.unity.2d.aseprite@1.2.4 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.aseprite@85ecd97bb34e)
    com.unity.searcher@4.9.3 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.searcher@1e17ce91558d)
    com.unity.burst@1.8.21 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.burst@59eb6f11d242)
    com.unity.mathematics@1.3.2 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.mathematics@8017b507cc74)
    com.unity.collections@2.5.1 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.collections@56bff8827a7e)
    com.unity.profiling.core@1.0.2 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.profiling.core@aac7b93912bc)
    com.unity.2d.common@9.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.common@2df623fe8b00)
    com.unity.nuget.mono-cecil@1.11.4 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f)
    com.unity.test-framework.performance@3.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed)
  Built-in packages:
    com.unity.feature.2d@2.0.1 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.feature.2d@dd1ea8910f12)
    com.unity.feature.mobile@1.0.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.feature.mobile@00f8a2b6a6fb)
    com.unity.multiplayer.center@1.0.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546)
    com.unity.render-pipelines.universal@17.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.render-pipelines.universal@89399b10acbb)
    com.unity.ugui@2.0.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.ugui@8a519b6be09c)
    com.unity.modules.accessibility@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility)
    com.unity.modules.ai@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ai)
    com.unity.modules.androidjni@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni)
    com.unity.modules.animation@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation)
    com.unity.modules.assetbundle@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle)
    com.unity.modules.audio@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio)
    com.unity.modules.cloth@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.cloth)
    com.unity.modules.director@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.director)
    com.unity.modules.imageconversion@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion)
    com.unity.modules.imgui@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui)
    com.unity.modules.jsonserialize@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize)
    com.unity.modules.particlesystem@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem)
    com.unity.modules.physics@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics)
    com.unity.modules.physics2d@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d)
    com.unity.modules.screencapture@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture)
    com.unity.modules.terrain@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain)
    com.unity.modules.terrainphysics@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrainphysics)
    com.unity.modules.tilemap@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap)
    com.unity.modules.ui@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui)
    com.unity.modules.uielements@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements)
    com.unity.modules.umbra@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.umbra)
    com.unity.modules.unityanalytics@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unityanalytics)
    com.unity.modules.unitywebrequest@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww)
    com.unity.modules.vehicles@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.vehicles)
    com.unity.modules.video@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.video)
    com.unity.modules.vr@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.vr)
    com.unity.modules.wind@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.wind)
    com.unity.modules.xr@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.xr)
    com.unity.modules.subsystems@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.subsystems)
    com.unity.modules.hierarchycore@1.0.0 (location: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore)
    com.unity.render-pipelines.core@17.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.render-pipelines.core@a7356ab905fd)
    com.unity.shadergraph@17.1.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.shadergraph@26dc5ae27e7d)
    com.unity.render-pipelines.universal-config@17.0.3 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.render-pipelines.universal-config@6af1487fecff)
    com.unity.test-framework@1.5.1 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f)
    com.unity.ext.nunit@2.0.5 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.ext.nunit@031a54704bff)
    com.unity.2d.sprite@1.0.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.sprite@8e227b7a8e02)
    com.unity.2d.tilemap@1.0.0 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.2d.tilemap@b53398a6d6ff)
    com.unity.rendering.light-transport@1.0.1 (location: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/PackageCache/com.unity.rendering.light-transport@56c10358ff14)
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.36 seconds
Refreshing native plugins compatible for Editor in 4.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
DELL U2312HM preferred device: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Metal devices available: 1
0: Intel(R) Iris(TM) Plus Graphics 655 (low power)
Using device Intel(R) Iris(TM) Plus Graphics 655 (low power)
Initializing Metal device caps: Intel(R) Iris(TM) Plus Graphics 655
[Licensing::Client] Successfully resolved entitlement details
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56518
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
AcceleratorClientConnectionCallback - disconnected - :0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.000677 seconds.
- Loaded All Assemblies, in  0.611 seconds
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.644 seconds
Domain Reload Profiling: 1255ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (261ms)
		LoadAssemblies (184ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (254ms)
				TypeCache.ScanAssembly (233ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (645ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (87ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (124ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (132ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
Application.AssetDatabase Initial Refresh Start
[ScriptCompilation] Requested script compilation because: Assetdatabase observed changes in script compilation related files
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://unix:/tmp/ilpp.sock-f5d39051bd643ed7e3a1e73d92457552
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Content root path: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 61.7642ms
Starting: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/bee_backend --dont-print-to-structured-log --ipc --defer-dag-verification --dagfile="Library/Bee/200b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2
DisplayProgressbar: Compiling Scripts
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/ConfigurePostProcessors application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/ConfigurePostProcessors'
[40m[1m[33mwarn[39m[22m[49m: Unity.ILPP.Runner.PostProcessingAssemblyLoadContext[0]
      Assembly system.runtime.interopservices.windowsruntime has duplicate hint path. Ignoring "/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll" in favor of "/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
[40m[1m[33mwarn[39m[22m[49m: Unity.ILPP.Runner.PostProcessingAssemblyLoadContext[0]
      Assembly system.componentmodel.composition has duplicate hint path. Ignoring "/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll" in favor of "/Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingAssemblyLoadContext[0]
      Resolved Unity.Burst.Cecil, Version=********, Culture=neutral, PublicKeyToken=50cebf1cceb9d05e with Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingAssemblyLoadContext[0]
      Resolved Mono.Cecil, Version=********, Culture=neutral, PublicKeyToken=50cebf1cceb9d05e with Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/ConfigurePostProcessors'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/ConfigurePostProcessors application/grpc - - 200 - application/grpc 43.8522ms
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/PostProcessAssembly application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/PostProcessAssembly'
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingPipeline[0]
      Processing assembly Library/Bee/artifacts/200b0aE.dag/MobileScrollingGame.Tests.dll, with 122 defines and 252 references
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingPipeline[0]
      processors: Unity.Jobs.CodeGen.JobsILPostProcessor, zzzUnity.Burst.CodeGen.BurstILPostProcessor
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingPipeline[0]
      running Unity.Jobs.CodeGen.JobsILPostProcessor
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingPipeline[0]
      running zzzUnity.Burst.CodeGen.BurstILPostProcessor
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/PostProcessAssembly'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/PostProcessAssembly application/grpc - - 200 - application/grpc 27.6624ms
ExitCode: 0 Duration: 3s790ms
[ 790/1056  0s] ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput
[1052/1056  3s] Csc Library/Bee/artifacts/200b0aE.dag/MobileScrollingGame.Tests.dll (+2 others)
[1053/1056  0s] ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/MobileScrollingGame.Tests.dll (+pdb)
Processing assembly Library/Bee/artifacts/200b0aE.dag/MobileScrollingGame.Tests.dll, with 122 defines and 252 references
processors: Unity.Jobs.CodeGen.JobsILPostProcessor, zzzUnity.Burst.CodeGen.BurstILPostProcessor
running Unity.Jobs.CodeGen.JobsILPostProcessor
running zzzUnity.Burst.CodeGen.BurstILPostProcessor
[1054/1056  0s] CopyFiles Library/ScriptAssemblies/MobileScrollingGame.Tests.pdb
[1055/1056  0s] CopyFiles Library/ScriptAssemblies/MobileScrollingGame.Tests.dll
*** Tundra build success (3.70 seconds), 5 items updated, 1056 evaluated
AssetDatabase: script compilation time: 4.811187s
Total cache size 69660316
Total cache size after purge 69660316, took 00:00:00.1059585
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.137 seconds
Script 'CharacterController' has the same name as built-in Unity component.
AddComponent and GetComponent will not work with this script.
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 ScriptFactory::SelectMonoScriptInfo(core::basic_string<char, core::StringStorageDefault<char>> const&, ScriptFactory::ScriptArtifactInfo&)
 #4 ScriptFactory::BatchRegisterAndCreateObjects(core::vector<ScriptAssetInfo, core::allocator<ScriptAssetInfo, 0ul>> const&)
 #5 ScriptFactory::ProcessChanges(ScriptAssetChanges const&, bool, core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>>&)
 #6 TransientArtifactCoordinator::ProcessScriptAssetsChanges(core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>> const&, core::hash_map<UnityGUID, core::basic_string<char, core::StringStorageDefault<char>>, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>> const&, ScriptChanges const&, CompileDomainReloadStatus, core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>>&)
 #7 ProcessScriptsAndDomainReloadIfNeeded(ScriptChanges&, CompileDomainReloadStatus&, bool, bool, core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>> const&, core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>> const&, core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>> const&, core::hash_map<UnityGUID, core::basic_string<char, core::StringStorageDefault<char>>, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>> const&, InternalRefreshFlagsV2 const&, AssetDatabase::UpdateAssetOptions const&, bool&, bool&, LoadedNativeAssetScriptTypeHashes&, core::hash_set<UnityGUID, core::hash<UnityGUID>, std::__1::equal_to<UnityGUID>>&)
 #8 RefreshInternalV2(AssetDatabase::UpdateAssetOptions, ScanFilter const&, InternalRefreshFlagsV2)
 #9 StopAssetImportingV2Internal(AssetDatabase::UpdateAssetOptions, InternalRefreshFlagsV2, ScanFilter const*, char const*)
 #10 InitialRefreshV2(bool, bool)
 #11 AssetDatabase::InitialRefresh(bool, bool)
 #12 Application::InitializeProject()
 #13 -[EditorApplication applicationDidFinishLaunching:]
 #14 __CFNOTIFICATIONCENTER_IS_CALLING_OUT_TO_AN_OBSERVER__
 #15 ___CFXRegistrationPost_block_invoke
 #16 _CFXRegistrationPost
 #17 _CFXNotificationPost
 #18 -[NSNotificationCenter postNotificationName:object:userInfo:]
 #19 -[NSApplication _postDidFinishNotification]
 #20 -[NSApplication _sendFinishLaunchingNotification]
 #21 -[NSApplication(NSAppleEventHandling) _handleAEOpenEvent:]
 #22 -[NSApplication(NSAppleEventHandling) _handleCoreEvent:withReplyEvent:]
 #23 -[NSAppleEventManager dispatchRawAppleEvent:withRawReply:handlerRefCon:]
 #24 _NSAppleEventManagerGenericHandler
 #25 _AppleEventsCheckInAppWithBlock
 #26 _AppleEventsCheckInAppWithBlock
 #27 aeProcessAppleEvent
 #28 AEProcessAppleEvent
 #29 _DPSNextEvent
 #30 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #31 -[NSApplication run]
 #32 NSApplicationMain
 #33 EditorMain(int, char const**)
 #34 main
 #35 start

Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.820 seconds
Domain Reload Profiling: 5957ms
	BeginReloadAssembly (226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (795ms)
		LoadAssemblies (459ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (438ms)
			TypeCache.Refresh (309ms)
				TypeCache.ScanAssembly (289ms)
			BuildScriptInfoCaches (104ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (4821ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4506ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (249ms)
			ProcessInitializeOnLoadAttributes (4050ms)
			ProcessInitializeOnLoadMethodAttributes (172ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 3.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Asset Pipeline Refresh (id=7f61a0644b13f41dfa5bab625f8ccec3): Total: 12.397 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=5959 ms
		Asset DB Callback time: managed=388 ms, native=11 ms
		Scripting: domain reloads=1, domain reload time=1138 ms, compile time=4814 ms, other=86 ms
		Project Asset Count: scripts=1501, non-scripts=2892
		Asset File Changes: new=0, changed=2, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.002ms
	InvokePackagesCallback: 1.313ms
	ApplyChangesToAssetFolders: 0.698ms
	Scan: 606.669ms
	OnSourceAssetsModified: 1.057ms
	CategorizeAssetsWithTransientArtifact: 77.750ms
	ProcessAssetsWithTransientArtifactChanges: 136.037ms
	CategorizeAssets: 75.330ms
	ImportOutOfDateAssets: 4837.144ms (19.913ms without children)
		CompileScripts: 4813.516ms
		ReloadNativeAssets: 0.033ms
		UnloadImportedAssets: 1.186ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 1.552ms
		InitializingProgressBar: 0.001ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.001ms
		OnDemandSchedulerStart: 0.944ms
	PostProcessAllAssets: 390.159ms
	Hotreload: 8.302ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.001ms
	UnloadStreamsBegin: 2.550ms
	PersistCurrentRevisions: 0.228ms
	UnloadStreamsEnd: 0.001ms
	GenerateScriptTypeHashes: 4.924ms
	Untracked: 6260.035ms

Application.AssetDatabase Initial Refresh End
Launching external process: /Applications/Unity/Hub/Editor/6000.1.1f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.18 seconds
Scanning for USB devices : 0.389ms
Initializing Unity extensions:
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-2dMobile2
2025-08-05 16:33:08.639 Unity[33518:353169] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x600002430bd0 SDF, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6961 unused Assets / (5.6 MB). Loaded Objects now: 7674.
Memory consumption went from 211.7 MB to 206.1 MB.
Total: 18.266600 ms (FindLiveObjects: 1.168353 ms CreateObjectMapping: 1.774890 ms MarkObjects: 11.011855 ms  DeleteObjects: 4.310293 ms)

2025-08-05 16:33:08.952 Unity[33518:353169] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x60000243e4c0 SDF, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
[Project] Loading completed in 17.374 seconds
	Project init time: 				15.933 seconds
		Template init time: 		0.000 seconds
		Package Manager init time: 		0.401 seconds
		Asset Database init time: 		0.449 seconds
		Global illumination init time: 	0.003 seconds
		Assemblies load time: 			1.275 seconds
		Unity extensions init time: 	0.012 seconds
		Asset Database refresh time: 	12.397 seconds
	Scene opening time: 			0.434 seconds
##utp:{"type":"ProjectInfo","version":2,"phase":"Immediate","time":1754382788987,"processId":33518,"projectLoad":17.373532873000003,"projectInit":15.933290193,"templateInit":0.0,"packageManagerInit":0.400669003,"assetDatabaseInit":0.449421983,"globalIlluminationInit":0.0030948,"assembliesLoad":1.274500275,"unityExtensionsInit":0.011830226,"assetDatabaseRefresh":12.397402591,"sceneOpening":0.434406869}
##utp:{"type":"EditorInfo","version":2,"phase":"Immediate","time":1754382788987,"processId":33518,"editorVersion":"6000.1.1f1 (7197418f847b)","branch":"6000.1/staging","buildType":"Release","platform":"OSX"}
Subscribe to USB device events
Scanning for USB devices : 0.246ms
Asset Pipeline Refresh (id=ad9520358ae884616b0836d133a0bba7): Total: 0.306 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=305 ms
		Asset DB Callback time: managed=0 ms, native=0 ms
		Scripting: domain reloads=0, domain reload time=0 ms, compile time=0 ms, other=0 ms
		Project Asset Count: scripts=1501, non-scripts=2892
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeCustomDependenciesCallbacks: 0.001ms
	InvokePackagesCallback: 0.001ms
	ApplyChangesToAssetFolders: 0.256ms
	Scan: 249.400ms
	OnSourceAssetsModified: 0.002ms
	CategorizeAssetsWithTransientArtifact: 0.002ms
	ProcessAssetsWithTransientArtifactChanges: 0.145ms
	CategorizeAssets: 40.314ms
	ImportOutOfDateAssets: 1.943ms (0.380ms without children)
		ReloadNativeAssets: 0.031ms
		UnloadImportedAssets: 0.176ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 0.870ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.484ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.230ms
	UnloadStreamsBegin: 1.371ms
	PersistCurrentRevisions: 0.236ms
	UnloadStreamsEnd: 0.001ms
	Untracked: 11.821ms

Running tests for ExecutionSettings with details:
targetPlatform = 
playerHeartbeatTimeout = 600
filters[0] = 
   Filter with settings:
   testMode = EditMode
   targetPlatform = 
   testNames = null
   groupNames = CameraFollowerTestDiagnostics
   categoryNames = null
   assemblyNames = null
ignoreTests = {}
featureFlags = null

 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [TestStarter.cs:64] UnityEditor.TestTools.TestRunner.CommandLineTest.TestStarter:InitializeAndExecuteRun ()
 #7  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #8 mono_jit_runtime_invoke
 #9 do_runtime_invoke
 #10 mono_runtime_invoke
 #11 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #12 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #13 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #14 SceneTracker::Update()
 #15 Application::TickTimer()
 #16 -[EditorApplication applicationDidFinishLaunching:]
 #17 __CFNOTIFICATIONCENTER_IS_CALLING_OUT_TO_AN_OBSERVER__
 #18 ___CFXRegistrationPost_block_invoke
 #19 _CFXRegistrationPost
 #20 _CFXNotificationPost
 #21 -[NSNotificationCenter postNotificationName:object:userInfo:]
 #22 -[NSApplication _postDidFinishNotification]
 #23 -[NSApplication _sendFinishLaunchingNotification]
 #24 -[NSApplication(NSAppleEventHandling) _handleAEOpenEvent:]
 #25 -[NSApplication(NSAppleEventHandling) _handleCoreEvent:withReplyEvent:]
 #26 -[NSAppleEventManager dispatchRawAppleEvent:withRawReply:handlerRefCon:]
 #27 _NSAppleEventManagerGenericHandler
 #28 _AppleEventsCheckInAppWithBlock
 #29 _AppleEventsCheckInAppWithBlock
 #30 aeProcessAppleEvent
 #31 AEProcessAppleEvent
 #32 _DPSNextEvent
 #33 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #34 -[NSApplication run]
 #35 NSApplicationMain
 #36 EditorMain(int, char const**)
 #37 main
 #38 start

[Licensing::Client] Successfully resolved entitlement details
[UnityConnectServicesConfig] config is NOT valid, switching to default
Created GICache directory at /Users/<USER>/Library/Caches/com.unity3d.UnityEditor/GiCache. Took: 0.004s, timestamps: [17.387 - 17.391]
Unloading 3 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4 unused Assets / (2.1 KB). Loaded Objects now: 7717.
Memory consumption went from 197.8 MB to 197.8 MB.
Total: 14.171075 ms (FindLiveObjects: 0.375310 ms CreateObjectMapping: 0.285727 ms MarkObjects: 13.402722 ms  DeleteObjects: 0.106202 ms)

Executing IPrebuildSetup for: Unity.PerformanceTesting.Editor.TestRunBuilder.
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [BuildActionTaskBase.cs:35] UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<Execute>d__7<T_REF>:MoveNext ()
 #7  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #8  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #9 mono_jit_runtime_invoke
 #10 do_runtime_invoke
 #11 mono_runtime_invoke
 #12 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #13 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #14 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #15 SceneTracker::Update()
 #16 Application::TickTimer()
 #17 -[EditorApplication onEditorUpdatesTickTimer]
 #18 __NSFireTimer
 #19 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #20 __CFRunLoopDoTimer
 #21 __CFRunLoopDoTimers
 #22 __CFRunLoopRun
 #23 CFRunLoopRunSpecific
 #24 RunCurrentEventLoopInMode
 #25 ReceiveNextEventCommon
 #26 _BlockUntilNextEventMatchingListInModeWithFilter
 #27 _DPSNextEvent
 #28 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #29 -[NSApplication run]
 #30 NSApplicationMain
 #31 EditorMain(int, char const**)
 #32 main
 #33 start

=== CameraFollower诊断测试开始 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

--- 诊断摄像机边界 ---
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

设置边界: (x:-25.00, y:-15.00, width:50.00, height:30.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机尺寸: 5
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机宽高比: 1.777778
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机视野高度: 10
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机视野宽度: 17.77778
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

X轴限制范围: [-16.11111, 16.11111]
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

Y轴限制范围: [-10, 10]
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

边界计算有效: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])

目标移动到边界外: (50.00, 0.00, 0.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])

限制后摄像机位置: (16.11, 1.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])

X轴在边界内: True (位置: 16.11111, 范围: [-16.11111, 16.11111])
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])

Y轴在边界内: True (位置: 1, 范围: [-10, 10])
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])

=== CameraFollower诊断测试结束 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

=== CameraFollower诊断测试开始 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

--- 诊断组件完整性 ---
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机对象存在: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

目标对象存在: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

CameraFollower组件存在: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

Camera组件存在: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机类型: 正交
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

正交尺寸: 5
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

宽高比: 1.777778
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

近裁剪面: 0.3
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

远裁剪面: 1000
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

所有组件和方法检查完成
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

=== CameraFollower诊断测试结束 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

=== CameraFollower诊断测试开始 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

--- 诊断SetFollowTarget ---
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

初始摄像机位置: (0.00, 0.00, 0.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

初始目标位置: (0.00, 0.00, 0.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

边界限制已禁用
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

目标位置: (0.00, 0.00, 0.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

设置目标后摄像机位置: (0.00, 1.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

期望摄像机位置: (0.00, 1.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

位置差异: 0
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机组件存在: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机正交: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机尺寸: 5
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机宽高比: 1.777778
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

X轴匹配: True (差异: 0)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

Y轴匹配: True (差异: 0)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

Z轴匹配: True (差异: 0)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

=== CameraFollower诊断测试结束 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

=== CameraFollower诊断测试开始 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

--- 诊断震动效果 ---
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

震动前位置: (0.00, 0.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

震动已触发
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

=== CameraFollower诊断测试结束 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

=== CameraFollower诊断测试开始 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

--- 诊断平滑移动 ---
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[EditorApplication checkForFocusChange:]
 #55 __NSFireTimer
 #56 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #57 __CFRunLoopDoTimer
 #58 __CFRunLoopDoTimers
 #59 __CFRunLoopRun
 #60 CFRunLoopRunSpecific
 #61 RunCurrentEventLoopInMode
 #62 ReceiveNextEventCommon
 #63 _BlockUntilNextEventMatchingListInModeWithFilter
 #64 _DPSNextEvent
 #65 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #66 -[NSApplication run]
 #67 NSApplicationMain
 #68 EditorMain(int, char const**)
 #69 main
 #70 start

初始摄像机位置: (0.00, 0.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[EditorApplication checkForFocusChange:]
 #55 __NSFireTimer
 #56 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #57 __CFRunLoopDoTimer
 #58 __CFRunLoopDoTimers
 #59 __CFRunLoopRun
 #60 CFRunLoopRunSpecific
 #61 RunCurrentEventLoopInMode
 #62 ReceiveNextEventCommon
 #63 _BlockUntilNextEventMatchingListInModeWithFilter
 #64 _DPSNextEvent
 #65 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #66 -[NSApplication run]
 #67 NSApplicationMain
 #68 EditorMain(int, char const**)
 #69 main
 #70 start

目标移动到: (5.00, 0.00, 0.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[EditorApplication checkForFocusChange:]
 #55 __NSFireTimer
 #56 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #57 __CFRunLoopDoTimer
 #58 __CFRunLoopDoTimers
 #59 __CFRunLoopRun
 #60 CFRunLoopRunSpecific
 #61 RunCurrentEventLoopInMode
 #62 ReceiveNextEventCommon
 #63 _BlockUntilNextEventMatchingListInModeWithFilter
 #64 _DPSNextEvent
 #65 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #66 -[NSApplication run]
 #67 NSApplicationMain
 #68 EditorMain(int, char const**)
 #69 main
 #70 start

帧 0: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[EditorApplication checkForFocusChange:]
 #55 __NSFireTimer
 #56 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #57 __CFRunLoopDoTimer
 #58 __CFRunLoopDoTimers
 #59 __CFRunLoopRun
 #60 CFRunLoopRunSpecific
 #61 RunCurrentEventLoopInMode
 #62 ReceiveNextEventCommon
 #63 _BlockUntilNextEventMatchingListInModeWithFilter
 #64 _DPSNextEvent
 #65 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #66 -[NSApplication run]
 #67 NSApplicationMain
 #68 EditorMain(int, char const**)
 #69 main
 #70 start

帧 1: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 2: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 3: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 4: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 5: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 6: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 7: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 8: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

帧 9: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

最终摄像机位置: (1.11, 0.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

总移动距离: 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [EnumerableTestMethodCommand.cs:78] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerableAndRecordExceptions>d__4:MoveNext ()
 #7  (Mono JIT Code) [EnumerableTestMethodCommand.cs:64] UnityEngine.TestTools.EnumerableTestMethodCommand:AdvanceEnumerator (System.Collections.IEnumerator)
 #8  (Mono JIT Code) [EnumerableTestMethodCommand.cs:47] UnityEngine.TestTools.EnumerableTestMethodCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #9  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:48] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #10  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #11  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #12  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #13  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #14  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #15  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #16  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #18  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #20  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #21  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #22  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #31  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #32  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #33  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #34 mono_jit_runtime_invoke
 #35 do_runtime_invoke
 #36 mono_runtime_invoke
 #37 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #38 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #39 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #40 SceneTracker::Update()
 #41 Application::TickTimer()
 #42 -[EditorApplication onEditorUpdatesTickTimer]
 #43 __NSFireTimer
 #44 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #45 __CFRunLoopDoTimer
 #46 __CFRunLoopDoTimers
 #47 __CFRunLoopRun
 #48 CFRunLoopRunSpecific
 #49 RunCurrentEventLoopInMode
 #50 ReceiveNextEventCommon
 #51 _BlockUntilNextEventMatchingListInModeWithFilter
 #52 _DPSNextEvent
 #53 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #54 -[NSApplication run]
 #55 NSApplicationMain
 #56 EditorMain(int, char const**)
 #57 main
 #58 start

=== CameraFollower诊断测试结束 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [EditorEnumeratorTestWorkItem.cs:100] UnityEditor.TestTools.TestRunner.EditorEnumeratorTestWorkItem/<PerformWork>d__3:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

=== CameraFollower诊断测试开始 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

--- 诊断UpdateCameraPosition ---
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

设置目标后初始摄像机位置: (0.00, 0.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

目标移动到: (5.00, 0.00, 0.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

更新后摄像机位置: (1.11, 0.00, -10.00)
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机X轴移动: 1.111111
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

摄像机向右移动: True
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.MethodWrapper:Invoke (object,object[])
 #14  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:24] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<>c__DisplayClass2_0:<Execute>b__0 ()
 #15  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:83] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand:ExecuteAndCheckLog (UnityEngine.TestTools.Logging.LogScope,NUnit.Framework.Internal.TestResult,System.Action)
 #16  (Mono JIT Code) [UnityLogCheckDelegatingCommand.cs:36] UnityEngine.TestRunner.NUnitExtensions.Runner.UnityLogCheckDelegatingCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #17  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #18  (Mono JIT Code) [ImmediateEnumerableCommand.cs:18] UnityEngine.TestTools.ImmediateEnumerableCommand:Execute (NUnit.Framework.Internal.ITestExecutionContext)
 #19  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:219] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #20  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #21  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #22  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #23  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #24  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #25  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #26  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #27  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #33  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #34  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #35  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #36  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #37  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #38  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #39  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #40  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #41  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #42 mono_jit_runtime_invoke
 #43 do_runtime_invoke
 #44 mono_runtime_invoke
 #45 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #46 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #47 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #48 SceneTracker::Update()
 #49 Application::TickTimer()
 #50 -[EditorApplication onEditorUpdatesTickTimer]
 #51 __NSFireTimer
 #52 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #53 __CFRunLoopDoTimer
 #54 __CFRunLoopDoTimers
 #55 __CFRunLoopRun
 #56 CFRunLoopRunSpecific
 #57 RunCurrentEventLoopInMode
 #58 ReceiveNextEventCommon
 #59 _BlockUntilNextEventMatchingListInModeWithFilter
 #60 _DPSNextEvent
 #61 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #62 -[NSApplication run]
 #63 NSApplicationMain
 #64 EditorMain(int, char const**)
 #65 main
 #66 start

=== CameraFollower诊断测试结束 ===
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6 mono_jit_runtime_invoke
 #7 do_runtime_invoke
 #8 mono_runtime_try_invoke_array
 #9 ves_icall_InternalInvoke
 #10 ves_icall_InternalInvoke_raw
 #11  (Mono JIT Code) (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo:InternalInvoke (System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
 #12  (Mono JIT Code) System.Reflection.MethodBase:Invoke (object,object[])
 #13  (Mono JIT Code) NUnit.Framework.Internal.Reflect:InvokeMethod (System.Reflection.MethodInfo,object)
 #14  (Mono JIT Code) [EnumeratorHelper.cs:35] UnityEngine.TestTools.EnumeratorHelper/<ProgressOnEnumerator>d__5:MoveNext ()
 #15  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #16  (Mono JIT Code) [BeforeAfterTestCommandBase.cs:212] UnityEngine.TestTools.BeforeAfterTestCommandBase`1/<ExecuteEnumerable>d__16<T_REF>:MoveNext ()
 #17  (Mono JIT Code) [RetryCommand.cs:37] UnityEngine.TestTools.RetryCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #18  (Mono JIT Code) [RepeatCommand.cs:36] UnityEngine.TestTools.RepeatCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #19  (Mono JIT Code) [TimeoutCommand.cs:33] UnityEngine.TestTools.TimeoutCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #20  (Mono JIT Code) [IgnoreTestCommand.cs:42] UnityEngine.TestTools.IgnoreTestCommand/<ExecuteEnumerable>d__3:MoveNext ()
 #21  (Mono JIT Code) [StrictCheckCommand.cs:29] UnityEngine.TestTools.StrictCheckCommand/<ExecuteEnumerable>d__2:MoveNext ()
 #22  (Mono JIT Code) [DefaultTestWorkItem.cs:50] UnityEngine.TestRunner.NUnitExtensions.Runner.DefaultTestWorkItem/<PerformWork>d__2:MoveNext ()
 #23  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #24  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #25  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #26  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #27  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #28  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #29  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #30  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #31  (Mono JIT Code) [CompositeWorkItem.cs:207] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<RunChildren>d__17:MoveNext ()
 #32  (Mono JIT Code) [CompositeWorkItem.cs:82] UnityEngine.TestRunner.NUnitExtensions.Runner.CompositeWorkItem/<PerformWork>d__13:MoveNext ()
 #33  (Mono JIT Code) [EditModeRunner.cs:169] UnityEditor.TestTools.TestRunner.EditModeRunner:MoveNextAndUpdateYieldObject ()
 #34  (Mono JIT Code) [TestJobRunner.cs:173] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteStep ()
 #35  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #36  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #37 mono_jit_runtime_invoke
 #38 do_runtime_invoke
 #39 mono_runtime_invoke
 #40 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #41 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #42 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #43 SceneTracker::Update()
 #44 Application::TickTimer()
 #45 -[EditorApplication onEditorUpdatesTickTimer]
 #46 __NSFireTimer
 #47 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #48 __CFRunLoopDoTimer
 #49 __CFRunLoopDoTimers
 #50 __CFRunLoopRun
 #51 CFRunLoopRunSpecific
 #52 RunCurrentEventLoopInMode
 #53 ReceiveNextEventCommon
 #54 _BlockUntilNextEventMatchingListInModeWithFilter
 #55 _DPSNextEvent
 #56 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #57 -[NSApplication run]
 #58 NSApplicationMain
 #59 EditorMain(int, char const**)
 #60 main
 #61 start

Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
Saving results to: TestResults_Fixed.xml
Executing IPostBuildCleanup for: Unity.PerformanceTesting.Editor.TestRunBuilder.
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) [BuildActionTaskBase.cs:35] UnityEditor.TestTools.TestRunner.TestRun.Tasks.BuildActionTaskBase`1/<Execute>d__7<T_REF>:MoveNext ()
 #7  (Mono JIT Code) [TestJobRunner.cs:109] UnityEditor.TestTools.TestRunner.TestRun.TestJobRunner:ExecuteCallback ()
 #8  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #9 mono_jit_runtime_invoke
 #10 do_runtime_invoke
 #11 mono_runtime_invoke
 #12 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #13 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #14 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #15 SceneTracker::Update()
 #16 Application::TickTimer()
 #17 -[EditorApplication onEditorUpdatesTickTimer]
 #18 __NSFireTimer
 #19 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #20 __CFRunLoopDoTimer
 #21 __CFRunLoopDoTimers
 #22 __CFRunLoopRun
 #23 CFRunLoopRunSpecific
 #24 RunCurrentEventLoopInMode
 #25 ReceiveNextEventCommon
 #26 _BlockUntilNextEventMatchingListInModeWithFilter
 #27 _DPSNextEvent
 #28 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #29 -[NSApplication run]
 #30 NSApplicationMain
 #31 EditorMain(int, char const**)
 #32 main
 #33 start

Asset Pipeline Refresh (id=3cedd210db6e849dbb1930d2bd5ab1fb): Total: 0.135 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 0 unused Assets / (0 B). Loaded Objects now: 7718.
Memory consumption went from 189.7 MB to 189.7 MB.
Total: 14.053846 ms (FindLiveObjects: 0.334146 ms CreateObjectMapping: 0.351698 ms MarkObjects: 13.353749 ms  DeleteObjects: 0.013331 ms)

DisplayProgressbar: Undo
Test run completed. Exiting with code 0 (Ok). Run completed.
 #0 PlatformStacktrace::GetStacktrace(int)
 #1 Stacktrace::GetStacktrace(int)
 #2 DebugStringToFile(DebugStringToFileData const&)
 #3 DebugLogHandler_CUSTOM_Internal_Log(LogType, LogOption, BindingsManagedSpan*, void*)
 #4  (Mono JIT Code) (wrapper managed-to-native) UnityEngine.DebugLogHandler:Internal_Log_Injected (UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,intptr)
 #5  (Mono JIT Code) [DebugLogHandler.cs:10] UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
 #6  (Mono JIT Code) (wrapper runtime-invoke) object:runtime_invoke_void (object,intptr,intptr,intptr)
 #7 mono_jit_runtime_invoke
 #8 do_runtime_invoke
 #9 mono_runtime_invoke
 #10 scripting_method_invoke(ScriptingMethodPtr, ScriptingObjectPtr, ScriptingArguments&, ScriptingExceptionPtr*, bool)
 #11 ScriptingInvocation::Invoke(ScriptingExceptionPtr*, bool)
 #12 Scripting::UnityEditor::EditorApplicationProxy::Internal_CallUpdateFunctions(ScriptingExceptionPtr*)
 #13 SceneTracker::Update()
 #14 Application::TickTimer()
 #15 -[EditorApplication onEditorUpdatesTickTimer]
 #16 __NSFireTimer
 #17 __CFRUNLOOP_IS_CALLING_OUT_TO_A_TIMER_CALLBACK_FUNCTION__
 #18 __CFRunLoopDoTimer
 #19 __CFRunLoopDoTimers
 #20 __CFRunLoopRun
 #21 CFRunLoopRunSpecific
 #22 RunCurrentEventLoopInMode
 #23 ReceiveNextEventCommon
 #24 _BlockUntilNextEventMatchingListInModeWithFilter
 #25 _DPSNextEvent
 #26 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]
 #27 -[NSApplication run]
 #28 NSApplicationMain
 #29 EditorMain(int, char const**)
 #30 main
 #31 start

Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Physics::Module] Cleanup current backned.
[Physics::Module] Id: 0xf2b8ea05
[Licensing::IpcConnector] LicenseClient-silverflute-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-silverflute channel disconnected successfully.
AcceleratorClientConnectionCallback - disconnected - :0
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application is shutting down...
Cleanup mono
[40m[32minfo[39m[22m[49m: Unity.ILPP.Runner.PostProcessingAssemblyLoadContext[0]
      ALC ILPP context 1 is unloading
16:33:12.288 |V| RiderPlugin                    | :1                             | AppDomain.CurrentDomain.DomainUnload lifetimeDefinition.Terminate
debugger-agent: Unable to listen on 45
[Package Manager] Server process was shutdown
Checking for leaked weakptr:
  Found no leaked weakptrs.
##utp:{"type":"MemoryLeaks","version":2,"phase":"Immediate","time":1754382792592,"processId":33518,"allocatedMemory":11517605,"memoryLabels":[{"Default":13829},{"Permanent":15376},{"Thread":1085064},{"Manager":20694},{"VertexData":8},{"Geometry":408},{"VFX":-104},{"Texture":296},{"Shader":79646},{"Material":24},{"GfxDevice":37720},{"Animation":344},{"Audio":3960},{"FontEngine":272},{"Physics":1241},{"Serialization":736},{"Input":9168},{"JobScheduler":34208},{"TextLib":160},{"Mono":32},{"ScriptingNativeRuntime":155032},{"BaseObject":1619776},{"Resource":912},{"Renderer":2944},{"Transform":4654},{"File":692},{"WebCam":32},{"Culling":32},{"Terrain":11298},{"Wind":24},{"String":8914},{"DynamicArray":115617},{"HashMap":102729},{"Utility":2658656},{"PoolAlloc":1952},{"AI":40},{"TypeTree":5005},{"ScriptManager":512},{"RuntimeInitializeOnLoadManager":64},{"SpriteAtlas":112},{"GI":7248},{"Director":8064},{"WebRequest":776},{"VR":45690},{"SceneManager":504},{"Video":1088},{"LazyScriptCache":32},{"NativeArray":944},{"Camera":17},{"Secure":10985},{"SerializationCache":1576},{"APIUpdating":10320},{"Subsystems":336},{"VirtualTexturing":57504},{"StaticSafetyDebugInfo":278528},{"Baselib":16},{"Analytics":240},{"Hierarchy":256},{"Gui":104},{"EditorUtility":66933},{"VersionControl":4},{"Undo":955},{"AssetDatabase":4988688},{"EditorGi":328},{"UnityConnect":25168},{"Upm":1788},{"DrivenProperties":72},{"LocalIPC":210},{"ProfilerEditor":9730},{"CoreBusinessMetrics":3846},{"Licensing":3512},{"AssetReference":32},{"IPCStream":32}]}
