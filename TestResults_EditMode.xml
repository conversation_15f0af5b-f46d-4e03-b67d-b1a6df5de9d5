<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="289" result="Failed(Child)" total="289" passed="183" failed="106" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:21Z" duration="2.2267213">
  <test-suite type="TestSuite" id="1000" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:21Z" duration="2.226721" total="289" passed="183" failed="106" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1310" name="MobileScrollingGame.Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/MobileScrollingGame.Tests.dll" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:21Z" duration="2.217472" total="289" passed="183" failed="106" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="33454" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1311" name="MobileScrollingGame" fullname="MobileScrollingGame" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:21Z" duration="2.216718" total="289" passed="183" failed="106" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestSuite" id="1312" name="Tests" fullname="MobileScrollingGame.Tests" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:21Z" duration="2.216084" total="289" passed="183" failed="106" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-suite type="TestFixture" id="1001" name="CameraBoundsTests" fullname="MobileScrollingGame.Tests.CameraBoundsTests" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" testcasecount="11" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.058249" total="11" passed="11" failed="0" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <test-case id="1011" name="BoundsChangedEvent_TriggersWhenBoundsExpanded" fullname="MobileScrollingGame.Tests.CameraBoundsTests.BoundsChangedEvent_TriggersWhenBoundsExpanded" methodname="BoundsChangedEvent_TriggersWhenBoundsExpanded" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="1403247096" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.020753" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1010" name="BoundsChangedEvent_TriggersWhenBoundsSet" fullname="MobileScrollingGame.Tests.CameraBoundsTests.BoundsChangedEvent_TriggersWhenBoundsSet" methodname="BoundsChangedEvent_TriggersWhenBoundsSet" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="15266799" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.007415" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1007" name="ClampPointToBounds_ClampsPointCorrectly" fullname="MobileScrollingGame.Tests.CameraBoundsTests.ClampPointToBounds_ClampsPointCorrectly" methodname="ClampPointToBounds_ClampsPointCorrectly" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="685434468" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.002576" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1008" name="ClampPointToBounds_DoesNotChangePointInside" fullname="MobileScrollingGame.Tests.CameraBoundsTests.ClampPointToBounds_DoesNotChangePointInside" methodname="ClampPointToBounds_DoesNotChangePointInside" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="1986652386" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.000922" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1012" name="ClampPointToBounds_HandlesEdgeCases" fullname="MobileScrollingGame.Tests.CameraBoundsTests.ClampPointToBounds_HandlesEdgeCases" methodname="ClampPointToBounds_HandlesEdgeCases" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="1486641332" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.000928" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1004" name="ExpandBounds_IncreasesAllSides" fullname="MobileScrollingGame.Tests.CameraBoundsTests.ExpandBounds_IncreasesAllSides" methodname="ExpandBounds_IncreasesAllSides" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="966058601" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.000962" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1009" name="GetBounds_ReturnsCurrentBounds" fullname="MobileScrollingGame.Tests.CameraBoundsTests.GetBounds_ReturnsCurrentBounds" methodname="GetBounds_ReturnsCurrentBounds" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="2013124879" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.001096" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1006" name="IsPointInBounds_ReturnsFalseForPointOutside" fullname="MobileScrollingGame.Tests.CameraBoundsTests.IsPointInBounds_ReturnsFalseForPointOutside" methodname="IsPointInBounds_ReturnsFalseForPointOutside" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="770175699" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.001508" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1005" name="IsPointInBounds_ReturnsTrueForPointInside" fullname="MobileScrollingGame.Tests.CameraBoundsTests.IsPointInBounds_ReturnsTrueForPointInside" methodname="IsPointInBounds_ReturnsTrueForPointInside" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="1344517499" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.000605" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1002" name="SetBounds_UpdatesBoundsCorrectly" fullname="MobileScrollingGame.Tests.CameraBoundsTests.SetBounds_UpdatesBoundsCorrectly" methodname="SetBounds_UpdatesBoundsCorrectly" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="1846719868" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.000492" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1003" name="SetBounds_WithCenterAndSize_CalculatesCorrectBounds" fullname="MobileScrollingGame.Tests.CameraBoundsTests.SetBounds_WithCenterAndSize_CalculatesCorrectBounds" methodname="SetBounds_WithCenterAndSize_CalculatesCorrectBounds" classname="MobileScrollingGame.Tests.CameraBoundsTests" runstate="Runnable" seed="928532030" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.000576" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1013" name="CameraFollowerTestDiagnostics" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" testcasecount="6" result="Failed" site="Child" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:19Z" duration="0.224766" total="6" passed="4" failed="2" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1016" name="DiagnoseCameraBounds" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseCameraBounds" methodname="DiagnoseCameraBounds" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1545065289" result="Failed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.045711" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机X轴应该被边界限制
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseCameraBounds () [0x0027c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestDiagnostics.cs:171
]]></stack-trace>
              </failure>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断摄像机边界 ---
设置边界: (x:-5.00, y:-3.00, width:10.00, height:6.00)
摄像机尺寸: 5
摄像机宽高比: 1.777778
摄像机视野高度: 10
摄像机视野宽度: 17.77778
X轴限制范围: [3.888889, -3.888889]
Y轴限制范围: [2, -2]
目标移动到边界外: (20.00, 0.00, 0.00)
限制后摄像机位置: (0.00, 0.00, -10.00)
X轴在边界内: False (位置: 0, 最大: -3.888889)
Y轴在边界内: False (位置: 0, 范围: [2, -2])
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1019" name="DiagnoseComponentIntegrity" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseComponentIntegrity" methodname="DiagnoseComponentIntegrity" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1131662293" result="Passed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:18Z" duration="0.030997" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断组件完整性 ---
摄像机对象存在: True
目标对象存在: True
CameraFollower组件存在: True
Camera组件存在: True
摄像机类型: 正交
正交尺寸: 5
宽高比: 1.777778
近裁剪面: 0.3
远裁剪面: 1000
所有组件和方法检查完成
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1014" name="DiagnoseSetFollowTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseSetFollowTarget" methodname="DiagnoseSetFollowTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="45024796" result="Failed" start-time="2025-08-05 08:28:18Z" end-time="2025-08-05 08:28:19Z" duration="0.082737" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  位置设置失败
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseSetFollowTarget () [0x0024a] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestDiagnostics.cs:88
]]></stack-trace>
              </failure>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断SetFollowTarget ---
初始摄像机位置: (0.00, 0.00, 0.00)
初始目标位置: (0.00, 0.00, 0.00)
设置目标后摄像机位置: (0.00, 0.00, -10.00)
期望摄像机位置: (0.00, 1.00, -10.00)
位置差异: 1
摄像机组件存在: True
摄像机正交: True
摄像机尺寸: 5
摄像机宽高比: 1.777778
X轴匹配: True (差异: 0)
Y轴匹配: False (差异: 1)
Z轴匹配: True (差异: 0)
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1018" name="DiagnoseShakeEffect" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseShakeEffect" methodname="DiagnoseShakeEffect" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1366983882" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.010780" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断震动效果 ---
震动前位置: (0.00, 0.00, -10.00)
震动已触发
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1017" name="DiagnoseSmoothMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseSmoothMovement" methodname="DiagnoseSmoothMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1188264274" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.039773" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断平滑移动 ---
初始摄像机位置: (0.00, 0.00, -10.00)
目标移动到: (5.00, 0.00, 0.00)
帧 0: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 1: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 2: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 3: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 4: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 5: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 6: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 7: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 8: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 9: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
最终摄像机位置: (1.11, 0.00, -10.00)
总移动距离: 1.111111
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1015" name="DiagnoseUpdateCameraPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseUpdateCameraPosition" methodname="DiagnoseUpdateCameraPosition" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="73861751" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.011808" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断UpdateCameraPosition ---
设置目标后初始摄像机位置: (0.00, 0.00, -10.00)
目标移动到: (5.00, 0.00, 0.00)
更新后摄像机位置: (1.11, 0.00, -10.00)
摄像机X轴移动: 1.111111
摄像机向右移动: True
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1020" name="CameraFollowerTests" fullname="MobileScrollingGame.Tests.CameraFollowerTests" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" testcasecount="11" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.023955" total="11" passed="11" failed="0" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <test-case id="1031" name="EnableBounds_TogglesBoundaryRestriction" fullname="MobileScrollingGame.Tests.CameraFollowerTests.EnableBounds_TogglesBoundaryRestriction" methodname="EnableBounds_TogglesBoundaryRestriction" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1389455289" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.002713" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1024" name="EnableFollowing_DisablesFollowingWhenFalse" fullname="MobileScrollingGame.Tests.CameraFollowerTests.EnableFollowing_DisablesFollowingWhenFalse" methodname="EnableFollowing_DisablesFollowingWhenFalse" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="41913438" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.001329" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1026" name="GetCameraPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTests.GetCameraPosition_ReturnsCorrectPosition" methodname="GetCameraPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="2006002311" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000574" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1023" name="SetCameraBounds_LimitsCameraMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetCameraBounds_LimitsCameraMovement" methodname="SetCameraBounds_LimitsCameraMovement" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="340315751" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.001228" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1030" name="SetFollowSpeed_ChangesFollowSpeed" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetFollowSpeed_ChangesFollowSpeed" methodname="SetFollowSpeed_ChangesFollowSpeed" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="2106938945" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000940" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1021" name="SetFollowTarget_SetsTargetCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetFollowTarget_SetsTargetCorrectly" methodname="SetFollowTarget_SetsTargetCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="714405939" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.005297" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1028" name="SetOffset_ChangesFollowOffset" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetOffset_ChangesFollowOffset" methodname="SetOffset_ChangesFollowOffset" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="750298279" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000931" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1025" name="ShakeCamera_TriggersShakeEffect" fullname="MobileScrollingGame.Tests.CameraFollowerTests.ShakeCamera_TriggersShakeEffect" methodname="ShakeCamera_TriggersShakeEffect" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1801676120" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000555" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1029" name="SnapToTarget_MovesImmediatelyToTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SnapToTarget_MovesImmediatelyToTarget" methodname="SnapToTarget_MovesImmediatelyToTarget" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="410649019" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000590" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1022" name="UpdateCameraPosition_FollowsTargetMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTests.UpdateCameraPosition_FollowsTargetMovement" methodname="UpdateCameraPosition_FollowsTargetMovement" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="235429153" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000529" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1027" name="UpdateCameraPosition_SmoothlyFollowsTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTests.UpdateCameraPosition_SmoothlyFollowsTarget" methodname="UpdateCameraPosition_SmoothlyFollowsTarget" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="208625663" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.004980" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1032" name="CameraFollowerTestsFixed" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" testcasecount="13" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.035016" total="13" passed="13" failed="0" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <test-case id="1044" name="ComponentIntegrity_AllComponentsExist" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.ComponentIntegrity_AllComponentsExist" methodname="ComponentIntegrity_AllComponentsExist" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="128244110" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.001841" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1043" name="EnableBounds_TogglesBoundaryRestriction" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableBounds_TogglesBoundaryRestriction" methodname="EnableBounds_TogglesBoundaryRestriction" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1688568055" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.001046" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1036" name="EnableFollowing_DisablesFollowingWhenFalse" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableFollowing_DisablesFollowingWhenFalse" methodname="EnableFollowing_DisablesFollowingWhenFalse" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="580203294" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000956" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1038" name="GetCameraPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.GetCameraPosition_ReturnsCorrectPosition" methodname="GetCameraPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1144356944" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000636" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1045" name="PublicMethods_DoNotThrowExceptions" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.PublicMethods_DoNotThrowExceptions" methodname="PublicMethods_DoNotThrowExceptions" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="191848394" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.002061" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1035" name="SetCameraBounds_LimitsCameraMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetCameraBounds_LimitsCameraMovement" methodname="SetCameraBounds_LimitsCameraMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="678999603" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.013904" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[边界: (x:-5.00, y:-3.00, width:10.00, height:6.00)
摄像机尺寸: 17.77778x10
halfWidth: 8.888889, halfHeight: 5
计算的移动范围: minX=3.888889, maxX=-3.888889, minY=2, maxY=-2
expectedMaxX: -3.888889
边界太小，摄像机将被居中
最终移动范围: minX=0, maxX=0
期望位置: (20.00, 1.00, -10.00), 限制后位置: (0.00, 0.00, -10.00)
]]></output>
            </test-case>
            <test-case id="1042" name="SetFollowSpeed_UpdatesSpeedCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetFollowSpeed_UpdatesSpeedCorrectly" methodname="SetFollowSpeed_UpdatesSpeedCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1911359301" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.001415" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1033" name="SetFollowTarget_SetsTargetCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetFollowTarget_SetsTargetCorrectly" methodname="SetFollowTarget_SetsTargetCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1828399616" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.001028" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1040" name="SetOffset_ChangesFollowOffset" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetOffset_ChangesFollowOffset" methodname="SetOffset_ChangesFollowOffset" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1326254282" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000615" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1037" name="ShakeCamera_DoesNotThrowException" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.ShakeCamera_DoesNotThrowException" methodname="ShakeCamera_DoesNotThrowException" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="663957487" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000493" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1041" name="SnapToTarget_MovesImmediatelyToTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SnapToTarget_MovesImmediatelyToTarget" methodname="SnapToTarget_MovesImmediatelyToTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1532705442" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000687" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1034" name="UpdateCameraPosition_FollowsTargetMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_FollowsTargetMovement" methodname="UpdateCameraPosition_FollowsTargetMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="337039706" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.000671" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1039" name="UpdateCameraPosition_SmoothlyFollowsTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_SmoothlyFollowsTarget" methodname="UpdateCameraPosition_SmoothlyFollowsTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="388456211" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.005230" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1046" name="CameraPerformanceTests" fullname="MobileScrollingGame.Tests.CameraPerformanceTests" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" testcasecount="17" result="Failed" site="Child" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:20Z" duration="1.420737" total="17" passed="8" failed="9" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1056" name="TestBasicShakePerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestBasicShakePerformance" methodname="TestBasicShakePerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="314016671" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.026601" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestBasicShakePerformance>d__17.MoveNext () [0x00079] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:239
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1057" name="TestConcurrentShakesPerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestConcurrentShakesPerformance" methodname="TestConcurrentShakesPerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="566403372" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.009788" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestConcurrentShakesPerformance>d__18.MoveNext () [0x000ce] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:264
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1049" name="TestCullingDistanceSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingDistanceSettings" methodname="TestCullingDistanceSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1331016902" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.009297" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: 25.0f
  But was:  1000.0f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingDistanceSettings () [0x00012] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:128
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1050" name="TestCullingWithCameraMovement" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingWithCameraMovement" methodname="TestCullingWithCameraMovement" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="2078550211" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.009388" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestCullingWithCameraMovement>d__11.MoveNext () [0x000f5] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:150
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1053" name="TestDynamicResolutionAdjustment" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestDynamicResolutionAdjustment" methodname="TestDynamicResolutionAdjustment" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="820645203" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.009911" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestDynamicResolutionAdjustment>d__14.MoveNext () [0x0008a] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:193
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1051" name="TestDynamicResolutionInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestDynamicResolutionInitialization" methodname="TestDynamicResolutionInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1924795789" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.004947" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1062" name="TestFrameRateWithOptimizations" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrameRateWithOptimizations" methodname="TestFrameRateWithOptimizations" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="2070370460" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.012159" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[无优化帧率: 0.0
有优化帧率: 0.0
裁剪对象数: 0/0
]]></output>
            </test-case>
            <test-case id="1047" name="TestFrustumCullingInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrustumCullingInitialization" methodname="TestFrustumCullingInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="353912811" result="Passed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.004314" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1048" name="TestFrustumCullingPerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrustumCullingPerformance" methodname="TestFrustumCullingPerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="48722115" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:19Z" duration="0.006243" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestFrustumCullingPerformance>d__9.MoveNext () [0x0005d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:116
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1060" name="TestIntegratedPerformanceOptimization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestIntegratedPerformanceOptimization" methodname="TestIntegratedPerformanceOptimization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1071407521" result="Failed" start-time="2025-08-05 08:28:19Z" end-time="2025-08-05 08:28:20Z" duration="1.001625" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestIntegratedPerformanceOptimization>d__21.MoveNext () [0x00172] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:335
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1063" name="TestMemoryUsageOptimization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestMemoryUsageOptimization" methodname="TestMemoryUsageOptimization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1459432759" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.278245" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[初始内存: 771308KB
峰值内存: 771328KB
最终内存: 679312KB
]]></output>
            </test-case>
            <test-case id="1061" name="TestPerformanceUnderStress" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestPerformanceUnderStress" methodname="TestPerformanceUnderStress" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="862428293" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.013425" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestPerformanceUnderStress>d__22.MoveNext () [0x00135] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:377
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1052" name="TestResolutionScaleSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestResolutionScaleSettings" methodname="TestResolutionScaleSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="353600950" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.004041" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1058" name="TestShakePoolingSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestShakePoolingSettings" methodname="TestShakePoolingSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="686479919" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.005549" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1055" name="TestShakeSystemInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestShakeSystemInitialization" methodname="TestShakeSystemInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1751934079" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.004630" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1059" name="TestSpecialShakeEffects" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestSpecialShakeEffects" methodname="TestSpecialShakeEffects" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1489211667" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.010414" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestSpecialShakeEffects>d__20.MoveNext () [0x00101] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:293
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1054" name="TestTargetFrameRateSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestTargetFrameRateSettings" methodname="TestTargetFrameRateSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1239183618" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.004040" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1064" name="CameraShakeTests" fullname="MobileScrollingGame.Tests.CameraShakeTests" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" testcasecount="17" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.032415" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1074" name="ContinuousShake_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.ContinuousShake_DoesNotThrow" methodname="ContinuousShake_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="410822767" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001046" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1072" name="ExplosionShake_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.ExplosionShake_DoesNotThrow" methodname="ExplosionShake_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="739744942" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000627" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1073" name="ImpactShake_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.ImpactShake_DoesNotThrow" methodname="ImpactShake_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="661350872" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000953" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1078" name="ImpactShake_WithZeroVector_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.ImpactShake_WithZeroVector_DoesNotThrow" methodname="ImpactShake_WithZeroVector_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="223491776" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000480" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1067" name="IsShaking_ReturnsFalseInitially" fullname="MobileScrollingGame.Tests.CameraShakeTests.IsShaking_ReturnsFalseInitially" methodname="IsShaking_ReturnsFalseInitially" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="2077342248" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000297" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1077" name="MultipleShakes_OnlyOneActiveAtTime" fullname="MobileScrollingGame.Tests.CameraShakeTests.MultipleShakes_OnlyOneActiveAtTime" methodname="MultipleShakes_OnlyOneActiveAtTime" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="1998571615" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002312" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1071" name="ShakeEndsAutomatically_AfterDuration" fullname="MobileScrollingGame.Tests.CameraShakeTests.ShakeEndsAutomatically_AfterDuration" methodname="ShakeEndsAutomatically_AfterDuration" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="1425165992" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002622" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动应该在持续时间后自动结束
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTests+<ShakeEndsAutomatically_AfterDuration>d__11.MoveNext () [0x00057] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTests.cs:111
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1075" name="ShakeEvents_TriggerCorrectly" fullname="MobileScrollingGame.Tests.CameraShakeTests.ShakeEvents_TriggerCorrectly" methodname="ShakeEvents_TriggerCorrectly" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="1331770681" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003058" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动结束事件应该被触发
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTests+<ShakeEvents_TriggerCorrectly>d__15.MoveNext () [0x0010a] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTests.cs:164
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1081" name="ShakeIntensity_DecreasesOverTime" fullname="MobileScrollingGame.Tests.CameraShakeTests.ShakeIntensity_DecreasesOverTime" methodname="ShakeIntensity_DecreasesOverTime" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="1711049475" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002846" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1069" name="StartShake_MovesObjectFromOriginalPosition" fullname="MobileScrollingGame.Tests.CameraShakeTests.StartShake_MovesObjectFromOriginalPosition" methodname="StartShake_MovesObjectFromOriginalPosition" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="52690524" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002908" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动时对象应该偏离原始位置
  Expected: greater than 0.00999999978f
  But was:  0.00645046076f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTests+<StartShake_MovesObjectFromOriginalPosition>d__9.MoveNext () [0x00086] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTests.cs:80
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1068" name="StartShake_SetsIsShakingToTrue" fullname="MobileScrollingGame.Tests.CameraShakeTests.StartShake_SetsIsShakingToTrue" methodname="StartShake_SetsIsShakingToTrue" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="1597791643" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001973" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1066" name="StartShake_WithCustomParameters_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.StartShake_WithCustomParameters_DoesNotThrow" methodname="StartShake_WithCustomParameters_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="315912166" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000461" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1065" name="StartShake_WithDefaultParameters_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.StartShake_WithDefaultParameters_DoesNotThrow" methodname="StartShake_WithDefaultParameters_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="1282899456" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000394" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1080" name="StartShake_WithNegativeIntensity_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.StartShake_WithNegativeIntensity_DoesNotThrow" methodname="StartShake_WithNegativeIntensity_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="850683961" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000491" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1079" name="StartShake_WithZeroDuration_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTests.StartShake_WithZeroDuration_DoesNotThrow" methodname="StartShake_WithZeroDuration_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="230582287" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000581" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1070" name="StopShake_RestoresOriginalPosition" fullname="MobileScrollingGame.Tests.CameraShakeTests.StopShake_RestoresOriginalPosition" methodname="StopShake_RestoresOriginalPosition" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="2084175048" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003604" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1076" name="StopShake_TriggersEndEvent" fullname="MobileScrollingGame.Tests.CameraShakeTests.StopShake_TriggersEndEvent" methodname="StopShake_TriggersEndEvent" classname="MobileScrollingGame.Tests.CameraShakeTests" runstate="Runnable" seed="630869977" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002633" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1082" name="CameraShakeTestsFixed" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" testcasecount="23" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.056806" total="23" passed="19" failed="4" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1101" name="ComponentIntegrity_AllMethodsWork" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ComponentIntegrity_AllMethodsWork" methodname="ComponentIntegrity_AllMethodsWork" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="585686575" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001240" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1092" name="ContinuousShake_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ContinuousShake_DoesNotThrow" methodname="ContinuousShake_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="785635233" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000512" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1103" name="EdgeCase_RapidStartStop" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.EdgeCase_RapidStartStop" methodname="EdgeCase_RapidStartStop" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="662923941" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.012604" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1105" name="EdgeCase_VeryLongDuration" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.EdgeCase_VeryLongDuration" methodname="EdgeCase_VeryLongDuration" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="770936601" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002980" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1104" name="EdgeCase_VeryShortDuration" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.EdgeCase_VeryShortDuration" methodname="EdgeCase_VeryShortDuration" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="371926942" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003309" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Very short shake should complete quickly
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTestsFixed+<EdgeCase_VeryShortDuration>d__26.MoveNext () [0x00053] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTestsFixed.cs:310
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1090" name="ExplosionShake_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ExplosionShake_DoesNotThrow" methodname="ExplosionShake_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="116396966" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000914" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1091" name="ImpactShake_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ImpactShake_DoesNotThrow" methodname="ImpactShake_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="7760411" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001031" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1096" name="ImpactShake_WithZeroVector_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ImpactShake_WithZeroVector_DoesNotThrow" methodname="ImpactShake_WithZeroVector_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="516793842" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000461" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1085" name="IsShaking_ReturnsFalseInitially" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.IsShaking_ReturnsFalseInitially" methodname="IsShaking_ReturnsFalseInitially" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="283326185" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000340" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1095" name="MultipleShakes_OnlyOneActiveAtTime" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.MultipleShakes_OnlyOneActiveAtTime" methodname="MultipleShakes_OnlyOneActiveAtTime" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1615482824" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003250" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1089" name="ShakeEndsAutomatically_AfterDuration" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ShakeEndsAutomatically_AfterDuration" methodname="ShakeEndsAutomatically_AfterDuration" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="482664076" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002700" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动应该在持续时间后自动结束
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTestsFixed+<ShakeEndsAutomatically_AfterDuration>d__11.MoveNext () [0x00057] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTestsFixed.cs:118
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1093" name="ShakeEvents_TriggerCorrectly" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ShakeEvents_TriggerCorrectly" methodname="ShakeEvents_TriggerCorrectly" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="156621885" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002993" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动结束事件应该被触发
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTestsFixed+<ShakeEvents_TriggerCorrectly>d__15.MoveNext () [0x0010a] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTestsFixed.cs:177
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1100" name="ShakeIntensity_HandlesZeroAndNegativeValues" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ShakeIntensity_HandlesZeroAndNegativeValues" methodname="ShakeIntensity_HandlesZeroAndNegativeValues" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="171268288" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002171" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1102" name="ShakeSettings_CanBeModified" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.ShakeSettings_CanBeModified" methodname="ShakeSettings_CanBeModified" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="131678816" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000583" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1087" name="StartShake_MovesObjectFromOriginalPosition" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_MovesObjectFromOriginalPosition" methodname="StartShake_MovesObjectFromOriginalPosition" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1944592879" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002339" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动时对象应该偏离原始位置
  Expected: greater than 0.00999999978f
  But was:  0.00641750963f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraShakeTestsFixed+<StartShake_MovesObjectFromOriginalPosition>d__9.MoveNext () [0x00086] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraShakeTestsFixed.cs:87
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1086" name="StartShake_SetsIsShakingToTrue" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_SetsIsShakingToTrue" methodname="StartShake_SetsIsShakingToTrue" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1101296262" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002072" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1084" name="StartShake_WithCustomParameters_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_WithCustomParameters_DoesNotThrow" methodname="StartShake_WithCustomParameters_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="31455772" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000519" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1083" name="StartShake_WithDefaultParameters_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_WithDefaultParameters_DoesNotThrow" methodname="StartShake_WithDefaultParameters_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1112405139" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000767" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1099" name="StartShake_WithNegativeDuration_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_WithNegativeDuration_DoesNotThrow" methodname="StartShake_WithNegativeDuration_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1344484245" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000672" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1098" name="StartShake_WithNegativeIntensity_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_WithNegativeIntensity_DoesNotThrow" methodname="StartShake_WithNegativeIntensity_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="989371177" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000529" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1097" name="StartShake_WithZeroDuration_DoesNotThrow" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StartShake_WithZeroDuration_DoesNotThrow" methodname="StartShake_WithZeroDuration_DoesNotThrow" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1092443258" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000603" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1088" name="StopShake_RestoresOriginalPosition" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StopShake_RestoresOriginalPosition" methodname="StopShake_RestoresOriginalPosition" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="1511449873" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003576" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1094" name="StopShake_TriggersEndEvent" fullname="MobileScrollingGame.Tests.CameraShakeTestsFixed.StopShake_TriggersEndEvent" methodname="StopShake_TriggersEndEvent" classname="MobileScrollingGame.Tests.CameraShakeTestsFixed" runstate="Runnable" seed="992158419" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003831" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1106" name="CameraSystemIntegrationTests" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" testcasecount="9" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.040875" total="9" passed="5" failed="4" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1110" name="BoundsChange_UpdatesCameraFollowerBounds" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.BoundsChange_UpdatesCameraFollowerBounds" methodname="BoundsChange_UpdatesCameraFollowerBounds" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="1299943488" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.007340" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该遵守新的边界
  Expected: less than or equal to -3.56666708f
  But was:  1.11111069f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraSystemIntegrationTests+<BoundsChange_UpdatesCameraFollowerBounds>d__12.MoveNext () [0x0015b] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraSystemIntegrationTests.cs:150
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1115" name="CameraBounds_IntegratesWithCameraFollower" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraBounds_IntegratesWithCameraFollower" methodname="CameraBounds_IntegratesWithCameraFollower" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="791705225" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001078" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1111" name="CameraFollower_ICameraController_ImplementsAllMethods" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraFollower_ICameraController_ImplementsAllMethods" methodname="CameraFollower_ICameraController_ImplementsAllMethods" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="1476437478" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001223" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1108" name="CameraFollower_WithBounds_RespectsBoundaries" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraFollower_WithBounds_RespectsBoundaries" methodname="CameraFollower_WithBounds_RespectsBoundaries" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="566890936" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.005034" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该被边界限制
  Expected: less than or equal to -1.56666696f
  But was:  1.11111069f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraSystemIntegrationTests+<CameraFollower_WithBounds_RespectsBoundaries>d__10.MoveNext () [0x00133] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraSystemIntegrationTests.cs:92
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1109" name="CameraShake_WithFollower_CombinesEffects" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraShake_WithFollower_CombinesEffects" methodname="CameraShake_WithFollower_CombinesEffects" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="607645569" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003619" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  震动结束后摄像机应该跟随目标
  Expected: greater than 0.0f
  But was:  -0.00476150494f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraSystemIntegrationTests+<CameraShake_WithFollower_CombinesEffects>d__11.MoveNext () [0x00135] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraSystemIntegrationTests.cs:121
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1113" name="CameraSystem_HandlesDisabledFollowing" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraSystem_HandlesDisabledFollowing" methodname="CameraSystem_HandlesDisabledFollowing" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="694856808" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.007642" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1114" name="CameraSystem_HandlesNullTarget" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraSystem_HandlesNullTarget" methodname="CameraSystem_HandlesNullTarget" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="1073762401" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003567" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1112" name="CameraSystem_HandlesTargetMovementSmoothly" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraSystem_HandlesTargetMovementSmoothly" methodname="CameraSystem_HandlesTargetMovementSmoothly" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="150659804" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.007393" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该接近目标位置
  Expected: less than 2.0f
  But was:  6.96109152f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraSystemIntegrationTests+<CameraSystem_HandlesTargetMovementSmoothly>d__14.MoveNext () [0x001e0] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraSystemIntegrationTests.cs:200
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1107" name="CameraSystem_InitializesCorrectly" fullname="MobileScrollingGame.Tests.CameraSystemIntegrationTests.CameraSystem_InitializesCorrectly" methodname="CameraSystem_InitializesCorrectly" classname="MobileScrollingGame.Tests.CameraSystemIntegrationTests" runstate="Runnable" seed="1230102344" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000774" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1116" name="CharacterAnimationIntegrationTests" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" testcasecount="15" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.030571" total="15" passed="3" failed="12" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1120" name="Integration_ActionCommand_TriggersActionAnimation" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_ActionCommand_TriggersActionAnimation" methodname="Integration_ActionCommand_TriggersActionAnimation" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="1336131942" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002501" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1124" name="Integration_AnimationEvents_PropagateCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_AnimationEvents_PropagateCorrectly" methodname="Integration_AnimationEvents_PropagateCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="462501847" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001388" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  控制器事件应该触发
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_AnimationEvents_PropagateCorrectly () [0x00082] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:222
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1117" name="Integration_CharacterControllerWithAnimator_InitializesCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_CharacterControllerWithAnimator_InitializesCorrectly" methodname="Integration_CharacterControllerWithAnimator_InitializesCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="900795131" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000628" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1128" name="Integration_CharacterDataUpdate_UpdatesAnimationSystem" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_CharacterDataUpdate_UpdatesAnimationSystem" methodname="Integration_CharacterDataUpdate_UpdatesAnimationSystem" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="161297581" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002850" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_CharacterDataUpdate_UpdatesAnimationSystem () [0x00032] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:309 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1126" name="Integration_ContinuousMovement_UpdatesAnimationContinuously" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_ContinuousMovement_UpdatesAnimationContinuously" methodname="Integration_ContinuousMovement_UpdatesAnimationContinuously" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="269919153" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002300" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests+<Integration_ContinuousMovement_UpdatesAnimationContinuously>d__17.MoveNext () [0x00054] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:257 
  at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1123" name="Integration_GroundedStateChange_TriggersLandingAnimation" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_GroundedStateChange_TriggersLandingAnimation" methodname="Integration_GroundedStateChange_TriggersLandingAnimation" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="1315226896" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003319" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  着陆应该触发动画事件
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests+<Integration_GroundedStateChange_TriggersLandingAnimation>d__14.MoveNext () [0x000a9] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:201
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1121" name="Integration_IdleCommand_TriggersIdleAnimation" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_IdleCommand_TriggersIdleAnimation" methodname="Integration_IdleCommand_TriggersIdleAnimation" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="1325815767" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001339" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetIdleState () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:335 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_IdleCommand_TriggersIdleAnimation () [0x0003f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:142 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1119" name="Integration_JumpCommand_TriggersJumpAnimation" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_JumpCommand_TriggersJumpAnimation" methodname="Integration_JumpCommand_TriggersJumpAnimation" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="921855363" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001050" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Jump () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:286 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_JumpCommand_TriggersJumpAnimation () [0x0003f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:100 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1118" name="Integration_MoveCommand_TriggersCorrectAnimation" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_MoveCommand_TriggersCorrectAnimation" methodname="Integration_MoveCommand_TriggersCorrectAnimation" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="1092463832" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001135" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_MoveCommand_TriggersCorrectAnimation () [0x0003f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:79 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1122" name="Integration_MovementDirection_UpdatesFacingDirection" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_MovementDirection_UpdatesFacingDirection" methodname="Integration_MovementDirection_UpdatesFacingDirection" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="769684770" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002755" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests+<Integration_MovementDirection_UpdatesFacingDirection>d__13.MoveNext () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:163 
  at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1127" name="Integration_MultipleAnimationCommands_HandleCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_MultipleAnimationCommands_HandleCorrectly" methodname="Integration_MultipleAnimationCommands_HandleCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="2008420096" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001076" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetIdleState () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:335 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_MultipleAnimationCommands_HandleCorrectly () [0x00034] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:284 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1125" name="Integration_SetAnimation_WorksThroughController" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Integration_SetAnimation_WorksThroughController" methodname="Integration_SetAnimation_WorksThroughController" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="246904961" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001296" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1131" name="Requirements_AnimationStateTransitionsIntegration_WorksCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Requirements_AnimationStateTransitionsIntegration_WorksCorrectly" methodname="Requirements_AnimationStateTransitionsIntegration_WorksCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="716730177" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001224" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetIdleState () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:335 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Requirements_AnimationStateTransitionsIntegration_WorksCorrectly () [0x0003f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:370 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1129" name="Requirements_AnimatorControllerIntegration_WorksCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Requirements_AnimatorControllerIntegration_WorksCorrectly" methodname="Requirements_AnimatorControllerIntegration_WorksCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="1544407235" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001914" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  待机状态应该正常工作
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.SetIdleState () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:335 
  at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.<Requirements_AnimatorControllerIntegration_WorksCorrectly>b__20_0 () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:326 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Requirements_AnimatorControllerIntegration_WorksCorrectly () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:326
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1130" name="Requirements_SpriteFlippingIntegration_WorksCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Requirements_SpriteFlippingIntegration_WorksCorrectly" methodname="Requirements_SpriteFlippingIntegration_WorksCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationIntegrationTests" runstate="Runnable" seed="1544571906" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001275" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  SpriteRenderer应该被翻转
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationIntegrationTests.Requirements_SpriteFlippingIntegration_WorksCorrectly () [0x00032] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationIntegrationTests.cs:347
]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1132" name="CharacterAnimationTests" fullname="MobileScrollingGame.Tests.CharacterAnimationTests" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" testcasecount="30" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.041663" total="30" passed="25" failed="5" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1155" name="AnimationStateMachine_CanTransitionTo_InvalidTransition_ReturnsFalse" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_CanTransitionTo_InvalidTransition_ReturnsFalse" methodname="AnimationStateMachine_CanTransitionTo_InvalidTransition_ReturnsFalse" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1773646734" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.009625" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  从Death状态不应该能转换到其他状态
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_CanTransitionTo_InvalidTransition_ReturnsFalse () [0x00019] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationTests.cs:349
]]></stack-trace>
              </failure>
              <output><![CDATA[AnimationStateMachine: 不允许从 Idle 转换到 Death
]]></output>
            </test-case>
            <test-case id="1154" name="AnimationStateMachine_CanTransitionTo_ValidTransition_ReturnsTrue" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_CanTransitionTo_ValidTransition_ReturnsTrue" methodname="AnimationStateMachine_CanTransitionTo_ValidTransition_ReturnsTrue" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="222265878" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000431" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1151" name="AnimationStateMachine_ForceTransitionTo_IgnoresRules" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_ForceTransitionTo_IgnoresRules" methodname="AnimationStateMachine_ForceTransitionTo_IgnoresRules" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="172853736" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000468" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1157" name="AnimationStateMachine_GetAllowedTransitions_ReturnsCorrectList" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_GetAllowedTransitions_ReturnsCorrectList" methodname="AnimationStateMachine_GetAllowedTransitions_ReturnsCorrectList" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1394917272" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002463" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1153" name="AnimationStateMachine_GetStateData_ReturnsCorrectData" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_GetStateData_ReturnsCorrectData" methodname="AnimationStateMachine_GetStateData_ReturnsCorrectData" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="884294444" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000610" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1147" name="AnimationStateMachine_Initialize_SetsDefaultState" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_Initialize_SetsDefaultState" methodname="AnimationStateMachine_Initialize_SetsDefaultState" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="116913209" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000363" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1156" name="AnimationStateMachine_Reset_ReturnsToIdle" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_Reset_ReturnsToIdle" methodname="AnimationStateMachine_Reset_ReturnsToIdle" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="155380810" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000527" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1152" name="AnimationStateMachine_StateChanged_TriggersEvent" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_StateChanged_TriggersEvent" methodname="AnimationStateMachine_StateChanged_TriggersEvent" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="521731104" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000546" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1149" name="AnimationStateMachine_TryTransitionTo_InvalidTransition_ReturnsFalse" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_TryTransitionTo_InvalidTransition_ReturnsFalse" methodname="AnimationStateMachine_TryTransitionTo_InvalidTransition_ReturnsFalse" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="2052754470" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002095" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[AnimationStateMachine: 不允许从 Jumping 转换到 Walking
]]></output>
            </test-case>
            <test-case id="1150" name="AnimationStateMachine_TryTransitionTo_SameState_ReturnsFalse" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_TryTransitionTo_SameState_ReturnsFalse" methodname="AnimationStateMachine_TryTransitionTo_SameState_ReturnsFalse" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="274264545" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000638" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1148" name="AnimationStateMachine_TryTransitionTo_ValidTransition_ReturnsTrue" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.AnimationStateMachine_TryTransitionTo_ValidTransition_ReturnsTrue" methodname="AnimationStateMachine_TryTransitionTo_ValidTransition_ReturnsTrue" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1434495330" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000611" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1145" name="CharacterAnimator_AnimationStateChanged_TriggersEvent" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_AnimationStateChanged_TriggersEvent" methodname="CharacterAnimator_AnimationStateChanged_TriggersEvent" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="480663277" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000823" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1146" name="CharacterAnimator_FacingDirectionChanged_TriggersEvent" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_FacingDirectionChanged_TriggersEvent" methodname="CharacterAnimator_FacingDirectionChanged_TriggersEvent" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="641900141" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000715" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1133" name="CharacterAnimator_InitialState_ShouldBeIdle" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_InitialState_ShouldBeIdle" methodname="CharacterAnimator_InitialState_ShouldBeIdle" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="205024109" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000903" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1143" name="CharacterAnimator_PauseAnimation_SetsSpeedToZero" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PauseAnimation_SetsSpeedToZero" methodname="CharacterAnimator_PauseAnimation_SetsSpeedToZero" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1289601266" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001747" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  暂停动画应该将速度设置为0
  Expected: 0.0f
  But was:  1.0f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PauseAnimation_SetsSpeedToZero () [0x0000b] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationTests.cs:173
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1138" name="CharacterAnimator_PlayAction_SetsCorrectState" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PlayAction_SetsCorrectState" methodname="CharacterAnimator_PlayAction_SetsCorrectState" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="112245825" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000667" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1134" name="CharacterAnimator_PlayIdle_SetsCorrectState" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PlayIdle_SetsCorrectState" methodname="CharacterAnimator_PlayIdle_SetsCorrectState" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1823421653" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000554" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1136" name="CharacterAnimator_PlayJumping_SetsCorrectState" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PlayJumping_SetsCorrectState" methodname="CharacterAnimator_PlayJumping_SetsCorrectState" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1151288261" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000455" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1137" name="CharacterAnimator_PlayLanding_SetsCorrectState" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PlayLanding_SetsCorrectState" methodname="CharacterAnimator_PlayLanding_SetsCorrectState" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="2005970427" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000580" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1135" name="CharacterAnimator_PlayWalking_SetsCorrectState" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_PlayWalking_SetsCorrectState" methodname="CharacterAnimator_PlayWalking_SetsCorrectState" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1715155332" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000417" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1144" name="CharacterAnimator_ResumeAnimation_SetsSpeedToOne" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_ResumeAnimation_SetsSpeedToOne" methodname="CharacterAnimator_ResumeAnimation_SetsSpeedToOne" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1848703613" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000467" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1142" name="CharacterAnimator_SetAnimationSpeed_ChangesSpeed" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_SetAnimationSpeed_ChangesSpeed" methodname="CharacterAnimator_SetAnimationSpeed_ChangesSpeed" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1220780335" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000592" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  动画速度应该被正确设置
  Expected: 0.5f
  But was:  1.0f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_SetAnimationSpeed_ChangesSpeed () [0x00012] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationTests.cs:163
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1139" name="CharacterAnimator_SetFacingDirection_Left_FlipsSprite" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_SetFacingDirection_Left_FlipsSprite" methodname="CharacterAnimator_SetFacingDirection_Left_FlipsSprite" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="750195788" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000584" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  朝向左侧时精灵应该被翻转
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_SetFacingDirection_Left_FlipsSprite () [0x00032] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationTests.cs:123
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1140" name="CharacterAnimator_SetFacingDirection_Right_DoesNotFlipSprite" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_SetFacingDirection_Right_DoesNotFlipSprite" methodname="CharacterAnimator_SetFacingDirection_Right_DoesNotFlipSprite" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1300686841" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000396" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1141" name="CharacterAnimator_UpdateAnimatorParameters_UpdatesCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.CharacterAnimator_UpdateAnimatorParameters_UpdatesCorrectly" methodname="CharacterAnimator_UpdateAnimatorParameters_UpdatesCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1308343265" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000794" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1159" name="Integration_AnimationParameterUpdates_ShouldWorkOverTime" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.Integration_AnimationParameterUpdates_ShouldWorkOverTime" methodname="Integration_AnimationParameterUpdates_ShouldWorkOverTime" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="118943160" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002466" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1158" name="Integration_CharacterAnimatorWithStateMachine_ShouldWork" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.Integration_CharacterAnimatorWithStateMachine_ShouldWork" methodname="Integration_CharacterAnimatorWithStateMachine_ShouldWork" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="335723014" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000664" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1162" name="Requirements_AnimationStateTransitions_WorkCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.Requirements_AnimationStateTransitions_WorkCorrectly" methodname="Requirements_AnimationStateTransitions_WorkCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1815589418" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002120" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[AnimationStateMachine: 不允许从 Death 转换到 Walking
]]></output>
            </test-case>
            <test-case id="1160" name="Requirements_AnimatorController_ManagesStates" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.Requirements_AnimatorController_ManagesStates" methodname="Requirements_AnimatorController_ManagesStates" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1895497763" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000746" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1161" name="Requirements_SpriteFlipping_WorksCorrectly" fullname="MobileScrollingGame.Tests.CharacterAnimationTests.Requirements_SpriteFlipping_WorksCorrectly" methodname="Requirements_SpriteFlipping_WorksCorrectly" classname="MobileScrollingGame.Tests.CharacterAnimationTests" runstate="Runnable" seed="1952712032" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000670" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  精灵应该被翻转
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterAnimationTests.Requirements_SpriteFlipping_WorksCorrectly () [0x00066] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterAnimationTests.cs:454
]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1163" name="CharacterControllerTests" fullname="MobileScrollingGame.Tests.CharacterControllerTests" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" testcasecount="15" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.016727" total="15" passed="6" failed="9" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1164" name="CharacterController_ImplementsICharacterController" fullname="MobileScrollingGame.Tests.CharacterControllerTests.CharacterController_ImplementsICharacterController" methodname="CharacterController_ImplementsICharacterController" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000649" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1166" name="GetHealth_InitialHealth_ReturnsMaxHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTests.GetHealth_InitialHealth_ReturnsMaxHealth" methodname="GetHealth_InitialHealth_ReturnsMaxHealth" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="977972063" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000820" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  初始生命值应该大于0
  Expected: greater than 0
  But was:  0
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterControllerTests.GetHealth_InitialHealth_ReturnsMaxHealth () [0x0000b] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:73
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1165" name="GetPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CharacterControllerTests.GetPosition_ReturnsCorrectPosition" methodname="GetPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000546" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1176" name="IsGrounded_ReturnsBoolean" fullname="MobileScrollingGame.Tests.CharacterControllerTests.IsGrounded_ReturnsBoolean" methodname="IsGrounded_ReturnsBoolean" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="1875267779" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000534" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Tests.CharacterControllerTests.IsGrounded_ReturnsBoolean () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:203 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1173" name="Jump_CallsJumpSystem" fullname="MobileScrollingGame.Tests.CharacterControllerTests.Jump_CallsJumpSystem" methodname="Jump_CallsJumpSystem" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="1846569998" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000818" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Jump方法应该能够被调用
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.Jump () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:286 
  at MobileScrollingGame.Tests.CharacterControllerTests.<Jump_CallsJumpSystem>b__14_0 () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:179 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterControllerTests.Jump_CallsJumpSystem () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:179
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1172" name="Move_ValidDirection_CallsMovementSystem" fullname="MobileScrollingGame.Tests.CharacterControllerTests.Move_ValidDirection_CallsMovementSystem" methodname="Move_ValidDirection_CallsMovementSystem" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="135569306" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001298" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Move方法应该能够处理有效的方向输入
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterControllerTests+<>c__DisplayClass13_0.<Move_ValidDirection_CallsMovementSystem>b__0 () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:171 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterControllerTests.Move_ValidDirection_CallsMovementSystem () [0x00021] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:171
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1174" name="PerformAction_CallsActionSystem" fullname="MobileScrollingGame.Tests.CharacterControllerTests.PerformAction_CallsActionSystem" methodname="PerformAction_CallsActionSystem" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="602315867" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000615" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1171" name="SetAnimation_ValidAnimationName_TriggersAnimationEvent" fullname="MobileScrollingGame.Tests.CharacterControllerTests.SetAnimation_ValidAnimationName_TriggersAnimationEvent" methodname="SetAnimation_ValidAnimationName_TriggersAnimationEvent" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000561" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1178" name="SetCharacterData_UpdatesCurrentHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTests.SetCharacterData_UpdatesCurrentHealth" methodname="SetCharacterData_UpdatesCurrentHealth" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000627" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTests.SetCharacterData_UpdatesCurrentHealth () [0x0001c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:243 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1177" name="SetCharacterData_ValidData_UpdatesCharacterData" fullname="MobileScrollingGame.Tests.CharacterControllerTests.SetCharacterData_ValidData_UpdatesCharacterData" methodname="SetCharacterData_ValidData_UpdatesCharacterData" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000711" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTests.SetCharacterData_ValidData_UpdatesCharacterData () [0x0002c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:223 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1175" name="SetIdleState_CallsIdleSystem" fullname="MobileScrollingGame.Tests.CharacterControllerTests.SetIdleState_CallsIdleSystem" methodname="SetIdleState_CallsIdleSystem" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="1177137235" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001457" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  SetIdleState方法应该能够被调用
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.SetIdleState () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:335 
  at MobileScrollingGame.Tests.CharacterControllerTests.<SetIdleState_CallsIdleSystem>b__16_0 () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:195 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterControllerTests.SetIdleState_CallsIdleSystem () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:195
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1168" name="TakeDamage_ExcessiveDamage_HealthDoesNotGoBelowZero" fullname="MobileScrollingGame.Tests.CharacterControllerTests.TakeDamage_ExcessiveDamage_HealthDoesNotGoBelowZero" methodname="TakeDamage_ExcessiveDamage_HealthDoesNotGoBelowZero" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000841" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1169" name="TakeDamage_LethalDamage_TriggersDeathEvent" fullname="MobileScrollingGame.Tests.CharacterControllerTests.TakeDamage_LethalDamage_TriggersDeathEvent" methodname="TakeDamage_LethalDamage_TriggersDeathEvent" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000762" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1170" name="TakeDamage_TriggersHealthChangedEvent" fullname="MobileScrollingGame.Tests.CharacterControllerTests.TakeDamage_TriggersHealthChangedEvent" methodname="TakeDamage_TriggersHealthChangedEvent" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001244" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  事件应该传递正确的生命值
  Expected: -25
  But was:  0
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterControllerTests.TakeDamage_TriggersHealthChangedEvent () [0x0006c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:139
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1167" name="TakeDamage_ValidDamage_ReducesHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTests.TakeDamage_ValidDamage_ReducesHealth" methodname="TakeDamage_ValidDamage_ReducesHealth" classname="MobileScrollingGame.Tests.CharacterControllerTests" runstate="Runnable" seed="**********" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001115" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  伤害应该减少生命值
  Expected: -20
  But was:  0
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterControllerTests.TakeDamage_ValidDamage_ReducesHealth () [0x00026] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTests.cs:88
]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1179" name="CharacterControllerTestsFixed" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" testcasecount="16" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.010937" total="16" passed="0" failed="16" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1190" name="ComponentIntegrity_AllComponentsExist" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.ComponentIntegrity_AllComponentsExist" methodname="ComponentIntegrity_AllComponentsExist" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="1462522393" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001025" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1193" name="EdgeCase_MultipleDeathEvents_OnlyTriggersOnce" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.EdgeCase_MultipleDeathEvents_OnlyTriggersOnce" methodname="EdgeCase_MultipleDeathEvents_OnlyTriggersOnce" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="1639334340" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000691" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1192" name="EdgeCase_NegativeDamage_DoesNotIncreaseHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.EdgeCase_NegativeDamage_DoesNotIncreaseHealth" methodname="EdgeCase_NegativeDamage_DoesNotIncreaseHealth" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="692686072" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000475" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1191" name="EdgeCase_ZeroDamage_DoesNotChangeHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.EdgeCase_ZeroDamage_DoesNotChangeHealth" methodname="EdgeCase_ZeroDamage_DoesNotChangeHealth" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="452703056" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000350" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1195" name="EventSubscription_CanSubscribeAndUnsubscribe" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.EventSubscription_CanSubscribeAndUnsubscribe" methodname="EventSubscription_CanSubscribeAndUnsubscribe" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="1388436786" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000321" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1180" name="GetHealth_ReturnsCorrectHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.GetHealth_ReturnsCorrectHealth" methodname="GetHealth_ReturnsCorrectHealth" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="**********" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000326" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1186" name="Jump_DoesNotThrowException" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.Jump_DoesNotThrowException" methodname="Jump_DoesNotThrowException" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="801253417" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000334" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1185" name="Move_UpdatesPosition" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.Move_UpdatesPosition" methodname="Move_UpdatesPosition" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="426292523" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000322" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1194" name="PublicMethods_DoNotThrowExceptions" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.PublicMethods_DoNotThrowExceptions" methodname="PublicMethods_DoNotThrowExceptions" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="680129928" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000285" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1187" name="SetAnimation_TriggersAnimationChangedEvent" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetAnimation_TriggersAnimationChangedEvent" methodname="SetAnimation_TriggersAnimationChangedEvent" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="1824555631" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000314" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1189" name="SetCharacterData_UpdatesCharacterProperties" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetCharacterData_UpdatesCharacterProperties" methodname="SetCharacterData_UpdatesCharacterProperties" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="1567968689" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000324" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1188" name="SetIdleState_CallsIdleSystem" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetIdleState_CallsIdleSystem" methodname="SetIdleState_CallsIdleSystem" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="1626198995" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000283" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1184" name="TakeDamage_HealthCannotGoBelowZero" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.TakeDamage_HealthCannotGoBelowZero" methodname="TakeDamage_HealthCannotGoBelowZero" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="**********" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000361" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1183" name="TakeDamage_LethalDamage_TriggersDeathEvent" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.TakeDamage_LethalDamage_TriggersDeathEvent" methodname="TakeDamage_LethalDamage_TriggersDeathEvent" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="681903505" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000349" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1181" name="TakeDamage_ReducesHealth" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.TakeDamage_ReducesHealth" methodname="TakeDamage_ReducesHealth" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="520155842" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000354" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1182" name="TakeDamage_TriggersHealthChangedEvent" fullname="MobileScrollingGame.Tests.CharacterControllerTestsFixed.TakeDamage_TriggersHealthChangedEvent" methodname="TakeDamage_TriggersHealthChangedEvent" classname="MobileScrollingGame.Tests.CharacterControllerTestsFixed" runstate="Runnable" seed="**********" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000329" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterControllerTestsFixed.SetUp () [0x00078] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterControllerTestsFixed.cs:42 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1196" name="CharacterMovementIntegrationTests" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" testcasecount="8" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.015686" total="8" passed="0" failed="8" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1200" name="Integration_CharacterAnimationEvents_ShouldWork" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterAnimationEvents_ShouldWork" methodname="Integration_CharacterAnimationEvents_ShouldWork" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="1490232912" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002574" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Jump () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:286 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterAnimationEvents_ShouldWork () [0x00097] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:149 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1197" name="Integration_CharacterControllerWithInputMapper_ShouldWork" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterControllerWithInputMapper_ShouldWork" methodname="Integration_CharacterControllerWithInputMapper_ShouldWork" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="1194770633" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000971" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色控制器应该能够处理输入系统的输入
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests+<>c__DisplayClass6_0.<Integration_CharacterControllerWithInputMapper_ShouldWork>b__0 () [0x0000c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:65 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterControllerWithInputMapper_ShouldWork () [0x0001d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:62
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1204" name="Integration_CharacterDataConfiguration_ShouldWork" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterDataConfiguration_ShouldWork" methodname="Integration_CharacterDataConfiguration_ShouldWork" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="861913700" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000719" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterDataConfiguration_ShouldWork () [0x00032] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:217 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1199" name="Integration_CharacterHealthSystem_ShouldWork" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterHealthSystem_ShouldWork" methodname="Integration_CharacterHealthSystem_ShouldWork" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="**********" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000935" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  生命值应该减少
  Expected: less than 0
  But was:  0
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_CharacterHealthSystem_ShouldWork () [0x0007b] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:115
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1198" name="Integration_MovementInputToCharacter_ShouldMoveCharacter" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_MovementInputToCharacter_ShouldMoveCharacter" methodname="Integration_MovementInputToCharacter_ShouldMoveCharacter" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="**********" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001843" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests+<Integration_MovementInputToCharacter_ShouldMoveCharacter>d__7.MoveNext () [0x0003e] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:88 
  at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1201" name="Integration_RequirementValidation_BasicMovement" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_RequirementValidation_BasicMovement" methodname="Integration_RequirementValidation_BasicMovement" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="527061228" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001293" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色应该能够接收移动指令
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.<Integration_RequirementValidation_BasicMovement>b__10_0 () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:164 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_RequirementValidation_BasicMovement () [0x0001f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:164
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1202" name="Integration_RequirementValidation_JumpMechanism" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_RequirementValidation_JumpMechanism" methodname="Integration_RequirementValidation_JumpMechanism" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="1303025268" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001625" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色应该有跳跃功能
  Expected: No Exception to be thrown
  But was:  <System.NullReferenceException: Object reference not set to an instance of an object
  at MobileScrollingGame.Player.CharacterController.Jump () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:286 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.<Integration_RequirementValidation_JumpMechanism>b__11_0 () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:174 
  at NUnit.Framework.Constraints.ThrowsConstraint+VoidInvocationDescriptor.Invoke () [0x00000] in <f577cd9686154d6ab10efd7e65e64366>:0 
  at NUnit.Framework.Constraints.ThrowsConstraint+ExceptionInterceptor.Intercept (System.Object invocation) [0x00041] in <f577cd9686154d6ab10efd7e65e64366>:0 >
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_RequirementValidation_JumpMechanism () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:174
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1203" name="Integration_RequirementValidation_PhysicsMovement" fullname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests.Integration_RequirementValidation_PhysicsMovement" methodname="Integration_RequirementValidation_PhysicsMovement" classname="MobileScrollingGame.Tests.CharacterMovementIntegrationTests" runstate="Runnable" seed="1525608239" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003137" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at MobileScrollingGame.Player.CharacterController.Move (UnityEngine.Vector2 direction) [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:280 
  at MobileScrollingGame.Tests.CharacterMovementIntegrationTests+<Integration_RequirementValidation_PhysicsMovement>d__12.MoveNext () [0x0004b] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementIntegrationTests.cs:193 
  at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44 ]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1205" name="CharacterMovementTests" fullname="MobileScrollingGame.Tests.CharacterMovementTests" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.023281" total="13" passed="10" failed="3" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1207" name="CharacterData_InvalidMoveSpeed_ReturnsFalse" fullname="MobileScrollingGame.Tests.CharacterMovementTests.CharacterData_InvalidMoveSpeed_ReturnsFalse" methodname="CharacterData_InvalidMoveSpeed_ReturnsFalse" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="1290914483" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001417" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1206" name="CharacterData_IsValid_ReturnsTrue" fullname="MobileScrollingGame.Tests.CharacterMovementTests.CharacterData_IsValid_ReturnsTrue" methodname="CharacterData_IsValid_ReturnsTrue" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="1267439819" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000628" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1214" name="FacingRight_InitialState_IsTrue" fullname="MobileScrollingGame.Tests.CharacterMovementTests.FacingRight_InitialState_IsTrue" methodname="FacingRight_InitialState_IsTrue" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="349772411" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000766" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1217" name="GetCharacterData_ReturnsCorrectData" fullname="MobileScrollingGame.Tests.CharacterMovementTests.GetCharacterData_ReturnsCorrectData" methodname="GetCharacterData_ReturnsCorrectData" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="401889791" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001147" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1215" name="Move_LeftInput_FlipsCharacterToLeft" fullname="MobileScrollingGame.Tests.CharacterMovementTests.Move_LeftInput_FlipsCharacterToLeft" methodname="Move_LeftInput_FlipsCharacterToLeft" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="1686816594" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003075" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  向左移动时角色应该面向左侧
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementTests+<Move_LeftInput_FlipsCharacterToLeft>d__15.MoveNext () [0x0007c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementTests.cs:181
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1216" name="Move_RightInput_KeepsCharacterFacingRight" fullname="MobileScrollingGame.Tests.CharacterMovementTests.Move_RightInput_KeepsCharacterFacingRight" methodname="Move_RightInput_KeepsCharacterFacingRight" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="105710646" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002092" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1218" name="SetCharacterData_UpdatesMovementParameters" fullname="MobileScrollingGame.Tests.CharacterMovementTests.SetCharacterData_UpdatesMovementParameters" methodname="SetCharacterData_UpdatesMovementParameters" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="220443971" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000733" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1210" name="SetMoveInput_LeftMovement_MovesCharacterLeft" fullname="MobileScrollingGame.Tests.CharacterMovementTests.SetMoveInput_LeftMovement_MovesCharacterLeft" methodname="SetMoveInput_LeftMovement_MovesCharacterLeft" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="430992784" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002251" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色应该向左移动
  Expected: less than 0.0f
  But was:  0.0f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementTests+<SetMoveInput_LeftMovement_MovesCharacterLeft>d__10.MoveNext () [0x0007c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementTests.cs:117
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1209" name="SetMoveInput_RightMovement_MovesCharacterRight" fullname="MobileScrollingGame.Tests.CharacterMovementTests.SetMoveInput_RightMovement_MovesCharacterRight" methodname="SetMoveInput_RightMovement_MovesCharacterRight" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="795071312" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002267" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色应该向右移动
  Expected: greater than 0.0f
  But was:  0.0f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CharacterMovementTests+<SetMoveInput_RightMovement_MovesCharacterRight>d__9.MoveNext () [0x0008d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CharacterMovementTests.cs:100
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1208" name="SetMoveInput_ValidInput_UpdatesMovement" fullname="MobileScrollingGame.Tests.CharacterMovementTests.SetMoveInput_ValidInput_UpdatesMovement" methodname="SetMoveInput_ValidInput_UpdatesMovement" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="1977690090" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000520" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1212" name="StopMovement_ClearsMovementInput" fullname="MobileScrollingGame.Tests.CharacterMovementTests.StopMovement_ClearsMovementInput" methodname="StopMovement_ClearsMovementInput" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="1014350731" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000701" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1213" name="StopMovement_StopsCharacterMovement" fullname="MobileScrollingGame.Tests.CharacterMovementTests.StopMovement_StopsCharacterMovement" methodname="StopMovement_StopsCharacterMovement" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="286921525" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002456" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1211" name="TryJump_WhenNotGrounded_ReturnsFalse" fullname="MobileScrollingGame.Tests.CharacterMovementTests.TryJump_WhenNotGrounded_ReturnsFalse" methodname="TryJump_WhenNotGrounded_ReturnsFalse" classname="MobileScrollingGame.Tests.CharacterMovementTests" runstate="Runnable" seed="1064464062" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000586" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1219" name="CollisionDetectorTests" fullname="MobileScrollingGame.Tests.CollisionDetectorTests" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" testcasecount="23" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.046131" total="23" passed="11" failed="12" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1241" name="BoundaryChangedEvent_ShouldTriggerWhenBoundaryStateChanges" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.BoundaryChangedEvent_ShouldTriggerWhenBoundaryStateChanges" methodname="BoundaryChangedEvent_ShouldTriggerWhenBoundaryStateChanges" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="861661663" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.005422" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  边界状态改变时应该触发事件
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests+<BoundaryChangedEvent_ShouldTriggerWhenBoundaryStateChanges>d__33.MoveNext () [0x000af] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:470
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1235" name="BoundaryDetection_SetBoundaryCheck_ShouldDisableWhenSetToFalse" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.BoundaryDetection_SetBoundaryCheck_ShouldDisableWhenSetToFalse" methodname="BoundaryDetection_SetBoundaryCheck_ShouldDisableWhenSetToFalse" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="279533737" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.006451" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1234" name="BoundaryDetection_WhenCharacterOutOfBounds_ShouldReturnTrue" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.BoundaryDetection_WhenCharacterOutOfBounds_ShouldReturnTrue" methodname="BoundaryDetection_WhenCharacterOutOfBounds_ShouldReturnTrue" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="2042021081" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001062" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1233" name="BoundaryDetection_WhenCharacterWithinBounds_ShouldReturnFalse" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.BoundaryDetection_WhenCharacterWithinBounds_ShouldReturnFalse" methodname="BoundaryDetection_WhenCharacterWithinBounds_ShouldReturnFalse" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1941307971" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000973" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1242" name="ComplexScenario_CharacterMovingThroughEnvironment_ShouldDetectAllCollisions" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.ComplexScenario_CharacterMovingThroughEnvironment_ShouldDetectAllCollisions" methodname="ComplexScenario_CharacterMovingThroughEnvironment_ShouldDetectAllCollisions" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1950411376" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003902" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色应该检测到地面
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests+<ComplexScenario_CharacterMovingThroughEnvironment_ShouldDetectAllCollisions>d__34.MoveNext () [0x00143] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:497
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1224" name="GroundDetection_GetClosestGroundPoint_ShouldReturnCorrectPoint" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_GetClosestGroundPoint_ShouldReturnCorrectPoint" methodname="GroundDetection_GetClosestGroundPoint_ShouldReturnCorrectPoint" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1977354749" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002069" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  最近地面点的Y坐标应该正确
  Expected: -1.5d +/- 0.10000000149011612d
  But was:  -1.0d
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_GetClosestGroundPoint_ShouldReturnCorrectPoint () [0x0003b] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:178
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1223" name="GroundDetection_GetGroundAngle_ShouldReturnZeroForFlatGround" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_GetGroundAngle_ShouldReturnZeroForFlatGround" methodname="GroundDetection_GetGroundAngle_ShouldReturnZeroForFlatGround" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="2105405777" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000999" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1222" name="GroundDetection_GetGroundNormal_ShouldReturnCorrectNormal" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_GetGroundNormal_ShouldReturnCorrectNormal" methodname="GroundDetection_GetGroundNormal_ShouldReturnCorrectNormal" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="56992460" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000675" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1221" name="GroundDetection_WhenCharacterInAir_ShouldReturnFalse" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_WhenCharacterInAir_ShouldReturnFalse" methodname="GroundDetection_WhenCharacterInAir_ShouldReturnFalse" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1593324015" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000593" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1220" name="GroundDetection_WhenCharacterOnGround_ShouldReturnTrue" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_WhenCharacterOnGround_ShouldReturnTrue" methodname="GroundDetection_WhenCharacterOnGround_ShouldReturnTrue" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="774262811" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000922" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色在地面上时应该检测到地面
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.GroundDetection_WhenCharacterOnGround_ShouldReturnTrue () [0x0002f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:123
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1238" name="GroundedChangedEvent_ShouldTriggerWhenGroundedStateChanges" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.GroundedChangedEvent_ShouldTriggerWhenGroundedStateChanges" methodname="GroundedChangedEvent_ShouldTriggerWhenGroundedStateChanges" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="121478954" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002859" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  地面状态改变时应该触发事件
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests+<GroundedChangedEvent_ShouldTriggerWhenGroundedStateChanges>d__30.MoveNext () [0x000af] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:401
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1240" name="ObstacleDetectedEvent_ShouldTriggerWhenObstacleStateChanges" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.ObstacleDetectedEvent_ShouldTriggerWhenObstacleStateChanges" methodname="ObstacleDetectedEvent_ShouldTriggerWhenObstacleStateChanges" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1823109885" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002728" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  障碍物检测状态改变时应该触发事件
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests+<ObstacleDetectedEvent_ShouldTriggerWhenObstacleStateChanges>d__32.MoveNext () [0x000af] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:447
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1230" name="ObstacleDetection_CanMoveInDirection_ShouldBlockMovementWhenNearObstacle" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.ObstacleDetection_CanMoveInDirection_ShouldBlockMovementWhenNearObstacle" methodname="ObstacleDetection_CanMoveInDirection_ShouldBlockMovementWhenNearObstacle" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="731713916" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000798" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1229" name="ObstacleDetection_WhenCharacterAwayFromObstacle_ShouldReturnFalse" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.ObstacleDetection_WhenCharacterAwayFromObstacle_ShouldReturnFalse" methodname="ObstacleDetection_WhenCharacterAwayFromObstacle_ShouldReturnFalse" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1444513136" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000929" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色远离障碍物时不应该检测到障碍物
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.ObstacleDetection_WhenCharacterAwayFromObstacle_ShouldReturnFalse () [0x0002f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:254
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1228" name="ObstacleDetection_WhenCharacterNearObstacle_ShouldReturnTrue" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.ObstacleDetection_WhenCharacterNearObstacle_ShouldReturnTrue" methodname="ObstacleDetection_WhenCharacterNearObstacle_ShouldReturnTrue" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1658535123" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000500" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1232" name="PlatformDetection_WhenCharacterNotOnPlatform_ShouldReturnFalse" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.PlatformDetection_WhenCharacterNotOnPlatform_ShouldReturnFalse" methodname="PlatformDetection_WhenCharacterNotOnPlatform_ShouldReturnFalse" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1238091074" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000642" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色不在平台上时不应该检测到平台
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.PlatformDetection_WhenCharacterNotOnPlatform_ShouldReturnFalse () [0x0002f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:298
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1231" name="PlatformDetection_WhenCharacterOnPlatform_ShouldReturnTrue" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.PlatformDetection_WhenCharacterOnPlatform_ShouldReturnTrue" methodname="PlatformDetection_WhenCharacterOnPlatform_ShouldReturnTrue" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="82811873" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000583" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1236" name="SetDetectionParameters_ShouldUpdateParameters" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.SetDetectionParameters_ShouldUpdateParameters" methodname="SetDetectionParameters_ShouldUpdateParameters" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="373847869" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000998" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  更新检测参数后应该能检测到更远的地面
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.SetDetectionParameters_ShouldUpdateParameters () [0x00049] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:360
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1237" name="SetLayerMasks_ShouldUpdateLayerMasks" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.SetLayerMasks_ShouldUpdateLayerMasks" methodname="SetLayerMasks_ShouldUpdateLayerMasks" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="142512747" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000911" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1239" name="WallCollisionChangedEvent_ShouldTriggerWhenWallStateChanges" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.WallCollisionChangedEvent_ShouldTriggerWhenWallStateChanges" methodname="WallCollisionChangedEvent_ShouldTriggerWhenWallStateChanges" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="427399644" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002888" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  墙壁碰撞状态改变时应该触发事件
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests+<WallCollisionChangedEvent_ShouldTriggerWhenWallStateChanges>d__31.MoveNext () [0x000af] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:424
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1227" name="WallDetection_CanMoveInDirection_ShouldBlockMovementTowardsWall" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.WallDetection_CanMoveInDirection_ShouldBlockMovementTowardsWall" methodname="WallDetection_CanMoveInDirection_ShouldBlockMovementTowardsWall" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="408582039" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001056" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  应该能向远离墙壁方向移动
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.WallDetection_CanMoveInDirection_ShouldBlockMovementTowardsWall () [0x00060] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:224
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1226" name="WallDetection_WhenCharacterAwayFromWall_ShouldReturnFalse" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.WallDetection_WhenCharacterAwayFromWall_ShouldReturnFalse" methodname="WallDetection_WhenCharacterAwayFromWall_ShouldReturnFalse" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1347256637" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001324" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色远离墙壁时不应该检测到墙壁
  Expected: False
  But was:  True
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CollisionDetectorTests.WallDetection_WhenCharacterAwayFromWall_ShouldReturnFalse () [0x0002f] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionDetectorTests.cs:208
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1225" name="WallDetection_WhenCharacterNearWall_ShouldReturnTrue" fullname="MobileScrollingGame.Tests.CollisionDetectorTests.WallDetection_WhenCharacterNearWall_ShouldReturnTrue" methodname="WallDetection_WhenCharacterNearWall_ShouldReturnTrue" classname="MobileScrollingGame.Tests.CollisionDetectorTests" runstate="Runnable" seed="1092271318" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000630" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1243" name="CollisionIntegrationTests" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" testcasecount="9" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.018637" total="9" passed="0" failed="9" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1249" name="CharacterApproachingObstacle_ShouldDetectAndPreventMovement" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.CharacterApproachingObstacle_ShouldDetectAndPreventMovement" methodname="CharacterApproachingObstacle_ShouldDetectAndPreventMovement" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="1505973094" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003499" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1248" name="CharacterFallingOffPlatform_ShouldDetectLeavingPlatform" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.CharacterFallingOffPlatform_ShouldDetectLeavingPlatform" methodname="CharacterFallingOffPlatform_ShouldDetectLeavingPlatform" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="395137748" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001801" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1245" name="CharacterJumping_ShouldLeaveGroundAndDetectAirborne" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.CharacterJumping_ShouldLeaveGroundAndDetectAirborne" methodname="CharacterJumping_ShouldLeaveGroundAndDetectAirborne" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="1086683441" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001251" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1247" name="CharacterJumpingToPlatform_ShouldDetectPlatformLanding" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.CharacterJumpingToPlatform_ShouldDetectPlatformLanding" methodname="CharacterJumpingToPlatform_ShouldDetectPlatformLanding" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="645301210" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001152" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1244" name="CharacterLanding_ShouldTriggerGroundDetectionAndStopFalling" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.CharacterLanding_ShouldTriggerGroundDetectionAndStopFalling" methodname="CharacterLanding_ShouldTriggerGroundDetectionAndStopFalling" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="492563946" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001506" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1246" name="CharacterMovingToWall_ShouldDetectWallAndStopMovement" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.CharacterMovingToWall_ShouldDetectWallAndStopMovement" methodname="CharacterMovingToWall_ShouldDetectWallAndStopMovement" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="640102164" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001395" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1250" name="ComplexMovementScenario_ShouldHandleAllCollisionTypes" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.ComplexMovementScenario_ShouldHandleAllCollisionTypes" methodname="ComplexMovementScenario_ShouldHandleAllCollisionTypes" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="1064229407" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001194" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1252" name="EdgeCase_SimultaneousCollisions_ShouldHandleCorrectly" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.EdgeCase_SimultaneousCollisions_ShouldHandleCorrectly" methodname="EdgeCase_SimultaneousCollisions_ShouldHandleCorrectly" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="515040294" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001410" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1251" name="StressTest_RapidMovementChanges_ShouldMaintainStability" fullname="MobileScrollingGame.Tests.CollisionIntegrationTests.StressTest_RapidMovementChanges_ShouldMaintainStability" methodname="StressTest_RapidMovementChanges_ShouldMaintainStability" classname="MobileScrollingGame.Tests.CollisionIntegrationTests" runstate="Runnable" seed="618892285" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001110" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[SetUp : System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[--SetUp
  at MobileScrollingGame.Player.CharacterController.SetCharacterData (MobileScrollingGame.Player.CharacterData data) [0x00013] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Player/CharacterController.cs:380 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.CreateTestCharacter () [0x000db] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:85 
  at MobileScrollingGame.Tests.CollisionIntegrationTests.SetUp () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CollisionIntegrationTests.cs:32 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1253" name="CompilationTest" fullname="MobileScrollingGame.Tests.CompilationTest" classname="MobileScrollingGame.Tests.CompilationTest" runstate="Runnable" testcasecount="3" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.004760" total="3" passed="2" failed="1" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1255" name="CharacterAnimator_CanBeCreated" fullname="MobileScrollingGame.Tests.CompilationTest.CharacterAnimator_CanBeCreated" methodname="CharacterAnimator_CanBeCreated" classname="MobileScrollingGame.Tests.CompilationTest" runstate="Runnable" seed="1747686965" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000441" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1254" name="CharacterController_NamespaceResolution_Works" fullname="MobileScrollingGame.Tests.CompilationTest.CharacterController_NamespaceResolution_Works" methodname="CharacterController_NamespaceResolution_Works" classname="MobileScrollingGame.Tests.CompilationTest" runstate="Runnable" seed="1446277937" result="Failed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002971" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  应该能够创建Unity的CharacterController
  Expected: not null
  But was:  null
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CompilationTest.CharacterController_NamespaceResolution_Works () [0x00025] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CompilationTest.cs:26
]]></stack-trace>
              </failure>
              <output><![CDATA[Can't add component 'CharacterController' to CompilationTest because it conflicts with the existing 'Rigidbody2D' derived component!
]]></output>
            </test-case>
            <test-case id="1256" name="CharacterData_CanBeInstantiated" fullname="MobileScrollingGame.Tests.CompilationTest.CharacterData_CanBeInstantiated" methodname="CharacterData_CanBeInstantiated" classname="MobileScrollingGame.Tests.CompilationTest" runstate="Runnable" seed="847660457" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000245" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1257" name="InputSystemTests" fullname="MobileScrollingGame.Tests.InputSystemTests" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" testcasecount="19" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.024653" total="19" passed="17" failed="2" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1272" name="InputMapper_DebugMode_ShouldToggle" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_DebugMode_ShouldToggle" methodname="InputMapper_DebugMode_ShouldToggle" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1990966633" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000984" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1267" name="InputMapper_EnableDisableInput_ShouldWork" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_EnableDisableInput_ShouldWork" methodname="InputMapper_EnableDisableInput_ShouldWork" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="202657324" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000838" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1269" name="InputMapper_GetActiveInputSource_ShouldReturnCorrectSource" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_GetActiveInputSource_ShouldReturnCorrectSource" methodname="InputMapper_GetActiveInputSource_ShouldReturnCorrectSource" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="766814312" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001718" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.InvalidOperationException : You are trying to read Input using the UnityEngine.Input class, but you have switched active Input handling to Input System package in Player Settings.]]></message>
                <stack-trace><![CDATA[  at (wrapper managed-to-native) UnityEngine.Input.get_anyKey()
  at MobileScrollingGame.Input.InputMapper.GetActiveInputSource () [0x00051] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Input/InputMapper.cs:267 
  at MobileScrollingGame.Tests.InputSystemTests.InputMapper_GetActiveInputSource_ShouldReturnCorrectSource () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/InputSystemTests.cs:182 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <016d35b5449b460ca65d15b1a0aee10a>:0 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1266" name="InputMapper_InitialState_ShouldBeZero" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_InitialState_ShouldBeZero" methodname="InputMapper_InitialState_ShouldBeZero" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="905071812" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001003" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1268" name="InputMapper_ResetInputs_ShouldClearAllStates" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_ResetInputs_ShouldClearAllStates" methodname="InputMapper_ResetInputs_ShouldClearAllStates" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1921669494" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000975" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1271" name="InputMapper_SetKeyboardInputEnabled_ShouldWork" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_SetKeyboardInputEnabled_ShouldWork" methodname="InputMapper_SetKeyboardInputEnabled_ShouldWork" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="332754074" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000461" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1270" name="InputMapper_SetVirtualControlsPriority_ShouldWork" fullname="MobileScrollingGame.Tests.InputSystemTests.InputMapper_SetVirtualControlsPriority_ShouldWork" methodname="InputMapper_SetVirtualControlsPriority_ShouldWork" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1334120450" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000569" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1274" name="InputSystem_IInputHandler_Interface_ShouldBeImplemented" fullname="MobileScrollingGame.Tests.InputSystemTests.InputSystem_IInputHandler_Interface_ShouldBeImplemented" methodname="InputSystem_IInputHandler_Interface_ShouldBeImplemented" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="411271593" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001303" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1273" name="InputSystem_Integration_ShouldWorkTogether" fullname="MobileScrollingGame.Tests.InputSystemTests.InputSystem_Integration_ShouldWorkTogether" methodname="InputSystem_Integration_ShouldWorkTogether" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1410682683" result="Failed" label="Error" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.003228" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.InvalidOperationException : You are trying to read Input using the UnityEngine.Input class, but you have switched active Input handling to Input System package in Player Settings.]]></message>
                <stack-trace><![CDATA[  at (wrapper managed-to-native) UnityEngine.Input.get_anyKey()
  at MobileScrollingGame.Input.InputMapper.GetActiveInputSource () [0x00051] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Input/InputMapper.cs:267 
  at MobileScrollingGame.Tests.InputSystemTests+<InputSystem_Integration_ShouldWorkTogether>d__21.MoveNext () [0x00056] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/InputSystemTests.cs:238 
  at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44 ]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1276" name="InputSystem_Requirement1_ContinuousInputShouldWork" fullname="MobileScrollingGame.Tests.InputSystemTests.InputSystem_Requirement1_ContinuousInputShouldWork" methodname="InputSystem_Requirement1_ContinuousInputShouldWork" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1459635699" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001279" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1275" name="InputSystem_Requirement1_TouchControlsShouldWork" fullname="MobileScrollingGame.Tests.InputSystemTests.InputSystem_Requirement1_TouchControlsShouldWork" methodname="InputSystem_Requirement1_TouchControlsShouldWork" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1397523849" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001176" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1261" name="TouchInputHandler_DebugMode_ShouldToggle" fullname="MobileScrollingGame.Tests.InputSystemTests.TouchInputHandler_DebugMode_ShouldToggle" methodname="TouchInputHandler_DebugMode_ShouldToggle" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="383485623" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000593" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1259" name="TouchInputHandler_EnableDisableInput_ShouldWork" fullname="MobileScrollingGame.Tests.InputSystemTests.TouchInputHandler_EnableDisableInput_ShouldWork" methodname="TouchInputHandler_EnableDisableInput_ShouldWork" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="1475610738" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000466" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1258" name="TouchInputHandler_InitialState_ShouldBeZero" fullname="MobileScrollingGame.Tests.InputSystemTests.TouchInputHandler_InitialState_ShouldBeZero" methodname="TouchInputHandler_InitialState_ShouldBeZero" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="976022370" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000625" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1260" name="TouchInputHandler_ResetInputs_ShouldClearAllStates" fullname="MobileScrollingGame.Tests.InputSystemTests.TouchInputHandler_ResetInputs_ShouldClearAllStates" methodname="TouchInputHandler_ResetInputs_ShouldClearAllStates" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="260032885" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000598" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1265" name="VirtualControls_DebugMode_ShouldToggle" fullname="MobileScrollingGame.Tests.InputSystemTests.VirtualControls_DebugMode_ShouldToggle" methodname="VirtualControls_DebugMode_ShouldToggle" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="881262934" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000445" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1262" name="VirtualControls_InitialState_ShouldBeZero" fullname="MobileScrollingGame.Tests.InputSystemTests.VirtualControls_InitialState_ShouldBeZero" methodname="VirtualControls_InitialState_ShouldBeZero" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="2080747108" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000574" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1263" name="VirtualControls_ResetInputs_ShouldClearAllStates" fullname="MobileScrollingGame.Tests.InputSystemTests.VirtualControls_ResetInputs_ShouldClearAllStates" methodname="VirtualControls_ResetInputs_ShouldClearAllStates" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="203107420" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000864" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1264" name="VirtualControls_SetVisible_ShouldToggleVisibility" fullname="MobileScrollingGame.Tests.InputSystemTests.VirtualControls_SetVisible_ShouldToggleVisibility" methodname="VirtualControls_SetVisible_ShouldToggleVisibility" classname="MobileScrollingGame.Tests.InputSystemTests" runstate="Runnable" seed="560593416" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.001086" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1277" name="InputValidationTests" fullname="MobileScrollingGame.Tests.InputValidationTests" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" testcasecount="16" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.037040" total="16" passed="16" failed="0" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <test-case id="1291" name="ContinuousInput_BufferProcessing_ShouldMaintainState" fullname="MobileScrollingGame.Tests.InputValidationTests.ContinuousInput_BufferProcessing_ShouldMaintainState" methodname="ContinuousInput_BufferProcessing_ShouldMaintainState" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="276570398" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002344" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1289" name="ContinuousInput_TouchTracking_ShouldWork" fullname="MobileScrollingGame.Tests.InputValidationTests.ContinuousInput_TouchTracking_ShouldWork" methodname="ContinuousInput_TouchTracking_ShouldWork" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="1964212893" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000724" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1286" name="ErrorHandling_InvalidInputCount_ShouldTrack" fullname="MobileScrollingGame.Tests.InputValidationTests.ErrorHandling_InvalidInputCount_ShouldTrack" methodname="ErrorHandling_InvalidInputCount_ShouldTrack" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="1801401968" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000359" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1288" name="ErrorHandling_LastValidInputTime_ShouldTrack" fullname="MobileScrollingGame.Tests.InputValidationTests.ErrorHandling_LastValidInputTime_ShouldTrack" methodname="ErrorHandling_LastValidInputTime_ShouldTrack" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000437" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1287" name="ErrorHandling_SystemHealth_ShouldMonitor" fullname="MobileScrollingGame.Tests.InputValidationTests.ErrorHandling_SystemHealth_ShouldMonitor" methodname="ErrorHandling_SystemHealth_ShouldMonitor" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="709291030" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000321" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1284" name="InputBuffer_InitialState_ShouldBeEmpty" fullname="MobileScrollingGame.Tests.InputValidationTests.InputBuffer_InitialState_ShouldBeEmpty" methodname="InputBuffer_InitialState_ShouldBeEmpty" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="**********" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000336" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1285" name="InputBuffer_Processing_ShouldWorkOverTime" fullname="MobileScrollingGame.Tests.InputValidationTests.InputBuffer_Processing_ShouldWorkOverTime" methodname="InputBuffer_Processing_ShouldWorkOverTime" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="1205475832" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002804" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1290" name="InputRelease_StateReset_ShouldWork" fullname="MobileScrollingGame.Tests.InputValidationTests.InputRelease_StateReset_ShouldWork" methodname="InputRelease_StateReset_ShouldWork" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="165799404" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002022" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[输入状态重置
]]></output>
            </test-case>
            <test-case id="1280" name="InputValidation_ForceCleanup_ShouldWork" fullname="MobileScrollingGame.Tests.InputValidationTests.InputValidation_ForceCleanup_ShouldWork" methodname="InputValidation_ForceCleanup_ShouldWork" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="482820493" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.004323" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[强制清理输入系统完成
]]></output>
            </test-case>
            <test-case id="1278" name="InputValidation_InitialState_ShouldBeHealthy" fullname="MobileScrollingGame.Tests.InputValidationTests.InputValidation_InitialState_ShouldBeHealthy" methodname="InputValidation_InitialState_ShouldBeHealthy" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="775238930" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000728" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1281" name="InputValidation_InputBuffer_ShouldProcessOverTime" fullname="MobileScrollingGame.Tests.InputValidationTests.InputValidation_InputBuffer_ShouldProcessOverTime" methodname="InputValidation_InputBuffer_ShouldProcessOverTime" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="247327318" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002519" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1292" name="InputValidation_Integration_ShouldWorkTogether" fullname="MobileScrollingGame.Tests.InputValidationTests.InputValidation_Integration_ShouldWorkTogether" methodname="InputValidation_Integration_ShouldWorkTogether" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="801576465" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.005539" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[强制清理输入系统完成
输入状态重置
]]></output>
            </test-case>
            <test-case id="1293" name="InputValidation_PublicInterface_ShouldBeComplete" fullname="MobileScrollingGame.Tests.InputValidationTests.InputValidation_PublicInterface_ShouldBeComplete" methodname="InputValidation_PublicInterface_ShouldBeComplete" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="1456012187" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.005592" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[输入启用
输入状态重置
强制清理输入系统完成
]]></output>
            </test-case>
            <test-case id="1279" name="InputValidation_ResetInputs_ShouldClearAllValidationData" fullname="MobileScrollingGame.Tests.InputValidationTests.InputValidation_ResetInputs_ShouldClearAllValidationData" methodname="InputValidation_ResetInputs_ShouldClearAllValidationData" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="1480880961" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.002108" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[输入状态重置
]]></output>
            </test-case>
            <test-case id="1282" name="MultiTouch_ConflictResolution_ShouldHandleMultipleTouches" fullname="MobileScrollingGame.Tests.InputValidationTests.MultiTouch_ConflictResolution_ShouldHandleMultipleTouches" methodname="MultiTouch_ConflictResolution_ShouldHandleMultipleTouches" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="2063326953" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000646" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1283" name="MultiTouch_TouchTracking_ShouldTrackActiveTouches" fullname="MobileScrollingGame.Tests.InputValidationTests.MultiTouch_TouchTracking_ShouldTrackActiveTouches" methodname="MultiTouch_TouchTracking_ShouldTrackActiveTouches" classname="MobileScrollingGame.Tests.InputValidationTests" runstate="Runnable" seed="2077118263" result="Passed" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:20Z" duration="0.000397" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1294" name="PlatformCollisionHandlerTests" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" testcasecount="15" result="Failed" site="Child" start-time="2025-08-05 08:28:20Z" end-time="2025-08-05 08:28:21Z" duration="0.045576" total="15" passed="8" failed="7" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1309" name="EdgeCase_ClearIgnoredPlatforms_ShouldResetIgnoreList" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.EdgeCase_ClearIgnoredPlatforms_ShouldResetIgnoreList" methodname="EdgeCase_ClearIgnoredPlatforms_ShouldResetIgnoreList" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="1509483354" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.003065" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1308" name="EdgeCase_ForceLeavePlatform_ShouldResetState" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.EdgeCase_ForceLeavePlatform_ShouldResetState" methodname="EdgeCase_ForceLeavePlatform_ShouldResetState" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="297328177" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.003072" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  前置条件：角色应该在平台上
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<EdgeCase_ForceLeavePlatform_ShouldResetState>d__25.MoveNext () [0x0005d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:361
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1307" name="EdgeCase_MultipleDropThroughAttempts_ShouldHandleCorrectly" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.EdgeCase_MultipleDropThroughAttempts_ShouldHandleCorrectly" methodname="EdgeCase_MultipleDropThroughAttempts_ShouldHandleCorrectly" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="1301128412" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.003957" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  多次穿透尝试应该只触发一次
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<EdgeCase_MultipleDropThroughAttempts_ShouldHandleCorrectly>d__24.MoveNext () [0x0007e] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:351
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1302" name="MovingPlatform_WhenCharacterLeavesMovingPlatform_ShouldStopTrackingMovement" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.MovingPlatform_WhenCharacterLeavesMovingPlatform_ShouldStopTrackingMovement" methodname="MovingPlatform_WhenCharacterLeavesMovingPlatform_ShouldStopTrackingMovement" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="312866079" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.004234" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1301" name="MovingPlatform_WhenCharacterOnMovingPlatform_ShouldTrackPlatformMovement" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.MovingPlatform_WhenCharacterOnMovingPlatform_ShouldTrackPlatformMovement" methodname="MovingPlatform_WhenCharacterOnMovingPlatform_ShouldTrackPlatformMovement" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="990706876" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.003413" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  前置条件：角色应该在移动平台上
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<MovingPlatform_WhenCharacterOnMovingPlatform_ShouldTrackPlatformMovement>d__18.MoveNext () [0x00068] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:214
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1296" name="NormalPlatform_WhenCharacterNotOnPlatform_ShouldNotDetectPlatform" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.NormalPlatform_WhenCharacterNotOnPlatform_ShouldNotDetectPlatform" methodname="NormalPlatform_WhenCharacterNotOnPlatform_ShouldNotDetectPlatform" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="1410923487" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.002657" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1295" name="NormalPlatform_WhenCharacterOnPlatform_ShouldDetectPlatform" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.NormalPlatform_WhenCharacterOnPlatform_ShouldDetectPlatform" methodname="NormalPlatform_WhenCharacterOnPlatform_ShouldDetectPlatform" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="2147242338" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.002697" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色在普通平台上时应该检测到平台
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<NormalPlatform_WhenCharacterOnPlatform_ShouldDetectPlatform>d__12.MoveNext () [0x0005d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:118
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1299" name="OneWayPlatform_DropThrough_ShouldIgnorePlatform" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.OneWayPlatform_DropThrough_ShouldIgnorePlatform" methodname="OneWayPlatform_DropThrough_ShouldIgnorePlatform" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="1049035588" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.002809" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  前置条件：角色应该在单向平台上
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<OneWayPlatform_DropThrough_ShouldIgnorePlatform>d__16.MoveNext () [0x00068] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:177
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1300" name="OneWayPlatform_DropThroughTimeout_ShouldReEnablePlatform" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.OneWayPlatform_DropThroughTimeout_ShouldReEnablePlatform" methodname="OneWayPlatform_DropThroughTimeout_ShouldReEnablePlatform" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="139985116" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.003108" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1298" name="OneWayPlatform_WhenCharacterApproachesFromBelow_ShouldNotDetectPlatform" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.OneWayPlatform_WhenCharacterApproachesFromBelow_ShouldNotDetectPlatform" methodname="OneWayPlatform_WhenCharacterApproachesFromBelow_ShouldNotDetectPlatform" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="217072510" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.002604" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1297" name="OneWayPlatform_WhenCharacterLandsFromAbove_ShouldDetectPlatform" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.OneWayPlatform_WhenCharacterLandsFromAbove_ShouldDetectPlatform" methodname="OneWayPlatform_WhenCharacterLandsFromAbove_ShouldDetectPlatform" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="974299809" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.002519" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  角色从上方落到单向平台时应该检测到平台
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<OneWayPlatform_WhenCharacterLandsFromAbove_ShouldDetectPlatform>d__14.MoveNext () [0x0007c] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:152
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1303" name="PlatformSwitching_WhenMovingBetweenPlatforms_ShouldTriggerCorrectEvents" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.PlatformSwitching_WhenMovingBetweenPlatforms_ShouldTriggerCorrectEvents" methodname="PlatformSwitching_WhenMovingBetweenPlatforms_ShouldTriggerCorrectEvents" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="1101933101" result="Failed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.003272" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  进入平台时应该触发进入事件
  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.PlatformCollisionHandlerTests+<PlatformSwitching_WhenMovingBetweenPlatforms_ShouldTriggerCorrectEvents>d__20.MoveNext () [0x000fb] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/PlatformCollisionHandlerTests.cs:271
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1305" name="SetDropThroughSettings_ShouldUpdateDropThroughBehavior" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.SetDropThroughSettings_ShouldUpdateDropThroughBehavior" methodname="SetDropThroughSettings_ShouldUpdateDropThroughBehavior" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="291814566" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.001136" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1306" name="SetMovingPlatformSupport_ShouldUpdateMovingPlatformBehavior" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.SetMovingPlatformSupport_ShouldUpdateMovingPlatformBehavior" methodname="SetMovingPlatformSupport_ShouldUpdateMovingPlatformBehavior" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="20284927" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.000862" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1304" name="SetPlatformLayerMasks_ShouldUpdateDetectionLayers" fullname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests.SetPlatformLayerMasks_ShouldUpdateDetectionLayers" methodname="SetPlatformLayerMasks_ShouldUpdateDetectionLayers" classname="MobileScrollingGame.Tests.PlatformCollisionHandlerTests" runstate="Runnable" seed="1041741678" result="Passed" start-time="2025-08-05 08:28:21Z" end-time="2025-08-05 08:28:21Z" duration="0.000714" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
            </test-case>
          </test-suite>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>