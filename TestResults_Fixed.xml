<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="6" result="Passed" total="6" passed="6" failed="0" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.1696445">
  <test-suite type="TestSuite" id="1000" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.169645" total="6" passed="6" failed="0" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <test-suite type="Assembly" id="1310" name="MobileScrollingGame.Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/MobileScrollingGame.Tests.dll" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.158340" total="6" passed="6" failed="0" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="33518" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <test-suite type="TestSuite" id="1311" name="MobileScrollingGame" fullname="MobileScrollingGame" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.157379" total="6" passed="6" failed="0" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <test-suite type="TestSuite" id="1312" name="Tests" fullname="MobileScrollingGame.Tests" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.156932" total="6" passed="6" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-suite type="TestFixture" id="1013" name="CameraFollowerTestDiagnostics" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" testcasecount="6" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.152088" total="6" passed="6" failed="0" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <test-case id="1016" name="DiagnoseCameraBounds" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseCameraBounds" methodname="DiagnoseCameraBounds" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1259647129" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.040187" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断摄像机边界 ---
设置边界: (x:-25.00, y:-15.00, width:50.00, height:30.00)
摄像机尺寸: 5
摄像机宽高比: 1.777778
摄像机视野高度: 10
摄像机视野宽度: 17.77778
X轴限制范围: [-16.11111, 16.11111]
Y轴限制范围: [-10, 10]
边界计算有效: True
目标移动到边界外: (50.00, 0.00, 0.00)
限制后摄像机位置: (16.11, 1.00, -10.00)
X轴在边界内: True (位置: 16.11111, 范围: [-16.11111, 16.11111])
Y轴在边界内: True (位置: 1, 范围: [-10, 10])
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1019" name="DiagnoseComponentIntegrity" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseComponentIntegrity" methodname="DiagnoseComponentIntegrity" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1749559316" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.018988" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断组件完整性 ---
摄像机对象存在: True
目标对象存在: True
CameraFollower组件存在: True
Camera组件存在: True
摄像机类型: 正交
正交尺寸: 5
宽高比: 1.777778
近裁剪面: 0.3
远裁剪面: 1000
所有组件和方法检查完成
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1014" name="DiagnoseSetFollowTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseSetFollowTarget" methodname="DiagnoseSetFollowTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1259393844" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.022035" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断SetFollowTarget ---
初始摄像机位置: (0.00, 0.00, 0.00)
初始目标位置: (0.00, 0.00, 0.00)
边界限制已禁用
目标位置: (0.00, 0.00, 0.00)
设置目标后摄像机位置: (0.00, 1.00, -10.00)
期望摄像机位置: (0.00, 1.00, -10.00)
位置差异: 0
摄像机组件存在: True
摄像机正交: True
摄像机尺寸: 5
摄像机宽高比: 1.777778
X轴匹配: True (差异: 0)
Y轴匹配: True (差异: 0)
Z轴匹配: True (差异: 0)
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1018" name="DiagnoseShakeEffect" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseShakeEffect" methodname="DiagnoseShakeEffect" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1966988878" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.006078" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断震动效果 ---
震动前位置: (0.00, 0.00, -10.00)
震动已触发
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1017" name="DiagnoseSmoothMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseSmoothMovement" methodname="DiagnoseSmoothMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="969153169" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.035111" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断平滑移动 ---
初始摄像机位置: (0.00, 0.00, -10.00)
目标移动到: (5.00, 0.00, 0.00)
帧 0: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 1: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 2: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 3: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 4: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 5: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 6: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 7: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 8: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
帧 9: 摄像机位置 (1.11, 0.00, -10.00), 移动距离 1.111111
最终摄像机位置: (1.11, 0.00, -10.00)
总移动距离: 1.111111
=== CameraFollower诊断测试结束 ===
]]></output>
            </test-case>
            <test-case id="1015" name="DiagnoseUpdateCameraPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics.DiagnoseUpdateCameraPosition" methodname="DiagnoseUpdateCameraPosition" classname="MobileScrollingGame.Tests.CameraFollowerTestDiagnostics" runstate="Runnable" seed="1512157035" result="Passed" start-time="2025-08-05 08:33:11Z" end-time="2025-08-05 08:33:11Z" duration="0.010291" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[=== CameraFollower诊断测试开始 ===
--- 诊断UpdateCameraPosition ---
设置目标后初始摄像机位置: (0.00, 0.00, -10.00)
目标移动到: (5.00, 0.00, 0.00)
更新后摄像机位置: (1.11, 0.00, -10.00)
摄像机X轴移动: 1.111111
摄像机向右移动: True
=== CameraFollower诊断测试结束 ===
Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
            </test-case>
          </test-suite>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>