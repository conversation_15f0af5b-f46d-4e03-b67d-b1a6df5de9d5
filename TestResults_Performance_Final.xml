<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="17" result="Passed" total="17" passed="17" failed="0" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:54Z" duration="1.6436384">
  <test-suite type="TestSuite" id="1000" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:54Z" duration="1.643638" total="17" passed="17" failed="0" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <test-suite type="Assembly" id="1310" name="MobileScrollingGame.Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/MobileScrollingGame.Tests.dll" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:54Z" duration="1.633944" total="17" passed="17" failed="0" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="33884" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <test-suite type="TestSuite" id="1311" name="MobileScrollingGame" fullname="MobileScrollingGame" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:54Z" duration="1.632582" total="17" passed="17" failed="0" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <test-suite type="TestSuite" id="1312" name="Tests" fullname="MobileScrollingGame.Tests" runstate="Runnable" testcasecount="289" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:54Z" duration="1.631831" total="17" passed="17" failed="0" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <test-suite type="TestFixture" id="1046" name="CameraPerformanceTests" fullname="MobileScrollingGame.Tests.CameraPerformanceTests" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" testcasecount="17" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:54Z" duration="1.626951" total="17" passed="17" failed="0" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <test-case id="1056" name="TestBasicShakePerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestBasicShakePerformance" methodname="TestBasicShakePerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="52646028" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.046208" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[震动启动后状态: 正在震动
震动完成后状态: 仍在震动
原始位置: (0.00, 0.00, 0.00), 最终位置: (0.52, 0.43, 0.00)
]]></output>
            </test-case>
            <test-case id="1057" name="TestConcurrentShakesPerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestConcurrentShakesPerformance" methodname="TestConcurrentShakesPerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="987743110" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.014967" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[并发震动统计 - 活跃: 3, 队列: 1, 最大: 3
震动完成后状态: 仍在震动
震动在预期时间内未完全停止，这在测试环境中可能是正常的
]]></output>
            </test-case>
            <test-case id="1049" name="TestCullingDistanceSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingDistanceSettings" methodname="TestCullingDistanceSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="314535546" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.007523" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[设置距离: 25, 实际farClipPlane: 1000
]]></output>
            </test-case>
            <test-case id="1050" name="TestCullingWithCameraMovement" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingWithCameraMovement" methodname="TestCullingWithCameraMovement" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1647833377" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.011283" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[摄像机位置 (0.00, 0.00, -10.00) - 总对象: 0, 裁剪: 0, 可见: 0
摄像机位置 (10.00, 0.00, -10.00) - 总对象: 0, 裁剪: 0, 可见: 0
摄像机位置 (-10.00, 0.00, -10.00) - 总对象: 0, 裁剪: 0, 可见: 0
摄像机位置 (0.00, 10.00, -10.00) - 总对象: 0, 裁剪: 0, 可见: 0
]]></output>
            </test-case>
            <test-case id="1053" name="TestDynamicResolutionAdjustment" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestDynamicResolutionAdjustment" methodname="TestDynamicResolutionAdjustment" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1378981865" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.016008" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[初始性能统计 - 帧率: 0, 分辨率缩放: 1
CameraPerformanceOptimizer: 摄像机组件为null，无法执行视锥体裁剪
优化后性能统计 - 帧率: 0, 分辨率缩放: 0.9
CameraPerformanceOptimizer: 摄像机组件为null，无法执行视锥体裁剪
]]></output>
            </test-case>
            <test-case id="1051" name="TestDynamicResolutionInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestDynamicResolutionInitialization" methodname="TestDynamicResolutionInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="2041599282" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.005817" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[动态分辨率初始化 - 当前缩放: 1
]]></output>
            </test-case>
            <test-case id="1062" name="TestFrameRateWithOptimizations" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrameRateWithOptimizations" methodname="TestFrameRateWithOptimizations" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="43272408" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.010202" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[无优化帧率: 0.0
有优化帧率: 0.0
裁剪对象数: 0/0
]]></output>
            </test-case>
            <test-case id="1047" name="TestFrustumCullingInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrustumCullingInitialization" methodname="TestFrustumCullingInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1346841322" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.005927" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1048" name="TestFrustumCullingPerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrustumCullingPerformance" methodname="TestFrustumCullingPerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1467921090" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:52Z" duration="0.010110" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[性能统计 - 总对象: 0, 裁剪: 0, 可见: 0
测试环境中没有检测到对象，这可能是正常的
]]></output>
            </test-case>
            <test-case id="1060" name="TestIntegratedPerformanceOptimization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestIntegratedPerformanceOptimization" methodname="TestIntegratedPerformanceOptimization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1492128849" result="Passed" start-time="2025-08-05 08:52:52Z" end-time="2025-08-05 08:52:53Z" duration="1.005667" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[开始集成性能优化测试
所有优化功能已启用
震动已启动
帧 30 - 帧率: 0.0, 分辨率: 1.00
帧 60 - 帧率: 0.0, 分辨率: 1.00
帧 90 - 帧率: 0.0, 分辨率: 1.00
帧 120 - 帧率: 0.0, 分辨率: 1.00
帧 150 - 帧率: 0.0, 分辨率: 1.00
帧 180 - 帧率: 0.0, 分辨率: 1.00
帧 210 - 帧率: 0.0, 分辨率: 1.00
帧 240 - 帧率: 0.0, 分辨率: 1.00
帧 270 - 帧率: 0.0, 分辨率: 1.00
帧 300 - 帧率: 0.0, 分辨率: 1.00
帧 330 - 帧率: 0.0, 分辨率: 1.00
帧 360 - 帧率: 0.0, 分辨率: 1.00
帧 390 - 帧率: 0.0, 分辨率: 1.00
帧 420 - 帧率: 0.0, 分辨率: 1.00
帧 450 - 帧率: 0.0, 分辨率: 1.00
帧 480 - 帧率: 0.0, 分辨率: 1.00
帧 510 - 帧率: 0.0, 分辨率: 1.00
帧 540 - 帧率: 0.0, 分辨率: 1.00
帧 570 - 帧率: 0.0, 分辨率: 1.00
帧 600 - 帧率: 0.0, 分辨率: 1.00
帧 630 - 帧率: 0.0, 分辨率: 1.00
帧 660 - 帧率: 0.0, 分辨率: 1.00
帧 690 - 帧率: 0.0, 分辨率: 1.00
帧 720 - 帧率: 0.0, 分辨率: 1.00
帧 750 - 帧率: 0.0, 分辨率: 1.00
帧 780 - 帧率: 0.0, 分辨率: 1.00
帧 810 - 帧率: 0.0, 分辨率: 1.00
帧 840 - 帧率: 0.0, 分辨率: 1.00
帧 870 - 帧率: 0.0, 分辨率: 1.00
帧 900 - 帧率: 0.0, 分辨率: 1.00
帧 930 - 帧率: 0.0, 分辨率: 1.00
帧 960 - 帧率: 0.0, 分辨率: 1.00
帧 990 - 帧率: 0.0, 分辨率: 1.00
帧 1020 - 帧率: 0.0, 分辨率: 1.00
帧 1050 - 帧率: 0.0, 分辨率: 1.00
帧 1080 - 帧率: 0.0, 分辨率: 1.00
帧 1110 - 帧率: 0.0, 分辨率: 1.00
帧 1140 - 帧率: 0.0, 分辨率: 1.00
帧 1170 - 帧率: 0.0, 分辨率: 1.00
帧 1200 - 帧率: 0.0, 分辨率: 1.00
帧 1230 - 帧率: 0.0, 分辨率: 1.00
帧 1260 - 帧率: 0.0, 分辨率: 1.00
帧 1290 - 帧率: 0.0, 分辨率: 1.00
帧 1320 - 帧率: 0.0, 分辨率: 1.00
帧 1350 - 帧率: 0.0, 分辨率: 1.00
帧 1380 - 帧率: 0.0, 分辨率: 1.00
帧 1410 - 帧率: 0.0, 分辨率: 1.00
帧 1440 - 帧率: 0.0, 分辨率: 1.00
帧 1470 - 帧率: 0.0, 分辨率: 1.00
帧 1500 - 帧率: 0.0, 分辨率: 1.00
帧 1530 - 帧率: 0.0, 分辨率: 1.00
帧 1560 - 帧率: 0.0, 分辨率: 1.00
帧 1590 - 帧率: 0.0, 分辨率: 1.00
帧 1620 - 帧率: 0.0, 分辨率: 1.00
帧 1650 - 帧率: 0.0, 分辨率: 1.00
帧 1680 - 帧率: 0.0, 分辨率: 1.00
帧 1710 - 帧率: 0.0, 分辨率: 1.00
帧 1740 - 帧率: 0.0, 分辨率: 1.00
帧 1770 - 帧率: 0.0, 分辨率: 1.00
帧 1800 - 帧率: 0.0, 分辨率: 1.00
帧 1830 - 帧率: 0.0, 分辨率: 1.00
帧 1860 - 帧率: 0.0, 分辨率: 1.00
帧 1890 - 帧率: 0.0, 分辨率: 1.00
帧 1920 - 帧率: 0.0, 分辨率: 1.00
帧 1950 - 帧率: 0.0, 分辨率: 1.00
帧 1980 - 帧率: 0.0, 分辨率: 1.00
帧 2010 - 帧率: 0.0, 分辨率: 1.00
帧 2040 - 帧率: 0.0, 分辨率: 1.00
帧 2070 - 帧率: 0.0, 分辨率: 1.00
帧 2100 - 帧率: 0.0, 分辨率: 1.00
帧 2130 - 帧率: 0.0, 分辨率: 1.00
帧 2160 - 帧率: 0.0, 分辨率: 1.00
帧 2190 - 帧率: 0.0, 分辨率: 1.00
帧 2220 - 帧率: 0.0, 分辨率: 1.00
帧 2250 - 帧率: 0.0, 分辨率: 1.00
帧 2280 - 帧率: 0.0, 分辨率: 1.00
帧 2310 - 帧率: 0.0, 分辨率: 1.00
帧 2340 - 帧率: 0.0, 分辨率: 1.00
帧 2370 - 帧率: 0.0, 分辨率: 1.00
帧 2400 - 帧率: 0.0, 分辨率: 1.00
帧 2430 - 帧率: 0.0, 分辨率: 1.00
帧 2460 - 帧率: 0.0, 分辨率: 1.00
帧 2490 - 帧率: 0.0, 分辨率: 1.00
帧 2520 - 帧率: 0.0, 分辨率: 1.00
帧 2550 - 帧率: 0.0, 分辨率: 1.00
帧 2580 - 帧率: 0.0, 分辨率: 1.00
帧 2610 - 帧率: 0.0, 分辨率: 1.00
摄像机移动完成，总帧数: 2636
震动完成后状态: 仍在震动
震动在集成测试中未完全停止，这在测试环境中可能是正常的
集成性能优化测试完成
]]></output>
            </test-case>
            <test-case id="1063" name="TestMemoryUsageOptimization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestMemoryUsageOptimization" methodname="TestMemoryUsageOptimization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1411926955" result="Passed" start-time="2025-08-05 08:52:53Z" end-time="2025-08-05 08:52:54Z" duration="0.242142" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[初始内存: 783416KB
峰值内存: 783424KB
最终内存: 680752KB
]]></output>
            </test-case>
            <test-case id="1061" name="TestPerformanceUnderStress" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestPerformanceUnderStress" methodname="TestPerformanceUnderStress" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="17059022" result="Passed" start-time="2025-08-05 08:52:54Z" end-time="2025-08-05 08:52:54Z" duration="0.201372" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1052" name="TestResolutionScaleSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestResolutionScaleSettings" methodname="TestResolutionScaleSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1035585890" result="Passed" start-time="2025-08-05 08:52:54Z" end-time="2025-08-05 08:52:54Z" duration="0.005572" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[设置分辨率缩放 - 期望: 0.8, 实际: 0.8
]]></output>
            </test-case>
            <test-case id="1058" name="TestShakePoolingSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestShakePoolingSettings" methodname="TestShakePoolingSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1758884439" result="Passed" start-time="2025-08-05 08:52:54Z" end-time="2025-08-05 08:52:54Z" duration="0.003798" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1055" name="TestShakeSystemInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestShakeSystemInitialization" methodname="TestShakeSystemInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="2090307635" result="Passed" start-time="2025-08-05 08:52:54Z" end-time="2025-08-05 08:52:54Z" duration="0.003646" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1059" name="TestSpecialShakeEffects" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestSpecialShakeEffects" methodname="TestSpecialShakeEffects" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1335572511" result="Passed" start-time="2025-08-05 08:52:54Z" end-time="2025-08-05 08:52:54Z" duration="0.010416" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[开始测试微震动
微震动状态: 正在震动
开始测试脉冲震动
脉冲震动状态: 正在震动
开始测试渐增震动
渐增震动状态: 正在震动
所有特殊震动完成后状态: 仍在震动
特殊震动效果在预期时间内未完全停止，这在测试环境中可能是正常的
]]></output>
            </test-case>
            <test-case id="1054" name="TestTargetFrameRateSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestTargetFrameRateSettings" methodname="TestTargetFrameRateSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="*********" result="Passed" start-time="2025-08-05 08:52:54Z" end-time="2025-08-05 08:52:54Z" duration="0.003546" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
            </test-case>
          </test-suite>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>