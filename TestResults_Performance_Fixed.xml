<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="17" result="Failed(Child)" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:38Z" duration="1.4848527">
  <test-suite type="TestSuite" id="1000" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:38Z" duration="1.484853" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1310" name="MobileScrollingGame.Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/MobileScrollingGame.Tests.dll" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:38Z" duration="1.474082" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="33771" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1311" name="MobileScrollingGame" fullname="MobileScrollingGame" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:38Z" duration="1.472172" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestSuite" id="1312" name="Tests" fullname="MobileScrollingGame.Tests" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:38Z" duration="1.471214" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-suite type="TestFixture" id="1046" name="CameraPerformanceTests" fullname="MobileScrollingGame.Tests.CameraPerformanceTests" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" testcasecount="17" result="Failed" site="Child" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:38Z" duration="1.465940" total="17" passed="14" failed="3" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1056" name="TestBasicShakePerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestBasicShakePerformance" methodname="TestBasicShakePerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1847305913" result="Passed" start-time="2025-08-05 08:50:36Z" end-time="2025-08-05 08:50:37Z" duration="0.050047" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[震动启动后状态: 正在震动
震动完成后状态: 仍在震动
原始位置: (0.00, 0.00, 0.00), 最终位置: (-0.23, -0.23, 0.00)
]]></output>
            </test-case>
            <test-case id="1057" name="TestConcurrentShakesPerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestConcurrentShakesPerformance" methodname="TestConcurrentShakesPerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1073832593" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.015587" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[并发震动统计 - 活跃: 3, 队列: 1, 最大: 3
震动完成后状态: 仍在震动
震动在预期时间内未完全停止，这在测试环境中可能是正常的
]]></output>
            </test-case>
            <test-case id="1049" name="TestCullingDistanceSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingDistanceSettings" methodname="TestCullingDistanceSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="226021107" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.010011" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[设置距离: 25, 实际farClipPlane: 1000
]]></output>
            </test-case>
            <test-case id="1050" name="TestCullingWithCameraMovement" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestCullingWithCameraMovement" methodname="TestCullingWithCameraMovement" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1975147933" result="Failed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.013891" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestCullingWithCameraMovement>d__11.MoveNext () [0x000f5] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:179
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1053" name="TestDynamicResolutionAdjustment" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestDynamicResolutionAdjustment" methodname="TestDynamicResolutionAdjustment" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1231431305" result="Failed" label="Error" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.012168" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[System.NullReferenceException : Object reference not set to an instance of an object]]></message>
                <stack-trace><![CDATA[  at UnityEngine.GeometryUtility.CalculateFrustumPlanes (UnityEngine.Camera camera, UnityEngine.Plane[] planes) [0x00001] in /Users/<USER>/build/output/unity/unity/Runtime/Export/Geometry/GeometryUtility.cs:23 
  at MobileScrollingGame.Camera.CameraPerformanceOptimizer.PerformFrustumCulling () [0x00000] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Camera/CameraPerformanceOptimizer.cs:157 
  at MobileScrollingGame.Camera.CameraPerformanceOptimizer.ForceOptimization () [0x00008] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Camera/CameraPerformanceOptimizer.cs:373 
  at MobileScrollingGame.Tests.CameraPerformanceTests+<TestDynamicResolutionAdjustment>d__14.MoveNext () [0x000e1] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:252 
  at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44 ]]></stack-trace>
              </failure>
              <output><![CDATA[初始性能统计 - 帧率: 0, 分辨率缩放: 1
]]></output>
            </test-case>
            <test-case id="1051" name="TestDynamicResolutionInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestDynamicResolutionInitialization" methodname="TestDynamicResolutionInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="828435222" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.006000" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[动态分辨率初始化 - 当前缩放: 1
]]></output>
            </test-case>
            <test-case id="1062" name="TestFrameRateWithOptimizations" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrameRateWithOptimizations" methodname="TestFrameRateWithOptimizations" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="858888905" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.010495" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[无优化帧率: 0.0
有优化帧率: 0.0
裁剪对象数: 0/0
]]></output>
            </test-case>
            <test-case id="1047" name="TestFrustumCullingInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrustumCullingInitialization" methodname="TestFrustumCullingInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1178287180" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.006681" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1048" name="TestFrustumCullingPerformance" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestFrustumCullingPerformance" methodname="TestFrustumCullingPerformance" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1585428096" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:37Z" duration="0.009056" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[性能统计 - 总对象: 0, 裁剪: 0, 可见: 0
测试环境中没有检测到对象，这可能是正常的
]]></output>
            </test-case>
            <test-case id="1060" name="TestIntegratedPerformanceOptimization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestIntegratedPerformanceOptimization" methodname="TestIntegratedPerformanceOptimization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="578593088" result="Passed" start-time="2025-08-05 08:50:37Z" end-time="2025-08-05 08:50:38Z" duration="1.006583" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[开始集成性能优化测试
所有优化功能已启用
震动已启动
帧 30 - 帧率: 0.0, 分辨率: 1.00
帧 60 - 帧率: 0.0, 分辨率: 1.00
帧 90 - 帧率: 0.0, 分辨率: 1.00
帧 120 - 帧率: 0.0, 分辨率: 1.00
帧 150 - 帧率: 0.0, 分辨率: 1.00
帧 180 - 帧率: 0.0, 分辨率: 1.00
帧 210 - 帧率: 0.0, 分辨率: 1.00
帧 240 - 帧率: 0.0, 分辨率: 1.00
帧 270 - 帧率: 0.0, 分辨率: 1.00
帧 300 - 帧率: 0.0, 分辨率: 1.00
帧 330 - 帧率: 0.0, 分辨率: 1.00
帧 360 - 帧率: 0.0, 分辨率: 1.00
帧 390 - 帧率: 0.0, 分辨率: 1.00
帧 420 - 帧率: 0.0, 分辨率: 1.00
帧 450 - 帧率: 0.0, 分辨率: 1.00
帧 480 - 帧率: 0.0, 分辨率: 1.00
帧 510 - 帧率: 0.0, 分辨率: 1.00
帧 540 - 帧率: 0.0, 分辨率: 1.00
帧 570 - 帧率: 0.0, 分辨率: 1.00
帧 600 - 帧率: 0.0, 分辨率: 1.00
帧 630 - 帧率: 0.0, 分辨率: 1.00
帧 660 - 帧率: 0.0, 分辨率: 1.00
帧 690 - 帧率: 0.0, 分辨率: 1.00
帧 720 - 帧率: 0.0, 分辨率: 1.00
帧 750 - 帧率: 0.0, 分辨率: 1.00
帧 780 - 帧率: 0.0, 分辨率: 1.00
帧 810 - 帧率: 0.0, 分辨率: 1.00
帧 840 - 帧率: 0.0, 分辨率: 1.00
帧 870 - 帧率: 0.0, 分辨率: 1.00
帧 900 - 帧率: 0.0, 分辨率: 1.00
帧 930 - 帧率: 0.0, 分辨率: 1.00
帧 960 - 帧率: 0.0, 分辨率: 1.00
帧 990 - 帧率: 0.0, 分辨率: 1.00
帧 1020 - 帧率: 0.0, 分辨率: 1.00
帧 1050 - 帧率: 0.0, 分辨率: 1.00
帧 1080 - 帧率: 0.0, 分辨率: 1.00
帧 1110 - 帧率: 0.0, 分辨率: 1.00
帧 1140 - 帧率: 0.0, 分辨率: 1.00
帧 1170 - 帧率: 0.0, 分辨率: 1.00
帧 1200 - 帧率: 0.0, 分辨率: 1.00
帧 1230 - 帧率: 0.0, 分辨率: 1.00
帧 1260 - 帧率: 0.0, 分辨率: 1.00
帧 1290 - 帧率: 0.0, 分辨率: 1.00
帧 1320 - 帧率: 0.0, 分辨率: 1.00
帧 1350 - 帧率: 0.0, 分辨率: 1.00
帧 1380 - 帧率: 0.0, 分辨率: 1.00
帧 1410 - 帧率: 0.0, 分辨率: 1.00
帧 1440 - 帧率: 0.0, 分辨率: 1.00
帧 1470 - 帧率: 0.0, 分辨率: 1.00
帧 1500 - 帧率: 0.0, 分辨率: 1.00
帧 1530 - 帧率: 0.0, 分辨率: 1.00
帧 1560 - 帧率: 0.0, 分辨率: 1.00
帧 1590 - 帧率: 0.0, 分辨率: 1.00
帧 1620 - 帧率: 0.0, 分辨率: 1.00
帧 1650 - 帧率: 0.0, 分辨率: 1.00
帧 1680 - 帧率: 0.0, 分辨率: 1.00
帧 1710 - 帧率: 0.0, 分辨率: 1.00
帧 1740 - 帧率: 0.0, 分辨率: 1.00
帧 1770 - 帧率: 0.0, 分辨率: 1.00
帧 1800 - 帧率: 0.0, 分辨率: 1.00
帧 1830 - 帧率: 0.0, 分辨率: 1.00
帧 1860 - 帧率: 0.0, 分辨率: 1.00
帧 1890 - 帧率: 0.0, 分辨率: 1.00
帧 1920 - 帧率: 0.0, 分辨率: 1.00
帧 1950 - 帧率: 0.0, 分辨率: 1.00
帧 1980 - 帧率: 0.0, 分辨率: 1.00
帧 2010 - 帧率: 0.0, 分辨率: 1.00
帧 2040 - 帧率: 0.0, 分辨率: 1.00
帧 2070 - 帧率: 0.0, 分辨率: 1.00
帧 2100 - 帧率: 0.0, 分辨率: 1.00
帧 2130 - 帧率: 0.0, 分辨率: 1.00
帧 2160 - 帧率: 0.0, 分辨率: 1.00
帧 2190 - 帧率: 0.0, 分辨率: 1.00
帧 2220 - 帧率: 0.0, 分辨率: 1.00
帧 2250 - 帧率: 0.0, 分辨率: 1.00
帧 2280 - 帧率: 0.0, 分辨率: 1.00
摄像机移动完成，总帧数: 2288
震动完成后状态: 仍在震动
震动在集成测试中未完全停止，这在测试环境中可能是正常的
集成性能优化测试完成
]]></output>
            </test-case>
            <test-case id="1063" name="TestMemoryUsageOptimization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestMemoryUsageOptimization" methodname="TestMemoryUsageOptimization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1801983243" result="Passed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.258988" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[初始内存: 787004KB
峰值内存: 787008KB
最终内存: 703320KB
]]></output>
            </test-case>
            <test-case id="1061" name="TestPerformanceUnderStress" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestPerformanceUnderStress" methodname="TestPerformanceUnderStress" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="2072809666" result="Failed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.011022" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  Expected: True
  But was:  False
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraPerformanceTests+<TestPerformanceUnderStress>d__22.MoveNext () [0x00135] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraPerformanceTests.cs:546
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1052" name="TestResolutionScaleSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestResolutionScaleSettings" methodname="TestResolutionScaleSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="44378739" result="Passed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.007367" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[设置分辨率缩放 - 期望: 0.8, 实际: 0.8
]]></output>
            </test-case>
            <test-case id="1058" name="TestShakePoolingSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestShakePoolingSettings" methodname="TestShakePoolingSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1853216975" result="Passed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.004736" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1055" name="TestShakeSystemInitialization" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestShakeSystemInitialization" methodname="TestShakeSystemInitialization" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="807409697" result="Passed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.004642" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1059" name="TestSpecialShakeEffects" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestSpecialShakeEffects" methodname="TestSpecialShakeEffects" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1844154929" result="Passed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.010563" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[开始测试微震动
微震动状态: 正在震动
开始测试脉冲震动
脉冲震动状态: 正在震动
开始测试渐增震动
渐增震动状态: 正在震动
所有特殊震动完成后状态: 仍在震动
特殊震动效果在预期时间内未完全停止，这在测试环境中可能是正常的
]]></output>
            </test-case>
            <test-case id="1054" name="TestTargetFrameRateSettings" fullname="MobileScrollingGame.Tests.CameraPerformanceTests.TestTargetFrameRateSettings" methodname="TestTargetFrameRateSettings" classname="MobileScrollingGame.Tests.CameraPerformanceTests" runstate="Runnable" seed="1993006636" result="Passed" start-time="2025-08-05 08:50:38Z" end-time="2025-08-05 08:50:38Z" duration="0.005529" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
            </test-case>
          </test-suite>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>